from app import db
from datetime import datetime

class Recipe(db.Model):
    __tablename__ = 'recipes'
    __table_args__ = {'extend_existing': True}
    
    id = db.Column(db.Integer, primary_key=True)
    product_id = db.Column(db.Integer, db.<PERSON>ey('products.id'), nullable=False)
    description = db.Column(db.Text)
    preparation_time = db.Column(db.Integer, default=0)  # en minutes
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relations
    product = db.relationship('Product', back_populates='recipe', uselist=False)
    items = db.relationship('RecipeItem', back_populates='recipe', lazy='joined', cascade='all, delete-orphan')
    
    def calculate_cost(self):
        """Calcule le coût total de la recette basé sur les ingrédients"""
        return sum(item.calculate_cost() for item in self.items)
    
    def get_possible_quantity(self):
        """Calcule la quantité maximale possible à produire basée sur les stocks disponibles"""
        if not self.items:
            return 0
        
        possible_quantities = []
        for item in self.items:
            if item.quantity <= 0:
                return 0
            possible = item.ingredient.stock_quantity / item.quantity
            possible_quantities.append(int(possible))
        
        return min(possible_quantities) if possible_quantities else 0
    
    def get_ingredients_needed(self):
        """Retourne la liste des ingrédients nécessaires avec leurs quantités"""
        needed = []
        for item in self.items:
            needed.append({
                'ingredient': item.ingredient,
                'quantity_needed': item.quantity,
                'available': item.ingredient.stock_quantity,
                'unit': item.ingredient.unit
            })
        return needed

class RecipeItem(db.Model):
    __tablename__ = 'recipe_items'
    __table_args__ = {'extend_existing': True}
    
    id = db.Column(db.Integer, primary_key=True)
    recipe_id = db.Column(db.Integer, db.ForeignKey('recipes.id'), nullable=False)
    ingredient_id = db.Column(db.Integer, db.ForeignKey('ingredients.id'), nullable=False)
    quantity = db.Column(db.Float, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relations
    ingredient = db.relationship('Ingredient', back_populates='recipe_items')
    recipe = db.relationship('Recipe', back_populates='items')
    
    def calculate_cost(self):
        """Calcule le coût de cet item basé sur la quantité et le coût moyen de l'ingrédient"""
        return self.quantity * self.ingredient.get_current_cost()