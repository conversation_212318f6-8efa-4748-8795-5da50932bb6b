<!doctype html>
<html>
  <head>
    <title>CodeMirror: Properties files mode</title>
    <link rel="stylesheet" href="../../lib/codemirror.css">
    <script src="../../lib/codemirror.js"></script>
    <script src="properties.js"></script>
    <style>.CodeMirror {border-top: 1px solid #ddd; border-bottom: 1px solid #ddd;}</style>
    <link rel="stylesheet" href="../../doc/docs.css">
  </head>
  <body>
    <h1>CodeMirror: Properties files mode</h1>
    <form><textarea id="code" name="code">
# This is a properties file
a.key = A value
another.key = http://example.com
! Exclamation mark as comment
but.not=Within ! A value # indeed
   # Spaces at the beginning of a line
   spaces.before.key=value
backslash=Used for multi\
          line entries,\
          that's convenient.
# Unicode sequences
unicode.key=This is \u0020 Unicode
no.multiline=here
# Colons
colons : can be used too
# Spaces
spaces\ in\ keys=Not very common...
</textarea></form>
    <script>
      var editor = CodeMirror.fromTextArea(document.getElementById("code"), {});
    </script>

    <p><strong>MIME types defined:</strong> <code>text/x-properties</code>,
    <code>text/x-ini</code>.</p>

  </body>
</html>
