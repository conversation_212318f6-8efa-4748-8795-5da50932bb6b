<table class="flDebugTablesorter">
  <thead>
    <tr>
      <th>&nbsp;(ms)</th>
      <th>Action</th>
      <th>Context</th>
      <th>Query</th>
    </tr>
  </thead>
  <tbody>
    {% for query in queries %}
      <tr class="{{ loop.cycle('flDebugOdd', 'flDebugEven') }}">
        <td>{{ '%.4f'|format(query.duration * 1000) }}</td>
        <td>
        {% if query.signed_query %}
          <a class="flDebugRemoteCall" href="{{ url_for('debugtoolbar.sql_select', explain=False, query=query.signed_query, duration=query.duration )}}">SELECT</a><br />
          <a class="flDebugRemoteCall" href="{{ url_for('debugtoolbar.sql_select', explain=True, query=query.signed_query, duration=query.duration )}}">EXPLAIN</a><br />
        {% endif %}
        </td>
        <td title="{{ query.location_long }}">
                    {{ query.location }}
        </td>
        <td class="flDebugSyntax">
          <div class="flDebugSqlWrap">
            <div class="flDebugSql">{{ query.sql }}</div>
          </div>
        </td>
      </tr>
    {% endfor %}
  </tbody>
</table>
