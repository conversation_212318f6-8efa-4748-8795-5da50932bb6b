# Italian translations for WTForms.
# Copyright (C) 2020 WTForms Team
# This file is distributed under the same license as the WTForms project.
# <AUTHOR> <EMAIL>, 2020.
#
msgid ""
msgstr ""
"Project-Id-Version: WTForms 2.0dev\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2024-01-11 08:20+0100\n"
"PO-Revision-Date: 2017-03-01 11:53+0100\n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1)\n"
"Generated-By: Babel 2.8.0\n"

#: src/wtforms/validators.py:86
#, python-format
msgid "Invalid field name '%s'."
msgstr "Nome del campo non valido '%s'."

#: src/wtforms/validators.py:99
#, python-format
msgid "Field must be equal to %(other_name)s."
msgstr "Il valore deve essere uguale a %(other_name)s."

#: src/wtforms/validators.py:145
#, python-format
msgid "Field must be at least %(min)d character long."
msgid_plural "Field must be at least %(min)d characters long."
msgstr[0] "Il valore deve essere lungo almeno %(min)d carattere."
msgstr[1] "Il valore deve essere lungo almeno %(min)d caratteri."

#: src/wtforms/validators.py:151
#, python-format
msgid "Field cannot be longer than %(max)d character."
msgid_plural "Field cannot be longer than %(max)d characters."
msgstr[0] "Il valore non può essere più lungo di %(max)d carattere."
msgstr[1] "Il valore non può essere più lungo di %(max)d caratteri."

#: src/wtforms/validators.py:157
#, python-format
msgid "Field must be exactly %(max)d character long."
msgid_plural "Field must be exactly %(max)d characters long."
msgstr[0] ""
msgstr[1] ""

#: src/wtforms/validators.py:163
#, python-format
msgid "Field must be between %(min)d and %(max)d characters long."
msgstr ""
"La lunghezza del valore deve essere compresa tra %(min)d e %(max)d caratteri."

#: src/wtforms/validators.py:216
#, python-format
msgid "Number must be at least %(min)s."
msgstr "Il numero deve essere maggiore o uguale a %(min)s."

#: src/wtforms/validators.py:219
#, python-format
msgid "Number must be at most %(max)s."
msgstr "Il numero deve essere minore o uguale a %(max)s."

#: src/wtforms/validators.py:222
#, python-format
msgid "Number must be between %(min)s and %(max)s."
msgstr "Il numero deve essere compreso tra %(min)s e %(max)s."

#: src/wtforms/validators.py:293 src/wtforms/validators.py:323
msgid "This field is required."
msgstr "Questo campo è obbligatorio."

#: src/wtforms/validators.py:358
msgid "Invalid input."
msgstr "Valore non valido."

#: src/wtforms/validators.py:422
msgid "Invalid email address."
msgstr "Indirizzo e-mail non valido."

#: src/wtforms/validators.py:460
msgid "Invalid IP address."
msgstr "Indirizzo IP non valido."

#: src/wtforms/validators.py:503
msgid "Invalid Mac address."
msgstr "Indirizzo Mac non valido."

#: src/wtforms/validators.py:540
msgid "Invalid URL."
msgstr "URL non valido."

#: src/wtforms/validators.py:561
msgid "Invalid UUID."
msgstr "UUID non valido."

#: src/wtforms/validators.py:594
#, python-format
msgid "Invalid value, must be one of: %(values)s."
msgstr "Valore non valido, deve essere uno tra: %(values)s."

#: src/wtforms/validators.py:629
#, python-format
msgid "Invalid value, can't be any of: %(values)s."
msgstr "Valore non valido, non può essere nessuno tra: %(values)s."

#: src/wtforms/validators.py:698
#, fuzzy
#| msgid "This field is required."
msgid "This field cannot be edited."
msgstr "Questo campo è obbligatorio."

#: src/wtforms/validators.py:714
msgid "This field is disabled and cannot have a value."
msgstr ""

#: src/wtforms/csrf/core.py:96
msgid "Invalid CSRF Token."
msgstr "Token CSRF non valido"

#: src/wtforms/csrf/session.py:63
msgid "CSRF token missing."
msgstr "Token CSRF mancante"

#: src/wtforms/csrf/session.py:71
msgid "CSRF failed."
msgstr "CSRF fallito"

#: src/wtforms/csrf/session.py:76
msgid "CSRF token expired."
msgstr "Token CSRF scaduto"

#: src/wtforms/fields/choices.py:142
msgid "Invalid Choice: could not coerce."
msgstr "Opzione non valida: valore non convertibile"

#: src/wtforms/fields/choices.py:149 src/wtforms/fields/choices.py:203
msgid "Choices cannot be None."
msgstr ""

#: src/wtforms/fields/choices.py:155
msgid "Not a valid choice."
msgstr "Non è una opzione valida"

#: src/wtforms/fields/choices.py:193
msgid "Invalid choice(s): one or more data inputs could not be coerced."
msgstr ""
"Opzione(i) non valida(e): uno o pù valori non possono essere convertiti"

#: src/wtforms/fields/choices.py:214
#, fuzzy, python-format
#| msgid "'%(value)s' is not a valid choice for this field."
msgid "'%(value)s' is not a valid choice for this field."
msgid_plural "'%(value)s' are not valid choices for this field."
msgstr[0] "'%(value)s' non è una opzione valida per questo campo"
msgstr[1] "'%(value)s' non è una opzione valida per questo campo"

#: src/wtforms/fields/datetime.py:51
msgid "Not a valid datetime value."
msgstr "Il valore non corrisponde ad una data e un orario validi"

#: src/wtforms/fields/datetime.py:77
msgid "Not a valid date value."
msgstr "Non è una data valida"

#: src/wtforms/fields/datetime.py:103
msgid "Not a valid time value."
msgstr ""

#: src/wtforms/fields/datetime.py:148
#, fuzzy
#| msgid "Not a valid date value."
msgid "Not a valid week value."
msgstr "Non è una data valida"

#: src/wtforms/fields/numeric.py:82 src/wtforms/fields/numeric.py:92
msgid "Not a valid integer value."
msgstr "Non è una valore intero valido"

#: src/wtforms/fields/numeric.py:168
msgid "Not a valid decimal value."
msgstr "Non è un valore decimale valido"

#: src/wtforms/fields/numeric.py:197
msgid "Not a valid float value."
msgstr "Non è un valore in virgola mobile valido"
