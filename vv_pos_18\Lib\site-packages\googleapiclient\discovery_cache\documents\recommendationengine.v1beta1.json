{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/cloud-platform": {"description": "See, edit, configure, and delete your Google Cloud data and see the email address for your Google Account."}}}}, "basePath": "", "baseUrl": "https://recommendationengine.googleapis.com/", "batchPath": "batch", "canonicalName": "Recommendations AI", "description": "Note that we now highly recommend new customers to use Retail API, which incorporates the GA version of the Recommendations AI funtionalities. To enable Retail API, please visit https://console.cloud.google.com/apis/library/retail.googleapis.com. The Recommendations AI service enables customers to build end-to-end personalized recommendation systems without requiring a high level of expertise in machine learning, recommendation system, or Google Cloud.", "discoveryVersion": "v1", "documentationLink": "https://cloud.google.com/recommendations-ai/docs", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "recommendationengine:v1beta1", "kind": "discovery#restDescription", "mtlsRootUrl": "https://recommendationengine.mtls.googleapis.com/", "name": "recommendationengine", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"projects": {"resources": {"locations": {"resources": {"catalogs": {"methods": {"list": {"description": "Lists all the catalog configurations associated with the project.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/catalogs", "httpMethod": "GET", "id": "recommendationengine.projects.locations.catalogs.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Optional. Maximum number of results to return. If unspecified, defaults to 50. Max allowed value is 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token, received from a previous `ListCatalogs` call. Provide this to retrieve the subsequent page.", "location": "query", "type": "string"}, "parent": {"description": "Required. The account resource name with an associated location.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/catalogs", "response": {"$ref": "GoogleCloudRecommendationengineV1beta1ListCatalogsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates the catalog configuration.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}", "httpMethod": "PATCH", "id": "recommendationengine.projects.locations.catalogs.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "The fully qualified resource name of the catalog.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Optional. Indicates which fields in the provided 'catalog' to update. If not set, will only update the catalog_item_level_config field. Currently only fields that can be updated are catalog_item_level_config.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1beta1/{+name}", "request": {"$ref": "GoogleCloudRecommendationengineV1beta1Catalog"}, "response": {"$ref": "GoogleCloudRecommendationengineV1beta1Catalog"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"catalogItems": {"methods": {"create": {"description": "Creates a catalog item.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}/catalogItems", "httpMethod": "POST", "id": "recommendationengine.projects.locations.catalogs.catalogItems.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent catalog resource name, such as `projects/*/locations/global/catalogs/default_catalog`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/catalogItems", "request": {"$ref": "GoogleCloudRecommendationengineV1beta1CatalogItem"}, "response": {"$ref": "GoogleCloudRecommendationengineV1beta1CatalogItem"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a catalog item.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}/catalogItems/{catalogItemsId}", "httpMethod": "DELETE", "id": "recommendationengine.projects.locations.catalogs.catalogItems.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Full resource name of catalog item, such as `projects/*/locations/global/catalogs/default_catalog/catalogItems/some_catalog_item_id`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+/catalogItems/.*$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets a specific catalog item.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}/catalogItems/{catalogItemsId}", "httpMethod": "GET", "id": "recommendationengine.projects.locations.catalogs.catalogItems.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Full resource name of catalog item, such as `projects/*/locations/global/catalogs/default_catalog/catalogitems/some_catalog_item_id`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+/catalogItems/.*$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "GoogleCloudRecommendationengineV1beta1CatalogItem"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "import": {"description": "Bulk import of multiple catalog items. Request processing may be synchronous. No partial updating supported. Non-existing items will be created. Operation.response is of type ImportResponse. Note that it is possible for a subset of the items to be successfully updated.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}/catalogItems:import", "httpMethod": "POST", "id": "recommendationengine.projects.locations.catalogs.catalogItems.import", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. `projects/1234/locations/global/catalogs/default_catalog` If no updateMask is specified, requires catalogItems.create permission. If updateMask is specified, requires catalogItems.update permission.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/catalogItems:import", "request": {"$ref": "GoogleCloudRecommendationengineV1beta1ImportCatalogItemsRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Gets a list of catalog items.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}/catalogItems", "httpMethod": "GET", "id": "recommendationengine.projects.locations.catalogs.catalogItems.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. Use of this field is not supported by version v1beta1.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. Maximum number of results to return per page. If zero, the service will choose a reasonable default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. The previous ListCatalogItemsResponse.next_page_token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent catalog resource name, such as `projects/*/locations/global/catalogs/default_catalog`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/catalogItems", "response": {"$ref": "GoogleCloudRecommendationengineV1beta1ListCatalogItemsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates a catalog item. Partial updating is supported. Non-existing items will be created.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}/catalogItems/{catalogItemsId}", "httpMethod": "PATCH", "id": "recommendationengine.projects.locations.catalogs.catalogItems.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Full resource name of catalog item, such as `projects/*/locations/global/catalogs/default_catalog/catalogItems/some_catalog_item_id`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+/catalogItems/.*$", "required": true, "type": "string"}, "updateMask": {"description": "Optional. Indicates which fields in the provided 'item' to update. If not set, will by default update all fields.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1beta1/{+name}", "request": {"$ref": "GoogleCloudRecommendationengineV1beta1CatalogItem"}, "response": {"$ref": "GoogleCloudRecommendationengineV1beta1CatalogItem"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "eventStores": {"resources": {"operations": {"methods": {"get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}/eventStores/{eventStoresId}/operations/{operationsId}", "httpMethod": "GET", "id": "recommendationengine.projects.locations.catalogs.eventStores.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+/eventStores/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}/eventStores/{eventStoresId}/operations", "httpMethod": "GET", "id": "recommendationengine.projects.locations.catalogs.eventStores.operations.list", "parameterOrder": ["name"], "parameters": {"filter": {"description": "The standard list filter.", "location": "query", "type": "string"}, "name": {"description": "The name of the operation's parent resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+/eventStores/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "The standard list page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The standard list page token.", "location": "query", "type": "string"}}, "path": "v1beta1/{+name}/operations", "response": {"$ref": "GoogleLongrunningListOperationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "placements": {"methods": {"predict": {"description": "Makes a recommendation prediction. If using API Key based authentication, the API Key must be registered using the PredictionApiKeyRegistry service. [Learn more](https://cloud.google.com/recommendations-ai/docs/setting-up#register-key).", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}/eventStores/{eventStoresId}/placements/{placementsId}:predict", "httpMethod": "POST", "id": "recommendationengine.projects.locations.catalogs.eventStores.placements.predict", "parameterOrder": ["name"], "parameters": {"name": {"location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+/eventStores/[^/]+/placements/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}:predict", "request": {"$ref": "GoogleCloudRecommendationengineV1beta1PredictRequest"}, "response": {"$ref": "GoogleCloudRecommendationengineV1beta1PredictResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "predictionApiKeyRegistrations": {"methods": {"create": {"description": "Register an API key for use with predict method.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}/eventStores/{eventStoresId}/predictionApiKeyRegistrations", "httpMethod": "POST", "id": "recommendationengine.projects.locations.catalogs.eventStores.predictionApiKeyRegistrations.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent resource path. `projects/*/locations/global/catalogs/default_catalog/eventStores/default_event_store`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+/eventStores/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/predictionApiKeyRegistrations", "request": {"$ref": "GoogleCloudRecommendationengineV1beta1CreatePredictionApiKeyRegistrationRequest"}, "response": {"$ref": "GoogleCloudRecommendationengineV1beta1PredictionApiKeyRegistration"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Unregister an apiKey from using for predict method.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}/eventStores/{eventStoresId}/predictionApiKeyRegistrations/{predictionApiKeyRegistrationsId}", "httpMethod": "DELETE", "id": "recommendationengine.projects.locations.catalogs.eventStores.predictionApiKeyRegistrations.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The API key to unregister including full resource path. `projects/*/locations/global/catalogs/default_catalog/eventStores/default_event_store/predictionApiKeyRegistrations/`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+/eventStores/[^/]+/predictionApiKeyRegistrations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "List the registered apiKeys for use with predict method.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}/eventStores/{eventStoresId}/predictionApiKeyRegistrations", "httpMethod": "GET", "id": "recommendationengine.projects.locations.catalogs.eventStores.predictionApiKeyRegistrations.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Optional. Maximum number of results to return per page. If unset, the service will choose a reasonable default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. The previous `ListPredictionApiKeyRegistration.nextPageToken`.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent placement resource name such as `projects/1234/locations/global/catalogs/default_catalog/eventStores/default_event_store`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+/eventStores/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/predictionApiKeyRegistrations", "response": {"$ref": "GoogleCloudRecommendationengineV1beta1ListPredictionApiKeyRegistrationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "userEvents": {"methods": {"collect": {"description": "Writes a single user event from the browser. This uses a GET request to due to browser restriction of POST-ing to a 3rd party domain. This method is used only by the Recommendations AI JavaScript pixel. Users should not call this method directly.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}/eventStores/{eventStoresId}/userEvents:collect", "httpMethod": "GET", "id": "recommendationengine.projects.locations.catalogs.eventStores.userEvents.collect", "parameterOrder": ["parent"], "parameters": {"ets": {"description": "Optional. The event timestamp in milliseconds. This prevents browser caching of otherwise identical get requests. The name is abbreviated to reduce the payload bytes.", "format": "int64", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent eventStore name, such as `projects/1234/locations/global/catalogs/default_catalog/eventStores/default_event_store`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+/eventStores/[^/]+$", "required": true, "type": "string"}, "uri": {"description": "Optional. The url including cgi-parameters but excluding the hash fragment. The URL must be truncated to 1.5K bytes to conservatively be under the 2K bytes. This is often more useful than the referer url, because many browsers only send the domain for 3rd party requests.", "location": "query", "type": "string"}, "userEvent": {"description": "Required. URL encoded UserEvent proto.", "location": "query", "type": "string"}}, "path": "v1beta1/{+parent}/userEvents:collect", "response": {"$ref": "GoogleApiHttpBody"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "import": {"description": "Bulk import of User events. Request processing might be synchronous. Events that already exist are skipped. Use this method for backfilling historical user events. Operation.response is of type ImportResponse. Note that it is possible for a subset of the items to be successfully inserted. Operation.metadata is of type ImportMetadata.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}/eventStores/{eventStoresId}/userEvents:import", "httpMethod": "POST", "id": "recommendationengine.projects.locations.catalogs.eventStores.userEvents.import", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. `projects/1234/locations/global/catalogs/default_catalog/eventStores/default_event_store`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+/eventStores/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/userEvents:import", "request": {"$ref": "GoogleCloudRecommendationengineV1beta1ImportUserEventsRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Gets a list of user events within a time range, with potential filtering. The method does not list unjoined user events. Unjoined user event definition: when a user event is ingested from Recommendations AI User Event APIs, the catalog item included in the user event is connected with the current catalog. If a catalog item of the ingested event is not in the current catalog, it could lead to degraded model quality. This is called an unjoined event.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}/eventStores/{eventStoresId}/userEvents", "httpMethod": "GET", "id": "recommendationengine.projects.locations.catalogs.eventStores.userEvents.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. Filtering expression to specify restrictions over returned events. This is a sequence of terms, where each term applies some kind of a restriction to the returned user events. Use this expression to restrict results to a specific time range, or filter events by eventType. eg: eventTime > \"2012-04-23T18:25:43.511Z\" eventsMissingCatalogItems eventTime<\"2012-04-23T18:25:43.511Z\" eventType=search We expect only 3 types of fields: * eventTime: this can be specified a maximum of 2 times, once with a less than operator and once with a greater than operator. The eventTime restrict should result in one contiguous valid eventTime range. * eventType: only 1 eventType restriction can be specified. * eventsMissingCatalogItems: specififying this will restrict results to events for which catalog items were not found in the catalog. The default behavior is to return only those events for which catalog items were found. Some examples of valid filters expressions: * Example 1: eventTime > \"2012-04-23T18:25:43.511Z\" eventTime < \"2012-04-23T18:30:43.511Z\" * Example 2: eventTime > \"2012-04-23T18:25:43.511Z\" eventType = detail-page-view * Example 3: eventsMissingCatalogItems eventType = search eventTime < \"2018-04-23T18:30:43.511Z\" * Example 4: eventTime > \"2012-04-23T18:25:43.511Z\" * Example 5: eventType = search * Example 6: eventsMissingCatalogItems", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. Maximum number of results to return per page. If zero, the service will choose a reasonable default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. The previous ListUserEventsResponse.next_page_token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent eventStore resource name, such as `projects/*/locations/*/catalogs/default_catalog/eventStores/default_event_store`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+/eventStores/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/userEvents", "response": {"$ref": "GoogleCloudRecommendationengineV1beta1ListUserEventsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "purge": {"description": "Deletes permanently all user events specified by the filter provided. Depending on the number of events specified by the filter, this operation could take hours or days to complete. To test a filter, use the list command first.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}/eventStores/{eventStoresId}/userEvents:purge", "httpMethod": "POST", "id": "recommendationengine.projects.locations.catalogs.eventStores.userEvents.purge", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The resource name of the event_store under which the events are created. The format is `projects/${projectId}/locations/global/catalogs/${catalogId}/eventStores/${eventStoreId}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+/eventStores/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/userEvents:purge", "request": {"$ref": "GoogleCloudRecommendationengineV1beta1PurgeUserEventsRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "rejoin": {"description": "Triggers a user event rejoin operation with latest catalog data. Events will not be annotated with detailed catalog information if catalog item is missing at the time the user event is ingested, and these events are stored as unjoined events with a limited usage on training and serving. This API can be used to trigger a 'join' operation on specified events with latest version of catalog items. It can also be used to correct events joined with wrong catalog items.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}/eventStores/{eventStoresId}/userEvents:rejoin", "httpMethod": "POST", "id": "recommendationengine.projects.locations.catalogs.eventStores.userEvents.rejoin", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. Full resource name of user event, such as `projects/*/locations/*/catalogs/default_catalog/eventStores/default_event_store`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+/eventStores/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/userEvents:rejoin", "request": {"$ref": "GoogleCloudRecommendationengineV1beta1RejoinUserEventsRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "write": {"description": "Writes a single user event.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}/eventStores/{eventStoresId}/userEvents:write", "httpMethod": "POST", "id": "recommendationengine.projects.locations.catalogs.eventStores.userEvents.write", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent eventStore resource name, such as \"projects/1234/locations/global/catalogs/default_catalog/eventStores/default_event_store\".", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+/eventStores/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/userEvents:write", "request": {"$ref": "GoogleCloudRecommendationengineV1beta1UserEvent"}, "response": {"$ref": "GoogleCloudRecommendationengineV1beta1UserEvent"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}, "operations": {"methods": {"get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}/operations/{operationsId}", "httpMethod": "GET", "id": "recommendationengine.projects.locations.catalogs.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}/operations", "httpMethod": "GET", "id": "recommendationengine.projects.locations.catalogs.operations.list", "parameterOrder": ["name"], "parameters": {"filter": {"description": "The standard list filter.", "location": "query", "type": "string"}, "name": {"description": "The name of the operation's parent resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/catalogs/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "The standard list page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The standard list page token.", "location": "query", "type": "string"}}, "path": "v1beta1/{+name}/operations", "response": {"$ref": "GoogleLongrunningListOperationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}}}}}}, "revision": "20240702", "rootUrl": "https://recommendationengine.googleapis.com/", "schemas": {"GoogleApiHttpBody": {"description": "Message that represents an arbitrary HTTP body. It should only be used for payload formats that can't be represented as JSON, such as raw binary or an HTML page. This message can be used both in streaming and non-streaming API methods in the request as well as the response. It can be used as a top-level request field, which is convenient if one wants to extract parameters from either the URL or HTTP template into the request fields and also want access to the raw HTTP body. Example: message GetResourceRequest { // A unique request id. string request_id = 1; // The raw HTTP body is bound to this field. google.api.HttpBody http_body = 2; } service ResourceService { rpc GetResource(GetResourceRequest) returns (google.api.HttpBody); rpc UpdateResource(google.api.HttpBody) returns (google.protobuf.Empty); } Example with streaming methods: service CaldavService { rpc GetCalendar(stream google.api.HttpBody) returns (stream google.api.HttpBody); rpc UpdateCalendar(stream google.api.HttpBody) returns (stream google.api.HttpBody); } Use of this type only changes how the request and response bodies are handled, all other features will continue to work unchanged.", "id": "GoogleApiHttpBody", "properties": {"contentType": {"description": "The HTTP Content-Type header value specifying the content type of the body.", "type": "string"}, "data": {"description": "The HTTP request/response body as raw binary.", "format": "byte", "type": "string"}, "extensions": {"description": "Application specific response metadata. Must be set in the first response for streaming APIs.", "items": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "type": "object"}, "type": "array"}}, "type": "object"}, "GoogleCloudRecommendationengineV1alphaRejoinCatalogMetadata": {"description": "Metadata for TriggerCatalogRejoin method.", "id": "GoogleCloudRecommendationengineV1alphaRejoinCatalogMetadata", "properties": {}, "type": "object"}, "GoogleCloudRecommendationengineV1alphaRejoinCatalogResponse": {"description": "Response message for TriggerCatalogRejoin method.", "id": "GoogleCloudRecommendationengineV1alphaRejoinCatalogResponse", "properties": {"rejoinedUserEventsCount": {"description": "Number of user events that were joined with latest catalog items.", "format": "int64", "type": "string"}}, "type": "object"}, "GoogleCloudRecommendationengineV1alphaTuningMetadata": {"description": "Metadata associated with a tune operation.", "id": "GoogleCloudRecommendationengineV1alphaTuningMetadata", "properties": {"recommendationModel": {"description": "The resource name of the recommendation model that this tune applies to. Format: projects/{project_number}/locations/{location_id}/catalogs/{catalog_id}/eventStores/{event_store_id}/recommendationModels/{recommendation_model_id}", "type": "string"}}, "type": "object"}, "GoogleCloudRecommendationengineV1alphaTuningResponse": {"description": "Response associated with a tune operation.", "id": "GoogleCloudRecommendationengineV1alphaTuningResponse", "properties": {}, "type": "object"}, "GoogleCloudRecommendationengineV1beta1BigQuerySource": {"description": "BigQuery source import data from.", "id": "GoogleCloudRecommendationengineV1beta1BigQuerySource", "properties": {"dataSchema": {"description": "Optional. The schema to use when parsing the data from the source. Supported values for catalog imports: 1: \"catalog_recommendations_ai\" using https://cloud.google.com/recommendations-ai/docs/upload-catalog#json (Default for catalogItems.import) 2: \"catalog_merchant_center\" using https://cloud.google.com/recommendations-ai/docs/upload-catalog#mc Supported values for user event imports: 1: \"user_events_recommendations_ai\" using https://cloud.google.com/recommendations-ai/docs/manage-user-events#import (Default for userEvents.import) 2. \"user_events_ga360\" using https://support.google.com/analytics/answer/3437719?hl=en", "type": "string"}, "datasetId": {"description": "Required. The BigQuery data set to copy the data from.", "type": "string"}, "gcsStagingDir": {"description": "Optional. Intermediate Cloud Storage directory used for the import. Can be specified if one wants to have the BigQuery export to a specific Cloud Storage directory.", "type": "string"}, "projectId": {"description": "Optional. The project id (can be project # or id) that the BigQuery source is in. If not specified, inherits the project id from the parent request.", "type": "string"}, "tableId": {"description": "Required. The BigQuery table to copy the data from.", "type": "string"}}, "type": "object"}, "GoogleCloudRecommendationengineV1beta1Catalog": {"description": "The catalog configuration. Next ID: 5.", "id": "GoogleCloudRecommendationengineV1beta1Catalog", "properties": {"catalogItemLevelConfig": {"$ref": "GoogleCloudRecommendationengineV1beta1CatalogItemLevelConfig", "description": "Required. The catalog item level configuration."}, "defaultEventStoreId": {"description": "Required. The ID of the default event store.", "type": "string"}, "displayName": {"description": "Required. The catalog display name.", "type": "string"}, "name": {"description": "The fully qualified resource name of the catalog.", "type": "string"}}, "type": "object"}, "GoogleCloudRecommendationengineV1beta1CatalogInlineSource": {"description": "The inline source for the input config for ImportCatalogItems method.", "id": "GoogleCloudRecommendationengineV1beta1CatalogInlineSource", "properties": {"catalogItems": {"description": "Optional. A list of catalog items to update/create. Recommended max of 10k items.", "items": {"$ref": "GoogleCloudRecommendationengineV1beta1CatalogItem"}, "type": "array"}}, "type": "object"}, "GoogleCloudRecommendationengineV1beta1CatalogItem": {"description": "CatalogItem captures all metadata information of items to be recommended.", "id": "GoogleCloudRecommendationengineV1beta1CatalogItem", "properties": {"categoryHierarchies": {"description": "Required. Catalog item categories. This field is repeated for supporting one catalog item belonging to several parallel category hierarchies. For example, if a shoes product belongs to both [\"Shoes & Accessories\" -> \"Shoes\"] and [\"Sports & Fitness\" -> \"Athletic Clothing\" -> \"Shoes\"], it could be represented as: \"categoryHierarchies\": [ { \"categories\": [\"Shoes & Accessories\", \"Shoes\"]}, { \"categories\": [\"Sports & Fitness\", \"Athletic Clothing\", \"Shoes\"] } ]", "items": {"$ref": "GoogleCloudRecommendationengineV1beta1CatalogItemCategoryHierarchy"}, "type": "array"}, "description": {"description": "Optional. Catalog item description. UTF-8 encoded string with a length limit of 5 KiB.", "type": "string"}, "id": {"description": "Required. Catalog item identifier. UTF-8 encoded string with a length limit of 128 bytes. This id must be unique among all catalog items within the same catalog. It should also be used when logging user events in order for the user events to be joined with the Catalog.", "type": "string"}, "itemAttributes": {"$ref": "GoogleCloudRecommendationengineV1beta1FeatureMap", "description": "Optional. Highly encouraged. Extra catalog item attributes to be included in the recommendation model. For example, for retail products, this could include the store name, vendor, style, color, etc. These are very strong signals for recommendation model, thus we highly recommend providing the item attributes here."}, "itemGroupId": {"description": "Optional. Variant group identifier for prediction results. UTF-8 encoded string with a length limit of 128 bytes. This field must be enabled before it can be used. [Learn more](/recommendations-ai/docs/catalog#item-group-id).", "type": "string"}, "languageCode": {"description": "Optional. Deprecated. The model automatically detects the text language. Your catalog can include text in different languages, but duplicating catalog items to provide text in multiple languages can result in degraded model performance.", "type": "string"}, "productMetadata": {"$ref": "GoogleCloudRecommendationengineV1beta1ProductCatalogItem", "description": "Optional. Metadata specific to retail products."}, "tags": {"description": "Optional. Filtering tags associated with the catalog item. Each tag should be a UTF-8 encoded string with a length limit of 1 KiB. This tag can be used for filtering recommendation results by passing the tag as part of the predict request filter.", "items": {"type": "string"}, "type": "array"}, "title": {"description": "Required. Catalog item title. UTF-8 encoded string with a length limit of 1 KiB.", "type": "string"}}, "type": "object"}, "GoogleCloudRecommendationengineV1beta1CatalogItemCategoryHierarchy": {"description": "Category represents catalog item category hierarchy.", "id": "GoogleCloudRecommendationengineV1beta1CatalogItemCategoryHierarchy", "properties": {"categories": {"description": "Required. Catalog item categories. Each category should be a UTF-8 encoded string with a length limit of 2 KiB. Note that the order in the list denotes the specificity (from least to most specific).", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleCloudRecommendationengineV1beta1CatalogItemLevelConfig": {"description": "Configures the catalog level that users send events to, and the level at which predictions are made.", "id": "GoogleCloudRecommendationengineV1beta1CatalogItemLevelConfig", "properties": {"eventItemLevel": {"description": "Optional. Level of the catalog at which events are uploaded. See https://cloud.google.com/recommendations-ai/docs/catalog#catalog-levels for more details.", "enum": ["CATALOG_ITEM_LEVEL_UNSPECIFIED", "VARIANT", "MASTER"], "enumDescriptions": ["Unknown value - should never be used.", "Catalog items are at variant level.", "Catalog items are at master level."], "type": "string"}, "predictItemLevel": {"description": "Optional. Level of the catalog at which predictions are made. See https://cloud.google.com/recommendations-ai/docs/catalog#catalog-levels for more details.", "enum": ["CATALOG_ITEM_LEVEL_UNSPECIFIED", "VARIANT", "MASTER"], "enumDescriptions": ["Unknown value - should never be used.", "Catalog items are at variant level.", "Catalog items are at master level."], "type": "string"}}, "type": "object"}, "GoogleCloudRecommendationengineV1beta1CreatePredictionApiKeyRegistrationRequest": {"description": "Request message for the `CreatePredictionApiKeyRegistration` method.", "id": "GoogleCloudRecommendationengineV1beta1CreatePredictionApiKeyRegistrationRequest", "properties": {"predictionApiKeyRegistration": {"$ref": "GoogleCloudRecommendationengineV1beta1PredictionApiKeyRegistration", "description": "Required. The prediction API key registration."}}, "type": "object"}, "GoogleCloudRecommendationengineV1beta1EventDetail": {"description": "User event details shared by all recommendation types.", "id": "GoogleCloudRecommendationengineV1beta1EventDetail", "properties": {"eventAttributes": {"$ref": "GoogleCloudRecommendationengineV1beta1FeatureMap", "description": "Optional. Extra user event features to include in the recommendation model. For product recommendation, an example of extra user information is traffic_channel, i.e. how user arrives at the site. Users can arrive at the site by coming to the site directly, or coming through Google search, and etc."}, "experimentIds": {"description": "Optional. A list of identifiers for the independent experiment groups this user event belongs to. This is used to distinguish between user events associated with different experiment setups (e.g. using Recommendation Engine system, using different recommendation models).", "items": {"type": "string"}, "type": "array"}, "pageViewId": {"description": "Optional. A unique id of a web page view. This should be kept the same for all user events triggered from the same pageview. For example, an item detail page view could trigger multiple events as the user is browsing the page. The `pageViewId` property should be kept the same for all these events so that they can be grouped together properly. This `pageViewId` will be automatically generated if using the JavaScript pixel.", "type": "string"}, "recommendationToken": {"description": "Optional. Recommendation token included in the recommendation prediction response. This field enables accurate attribution of recommendation model performance. This token enables us to accurately attribute page view or purchase back to the event and the particular predict response containing this clicked/purchased item. If user clicks on product <PERSON> in the recommendation results, pass the `PredictResponse.recommendationToken` property as a url parameter to product <PERSON>'s page. When recording events on product <PERSON>'s page, log the PredictResponse.recommendation_token to this field. Optional, but highly encouraged for user events that are the result of a recommendation prediction query.", "type": "string"}, "referrerUri": {"description": "Optional. The referrer url of the current page. When using the JavaScript pixel, this value is filled in automatically.", "type": "string"}, "uri": {"description": "Optional. Complete url (window.location.href) of the user's current page. When using the JavaScript pixel, this value is filled in automatically. Maximum length 5KB.", "type": "string"}}, "type": "object"}, "GoogleCloudRecommendationengineV1beta1FeatureMap": {"description": "FeatureMap represents extra features that customers want to include in the recommendation model for catalogs/user events as categorical/numerical features.", "id": "GoogleCloudRecommendationengineV1beta1FeatureMap", "properties": {"categoricalFeatures": {"additionalProperties": {"$ref": "GoogleCloudRecommendationengineV1beta1FeatureMapStringList"}, "description": "Categorical features that can take on one of a limited number of possible values. Some examples would be the brand/maker of a product, or country of a customer. Feature names and values must be UTF-8 encoded strings. For example: `{ \"colors\": {\"value\": [\"yellow\", \"green\"]}, \"sizes\": {\"value\":[\"S\", \"M\"]}`", "type": "object"}, "numericalFeatures": {"additionalProperties": {"$ref": "GoogleCloudRecommendationengineV1beta1FeatureMapFloatList"}, "description": "Numerical features. Some examples would be the height/weight of a product, or age of a customer. Feature names must be UTF-8 encoded strings. For example: `{ \"lengths_cm\": {\"value\":[2.3, 15.4]}, \"heights_cm\": {\"value\":[8.1, 6.4]} }`", "type": "object"}}, "type": "object"}, "GoogleCloudRecommendationengineV1beta1FeatureMapFloatList": {"description": "A list of float features.", "id": "GoogleCloudRecommendationengineV1beta1FeatureMapFloatList", "properties": {"value": {"description": "Float feature value.", "items": {"format": "float", "type": "number"}, "type": "array"}}, "type": "object"}, "GoogleCloudRecommendationengineV1beta1FeatureMapStringList": {"description": "A list of string features.", "id": "GoogleCloudRecommendationengineV1beta1FeatureMapStringList", "properties": {"value": {"description": "String feature value with a length limit of 128 bytes.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleCloudRecommendationengineV1beta1GcsSource": {"description": "Google Cloud Storage location for input content. format.", "id": "GoogleCloudRecommendationengineV1beta1GcsSource", "properties": {"inputUris": {"description": "Required. Google Cloud Storage URIs to input files. URI can be up to 2000 characters long. URIs can match the full object path (for example, `gs://bucket/directory/object.json`) or a pattern matching one or more files, such as `gs://bucket/directory/*.json`. A request can contain at most 100 files, and each file can be up to 2 GB. See [Importing catalog information](/recommendations-ai/docs/upload-catalog) for the expected file format and setup instructions.", "items": {"type": "string"}, "type": "array"}, "jsonSchema": {"description": "Optional. The schema to use when parsing the data from the source. Supported values for catalog imports: 1: \"catalog_recommendations_ai\" using https://cloud.google.com/recommendations-ai/docs/upload-catalog#json (Default for catalogItems.import) 2: \"catalog_merchant_center\" using https://cloud.google.com/recommendations-ai/docs/upload-catalog#mc Supported values for user events imports: 1: \"user_events_recommendations_ai\" using https://cloud.google.com/recommendations-ai/docs/manage-user-events#import (Default for userEvents.import) 2. \"user_events_ga360\" using https://support.google.com/analytics/answer/3437719?hl=en", "type": "string"}}, "type": "object"}, "GoogleCloudRecommendationengineV1beta1Image": {"description": "Catalog item thumbnail/detail image.", "id": "GoogleCloudRecommendationengineV1beta1Image", "properties": {"height": {"description": "Optional. Height of the image in number of pixels.", "format": "int32", "type": "integer"}, "uri": {"description": "Required. URL of the image with a length limit of 5 KiB.", "type": "string"}, "width": {"description": "Optional. Width of the image in number of pixels.", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleCloudRecommendationengineV1beta1ImportCatalogItemsRequest": {"description": "Request message for Import methods.", "id": "GoogleCloudRecommendationengineV1beta1ImportCatalogItemsRequest", "properties": {"errorsConfig": {"$ref": "GoogleCloudRecommendationengineV1beta1ImportErrorsConfig", "description": "Optional. The desired location of errors incurred during the Import."}, "inputConfig": {"$ref": "GoogleCloudRecommendationengineV1beta1InputConfig", "description": "Required. The desired input location of the data."}, "requestId": {"description": "Optional. Unique identifier provided by client, within the ancestor dataset scope. Ensures idempotency and used for request deduplication. Server-generated if unspecified. Up to 128 characters long. This is returned as google.longrunning.Operation.name in the response.", "type": "string"}, "updateMask": {"description": "Optional. Indicates which fields in the provided imported 'items' to update. If not set, will by default update all fields.", "format": "google-fieldmask", "type": "string"}}, "type": "object"}, "GoogleCloudRecommendationengineV1beta1ImportCatalogItemsResponse": {"description": "Response of the ImportCatalogItemsRequest. If the long running operation is done, then this message is returned by the google.longrunning.Operations.response field if the operation was successful.", "id": "GoogleCloudRecommendationengineV1beta1ImportCatalogItemsResponse", "properties": {"errorSamples": {"description": "A sample of errors encountered while processing the request.", "items": {"$ref": "GoogleRpcStatus"}, "type": "array"}, "errorsConfig": {"$ref": "GoogleCloudRecommendationengineV1beta1ImportErrorsConfig", "description": "Echoes the destination for the complete errors in the request if set."}}, "type": "object"}, "GoogleCloudRecommendationengineV1beta1ImportErrorsConfig": {"description": "Configuration of destination for Import related errors.", "id": "GoogleCloudRecommendationengineV1beta1ImportErrorsConfig", "properties": {"gcsPrefix": {"description": "Google Cloud Storage path for import errors. This must be an empty, existing Cloud Storage bucket. Import errors will be written to a file in this bucket, one per line, as a JSON-encoded `google.rpc.Status` message.", "type": "string"}}, "type": "object"}, "GoogleCloudRecommendationengineV1beta1ImportMetadata": {"description": "Metadata related to the progress of the Import operation. This will be returned by the google.longrunning.Operation.metadata field.", "id": "GoogleCloudRecommendationengineV1beta1ImportMetadata", "properties": {"createTime": {"description": "Operation create time.", "format": "google-datetime", "type": "string"}, "failureCount": {"description": "Count of entries that encountered errors while processing.", "format": "int64", "type": "string"}, "operationName": {"description": "Name of the operation.", "type": "string"}, "requestId": {"description": "Id of the request / operation. This is parroting back the requestId that was passed in the request.", "type": "string"}, "successCount": {"description": "Count of entries that were processed successfully.", "format": "int64", "type": "string"}, "updateTime": {"description": "Operation last update time. If the operation is done, this is also the finish time.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleCloudRecommendationengineV1beta1ImportUserEventsRequest": {"description": "Request message for the ImportUserEvents request.", "id": "GoogleCloudRecommendationengineV1beta1ImportUserEventsRequest", "properties": {"errorsConfig": {"$ref": "GoogleCloudRecommendationengineV1beta1ImportErrorsConfig", "description": "Optional. The desired location of errors incurred during the Import."}, "inputConfig": {"$ref": "GoogleCloudRecommendationengineV1beta1InputConfig", "description": "Required. The desired input location of the data."}, "requestId": {"description": "Optional. Unique identifier provided by client, within the ancestor dataset scope. Ensures idempotency for expensive long running operations. Server-generated if unspecified. Up to 128 characters long. This is returned as google.longrunning.Operation.name in the response. Note that this field must not be set if the desired input config is catalog_inline_source.", "type": "string"}}, "type": "object"}, "GoogleCloudRecommendationengineV1beta1ImportUserEventsResponse": {"description": "Response of the ImportUserEventsRequest. If the long running operation was successful, then this message is returned by the google.longrunning.Operations.response field if the operation was successful.", "id": "GoogleCloudRecommendationengineV1beta1ImportUserEventsResponse", "properties": {"errorSamples": {"description": "A sample of errors encountered while processing the request.", "items": {"$ref": "GoogleRpcStatus"}, "type": "array"}, "errorsConfig": {"$ref": "GoogleCloudRecommendationengineV1beta1ImportErrorsConfig", "description": "Echoes the destination for the complete errors if this field was set in the request."}, "importSummary": {"$ref": "GoogleCloudRecommendationengineV1beta1UserEventImportSummary", "description": "Aggregated statistics of user event import status."}}, "type": "object"}, "GoogleCloudRecommendationengineV1beta1InputConfig": {"description": "The input config source.", "id": "GoogleCloudRecommendationengineV1beta1InputConfig", "properties": {"bigQuerySource": {"$ref": "GoogleCloudRecommendationengineV1beta1BigQuerySource", "description": "BigQuery input source."}, "catalogInlineSource": {"$ref": "GoogleCloudRecommendationengineV1beta1CatalogInlineSource", "description": "The Inline source for the input content for Catalog items."}, "gcsSource": {"$ref": "GoogleCloudRecommendationengineV1beta1GcsSource", "description": "Google Cloud Storage location for the input content."}, "userEventInlineSource": {"$ref": "GoogleCloudRecommendationengineV1beta1UserEventInlineSource", "description": "The Inline source for the input content for UserEvents."}}, "type": "object"}, "GoogleCloudRecommendationengineV1beta1ListCatalogItemsResponse": {"description": "Response message for ListCatalogItems method.", "id": "GoogleCloudRecommendationengineV1beta1ListCatalogItemsResponse", "properties": {"catalogItems": {"description": "The catalog items.", "items": {"$ref": "GoogleCloudRecommendationengineV1beta1CatalogItem"}, "type": "array"}, "nextPageToken": {"description": "If empty, the list is complete. If nonempty, the token to pass to the next request's ListCatalogItemRequest.page_token.", "type": "string"}}, "type": "object"}, "GoogleCloudRecommendationengineV1beta1ListCatalogsResponse": {"description": "Response for ListCatalogs method.", "id": "GoogleCloudRecommendationengineV1beta1ListCatalogsResponse", "properties": {"catalogs": {"description": "Output only. All the customer's catalogs.", "items": {"$ref": "GoogleCloudRecommendationengineV1beta1Catalog"}, "readOnly": true, "type": "array"}, "nextPageToken": {"description": "Pagination token, if not returned indicates the last page.", "type": "string"}}, "type": "object"}, "GoogleCloudRecommendationengineV1beta1ListPredictionApiKeyRegistrationsResponse": {"description": "Response message for the `ListPredictionApiKeyRegistrations`.", "id": "GoogleCloudRecommendationengineV1beta1ListPredictionApiKeyRegistrationsResponse", "properties": {"nextPageToken": {"description": "If empty, the list is complete. If nonempty, pass the token to the next request's `ListPredictionApiKeysRegistrationsRequest.pageToken`.", "type": "string"}, "predictionApiKeyRegistrations": {"description": "The list of registered API keys.", "items": {"$ref": "GoogleCloudRecommendationengineV1beta1PredictionApiKeyRegistration"}, "type": "array"}}, "type": "object"}, "GoogleCloudRecommendationengineV1beta1ListUserEventsResponse": {"description": "Response message for ListUserEvents method.", "id": "GoogleCloudRecommendationengineV1beta1ListUserEventsResponse", "properties": {"nextPageToken": {"description": "If empty, the list is complete. If nonempty, the token to pass to the next request's ListUserEvents.page_token.", "type": "string"}, "userEvents": {"description": "The user events.", "items": {"$ref": "GoogleCloudRecommendationengineV1beta1UserEvent"}, "type": "array"}}, "type": "object"}, "GoogleCloudRecommendationengineV1beta1PredictRequest": {"description": "Request message for Predict method. Full resource name of the format: `{name=projects/*/locations/global/catalogs/default_catalog/eventStores/default_event_store/placements/*}` The id of the recommendation engine placement. This id is used to identify the set of models that will be used to make the prediction. We currently support three placements with the following IDs by default: // * `shopping_cart`: Predicts items frequently bought together with one or more catalog items in the same shopping session. Commonly displayed after `add-to-cart` event, on product detail pages, or on the shopping cart page. * `home_page`: Predicts the next product that a user will most likely engage with or purchase based on the shopping or viewing history of the specified `userId` or `visitorId`. For example - Recommendations for you. * `product_detail`: Predicts the next product that a user will most likely engage with or purchase. The prediction is based on the shopping or viewing history of the specified `userId` or `visitorId` and its relevance to a specified `CatalogItem`. Typically used on product detail pages. For example - More items like this. * `recently_viewed_default`: Returns up to 75 items recently viewed by the specified `userId` or `visitorId`, most recent ones first. Returns nothing if neither of them has viewed any items yet. For example - Recently viewed. The full list of available placements can be seen at https://console.cloud.google.com/recommendation/catalogs/default_catalog/placements", "id": "GoogleCloudRecommendationengineV1beta1PredictRequest", "properties": {"dryRun": {"description": "Optional. Use dryRun mode for this prediction query. If set to true, a fake model will be used that returns arbitrary catalog items. Note that the dryRun mode should only be used for testing the API, or if the model is not ready.", "type": "boolean"}, "filter": {"description": "Optional. Filter for restricting prediction results. Accepts values for tags and the `filterOutOfStockItems` flag. * Tag expressions. Restricts predictions to items that match all of the specified tags. Boolean operators `OR` and `NOT` are supported if the expression is enclosed in parentheses, and must be separated from the tag values by a space. `-\"tagA\"` is also supported and is equivalent to `NOT \"tagA\"`. Tag values must be double quoted UTF-8 encoded strings with a size limit of 1 KiB. * filterOutOfStockItems. Restricts predictions to items that do not have a stockState value of OUT_OF_STOCK. Examples: * tag=(\"Red\" OR \"Blue\") tag=\"New-Arrival\" tag=(NOT \"promotional\") * filterOutOfStockItems tag=(-\"promotional\") * filterOutOfStockItems If your filter blocks all prediction results, nothing will be returned. If you want generic (unfiltered) popular items to be returned instead, set `strictFiltering` to false in `PredictRequest.params`.", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. The labels for the predict request. * Label keys can contain lowercase letters, digits and hyphens, must start with a letter, and must end with a letter or digit. * Non-zero label values can contain lowercase letters, digits and hyphens, must start with a letter, and must end with a letter or digit. * No more than 64 labels can be associated with a given request. See https://goo.gl/xmQnxf for more information on and examples of labels.", "type": "object"}, "pageSize": {"description": "Optional. Maximum number of results to return per page. Set this property to the number of prediction results required. If zero, the service will choose a reasonable default.", "format": "int32", "type": "integer"}, "pageToken": {"description": "Optional. The previous PredictResponse.next_page_token.", "type": "string"}, "params": {"additionalProperties": {"type": "any"}, "description": "Optional. Additional domain specific parameters for the predictions. Allowed values: * `returnCatalogItem`: Boolean. If set to true, the associated catalogItem object will be returned in the `PredictResponse.PredictionResult.itemMetadata` object in the method response. * `returnItemScore`: Boolean. If set to true, the prediction 'score' corresponding to each returned item will be set in the `metadata` field in the prediction response. The given 'score' indicates the probability of an item being clicked/purchased given the user's context and history. * `strictFiltering`: Boolean. True by default. If set to false, the service will return generic (unfiltered) popular items instead of empty if your filter blocks all prediction results. * `priceRerankLevel`: String. Default empty. If set to be non-empty, then it needs to be one of {'no-price-reranking', 'low-price-reranking', 'medium-price-reranking', 'high-price-reranking'}. This gives request level control and adjust prediction results based on product price. * `diversityLevel`: String. Default empty. If set to be non-empty, then it needs to be one of {'no-diversity', 'low-diversity', 'medium-diversity', 'high-diversity', 'auto-diversity'}. This gives request level control and adjust prediction results based on product category.", "type": "object"}, "userEvent": {"$ref": "GoogleCloudRecommendationengineV1beta1UserEvent", "description": "Required. Context about the user, what they are looking at and what action they took to trigger the predict request. Note that this user event detail won't be ingested to userEvent logs. Thus, a separate userEvent write request is required for event logging. Don't set UserInfo.visitor_id or UserInfo.user_id to the same fixed ID for different users. If you are trying to receive non-personalized recommendations (not recommended; this can negatively impact model performance), instead set UserInfo.visitor_id to a random unique ID and leave UserInfo.user_id unset."}}, "type": "object"}, "GoogleCloudRecommendationengineV1beta1PredictResponse": {"description": "Response message for predict method.", "id": "GoogleCloudRecommendationengineV1beta1PredictResponse", "properties": {"dryRun": {"description": "True if the dryRun property was set in the request.", "type": "boolean"}, "itemsMissingInCatalog": {"description": "IDs of items in the request that were missing from the catalog.", "items": {"type": "string"}, "type": "array"}, "metadata": {"additionalProperties": {"type": "any"}, "description": "Additional domain specific prediction response metadata.", "type": "object"}, "nextPageToken": {"description": "If empty, the list is complete. If nonempty, the token to pass to the next request's PredictRequest.page_token.", "type": "string"}, "recommendationToken": {"description": "A unique recommendation token. This should be included in the user event logs resulting from this recommendation, which enables accurate attribution of recommendation model performance.", "type": "string"}, "results": {"description": "A list of recommended items. The order represents the ranking (from the most relevant item to the least).", "items": {"$ref": "GoogleCloudRecommendationengineV1beta1PredictResponsePredictionResult"}, "type": "array"}}, "type": "object"}, "GoogleCloudRecommendationengineV1beta1PredictResponsePredictionResult": {"description": "PredictionResult represents the recommendation prediction results.", "id": "GoogleCloudRecommendationengineV1beta1PredictResponsePredictionResult", "properties": {"id": {"description": "ID of the recommended catalog item", "type": "string"}, "itemMetadata": {"additionalProperties": {"type": "any"}, "description": "Additional item metadata / annotations. Possible values: * `catalogItem`: JSON representation of the catalogItem. Will be set if `returnCatalogItem` is set to true in `PredictRequest.params`. * `score`: Prediction score in double value. Will be set if `returnItemScore` is set to true in `PredictRequest.params`.", "type": "object"}}, "type": "object"}, "GoogleCloudRecommendationengineV1beta1PredictionApiKeyRegistration": {"description": "Registered Api Key.", "id": "GoogleCloudRecommendationengineV1beta1PredictionApiKeyRegistration", "properties": {"apiKey": {"description": "The API key.", "type": "string"}}, "type": "object"}, "GoogleCloudRecommendationengineV1beta1ProductCatalogItem": {"description": "ProductCatalogItem captures item metadata specific to retail products.", "id": "GoogleCloudRecommendationengineV1beta1ProductCatalogItem", "properties": {"availableQuantity": {"description": "Optional. The available quantity of the item.", "format": "int64", "type": "string"}, "canonicalProductUri": {"description": "Optional. Canonical URL directly linking to the item detail page with a length limit of 5 KiB..", "type": "string"}, "costs": {"additionalProperties": {"format": "float", "type": "number"}, "description": "Optional. A map to pass the costs associated with the product. For example: {\"manufacturing\": 45.5} The profit of selling this item is computed like so: * If 'exactPrice' is provided, profit = displayPrice - sum(costs) * If 'priceRange' is provided, profit = minPrice - sum(costs)", "type": "object"}, "currencyCode": {"description": "Optional. Only required if the price is set. Currency code for price/costs. Use three-character ISO-4217 code.", "type": "string"}, "exactPrice": {"$ref": "GoogleCloudRecommendationengineV1beta1ProductCatalogItemExactPrice", "description": "Optional. The exact product price."}, "images": {"description": "Optional. Product images for the catalog item.", "items": {"$ref": "GoogleCloudRecommendationengineV1beta1Image"}, "type": "array"}, "priceRange": {"$ref": "GoogleCloudRecommendationengineV1beta1ProductCatalogItemPriceRange", "description": "Optional. The product price range."}, "stockState": {"description": "Optional. Online stock state of the catalog item. Default is `IN_STOCK`.", "enum": ["STOCK_STATE_UNSPECIFIED", "IN_STOCK", "OUT_OF_STOCK", "PREORDER", "BACKORDER"], "enumDescriptions": ["Default item stock status. Should never be used.", "Item in stock.", "Item out of stock.", "Item that is in pre-order state.", "Item that is back-ordered (i.e. temporarily out of stock)."], "type": "string"}}, "type": "object"}, "GoogleCloudRecommendationengineV1beta1ProductCatalogItemExactPrice": {"description": "Exact product price.", "id": "GoogleCloudRecommendationengineV1beta1ProductCatalogItemExactPrice", "properties": {"displayPrice": {"description": "Optional. Display price of the product.", "format": "float", "type": "number"}, "originalPrice": {"description": "Optional. Price of the product without any discount. If zero, by default set to be the 'displayPrice'.", "format": "float", "type": "number"}}, "type": "object"}, "GoogleCloudRecommendationengineV1beta1ProductCatalogItemPriceRange": {"description": "Product price range when there are a range of prices for different variations of the same product.", "id": "GoogleCloudRecommendationengineV1beta1ProductCatalogItemPriceRange", "properties": {"max": {"description": "Required. The maximum product price.", "format": "float", "type": "number"}, "min": {"description": "Required. The minimum product price.", "format": "float", "type": "number"}}, "type": "object"}, "GoogleCloudRecommendationengineV1beta1ProductDetail": {"description": "Detailed product information associated with a user event.", "id": "GoogleCloudRecommendationengineV1beta1ProductDetail", "properties": {"availableQuantity": {"description": "Optional. Quantity of the products in stock when a user event happens. Optional. If provided, this overrides the available quantity in Catalog for this event. and can only be set if `stock_status` is set to `IN_STOCK`. Note that if an item is out of stock, you must set the `stock_state` field to be `OUT_OF_STOCK`. Leaving this field unspecified / as zero is not sufficient to mark the item out of stock.", "format": "int32", "type": "integer"}, "currencyCode": {"description": "Optional. Currency code for price/costs. Use three-character ISO-4217 code. Required only if originalPrice or displayPrice is set.", "type": "string"}, "displayPrice": {"description": "Optional. Display price of the product (e.g. discounted price). If provided, this will override the display price in Catalog for this product.", "format": "float", "type": "number"}, "id": {"description": "Required. Catalog item ID. UTF-8 encoded string with a length limit of 128 characters.", "type": "string"}, "itemAttributes": {"$ref": "GoogleCloudRecommendationengineV1beta1FeatureMap", "description": "Optional. Extra features associated with a product in the user event."}, "originalPrice": {"description": "Optional. Original price of the product. If provided, this will override the original price in Catalog for this product.", "format": "float", "type": "number"}, "quantity": {"description": "Optional. Quantity of the product associated with the user event. For example, this field will be 2 if two products are added to the shopping cart for `add-to-cart` event. Required for `add-to-cart`, `add-to-list`, `remove-from-cart`, `checkout-start`, `purchase-complete`, `refund` event types.", "format": "int32", "type": "integer"}, "stockState": {"description": "Optional. Item stock state. If provided, this overrides the stock state in Catalog for items in this event.", "enum": ["STOCK_STATE_UNSPECIFIED", "IN_STOCK", "OUT_OF_STOCK", "PREORDER", "BACKORDER"], "enumDescriptions": ["Default item stock status. Should never be used.", "Item in stock.", "Item out of stock.", "Item that is in pre-order state.", "Item that is back-ordered (i.e. temporarily out of stock)."], "type": "string"}}, "type": "object"}, "GoogleCloudRecommendationengineV1beta1ProductEventDetail": {"description": "ProductEventDetail captures user event information specific to retail products.", "id": "GoogleCloudRecommendationengineV1beta1ProductEventDetail", "properties": {"cartId": {"description": "Optional. The id or name of the associated shopping cart. This id is used to associate multiple items added or present in the cart before purchase. This can only be set for `add-to-cart`, `remove-from-cart`, `checkout-start`, `purchase-complete`, or `shopping-cart-page-view` events.", "type": "string"}, "listId": {"description": "Required for `add-to-list` and `remove-from-list` events. The id or name of the list that the item is being added to or removed from. Other event types should not set this field.", "type": "string"}, "pageCategories": {"description": "Required for `category-page-view` events. At least one of search_query or page_categories is required for `search` events. Other event types should not set this field. The categories associated with a category page. Category pages include special pages such as sales or promotions. For instance, a special sale page may have the category hierarchy: categories : [\"Sales\", \"2017 Black Friday Deals\"].", "items": {"$ref": "GoogleCloudRecommendationengineV1beta1CatalogItemCategoryHierarchy"}, "type": "array"}, "productDetails": {"description": "The main product details related to the event. This field is required for the following event types: * `add-to-cart` * `add-to-list` * `checkout-start` * `detail-page-view` * `purchase-complete` * `refund` * `remove-from-cart` * `remove-from-list` This field is optional for the following event types: * `page-visit` * `shopping-cart-page-view` - note that 'product_details' should be set for this unless the shopping cart is empty. * `search` (highly encouraged) In a `search` event, this field represents the products returned to the end user on the current page (the end user may have not finished broswing the whole page yet). When a new page is returned to the end user, after pagination/filtering/ordering even for the same query, a new SEARCH event with different product_details is desired. The end user may have not finished broswing the whole page yet. This field is not allowed for the following event types: * `category-page-view` * `home-page-view`", "items": {"$ref": "GoogleCloudRecommendationengineV1beta1ProductDetail"}, "type": "array"}, "purchaseTransaction": {"$ref": "GoogleCloudRecommendationengineV1beta1PurchaseTransaction", "description": "Optional. A transaction represents the entire purchase transaction. Required for `purchase-complete` events. Optional for `checkout-start` events. Other event types should not set this field."}, "searchQuery": {"description": "At least one of search_query or page_categories is required for `search` events. Other event types should not set this field. The user's search query as UTF-8 encoded text with a length limit of 5 KiB.", "type": "string"}}, "type": "object"}, "GoogleCloudRecommendationengineV1beta1PurchaseTransaction": {"description": "A transaction represents the entire purchase transaction.", "id": "GoogleCloudRecommendationengineV1beta1PurchaseTransaction", "properties": {"costs": {"additionalProperties": {"format": "float", "type": "number"}, "description": "Optional. All the costs associated with the product. These can be manufacturing costs, shipping expenses not borne by the end user, or any other costs. Total product cost such that profit = revenue - (sum(taxes) + sum(costs)) If product_cost is not set, then profit = revenue - tax - shipping - sum(CatalogItem.costs). If CatalogItem.cost is not specified for one of the items, CatalogItem.cost based profit *cannot* be calculated for this Transaction.", "type": "object"}, "currencyCode": {"description": "Required. Currency code. Use three-character ISO-4217 code. This field is not required if the event type is `refund`.", "type": "string"}, "id": {"description": "Optional. The transaction ID with a length limit of 128 bytes.", "type": "string"}, "revenue": {"description": "Required. Total revenue or grand total associated with the transaction. This value include shipping, tax, or other adjustments to total revenue that you want to include as part of your revenue calculations. This field is not required if the event type is `refund`.", "format": "float", "type": "number"}, "taxes": {"additionalProperties": {"format": "float", "type": "number"}, "description": "Optional. All the taxes associated with the transaction.", "type": "object"}}, "type": "object"}, "GoogleCloudRecommendationengineV1beta1PurgeUserEventsMetadata": {"description": "Metadata related to the progress of the PurgeUserEvents operation. This will be returned by the google.longrunning.Operation.metadata field.", "id": "GoogleCloudRecommendationengineV1beta1PurgeUserEventsMetadata", "properties": {"createTime": {"description": "Operation create time.", "format": "google-datetime", "type": "string"}, "operationName": {"description": "The ID of the request / operation.", "type": "string"}}, "type": "object"}, "GoogleCloudRecommendationengineV1beta1PurgeUserEventsRequest": {"description": "Request message for PurgeUserEvents method.", "id": "GoogleCloudRecommendationengineV1beta1PurgeUserEventsRequest", "properties": {"filter": {"description": "Required. The filter string to specify the events to be deleted. Empty string filter is not allowed. The eligible fields for filtering are: * `eventType`: UserEvent.eventType field of type string. * `eventTime`: in ISO 8601 \"zulu\" format. * `visitorId`: field of type string. Specifying this will delete all events associated with a visitor. * `userId`: field of type string. Specifying this will delete all events associated with a user. Examples: * Deleting all events in a time range: `eventTime > \"2012-04-23T18:25:43.511Z\" eventTime < \"2012-04-23T18:30:43.511Z\"` * Deleting specific eventType in time range: `eventTime > \"2012-04-23T18:25:43.511Z\" eventType = \"detail-page-view\"` * Deleting all events for a specific visitor: `visitorId = \"visitor1024\"` The filtering fields are assumed to have an implicit AND.", "type": "string"}, "force": {"description": "Optional. The default value is false. Override this flag to true to actually perform the purge. If the field is not set to true, a sampling of events to be deleted will be returned.", "type": "boolean"}}, "type": "object"}, "GoogleCloudRecommendationengineV1beta1PurgeUserEventsResponse": {"description": "Response of the PurgeUserEventsRequest. If the long running operation is successfully done, then this message is returned by the google.longrunning.Operations.response field.", "id": "GoogleCloudRecommendationengineV1beta1PurgeUserEventsResponse", "properties": {"purgedEventsCount": {"description": "The total count of events purged as a result of the operation.", "format": "int64", "type": "string"}, "userEventsSample": {"description": "A sampling of events deleted (or will be deleted) depending on the `force` property in the request. Max of 500 items will be returned.", "items": {"$ref": "GoogleCloudRecommendationengineV1beta1UserEvent"}, "type": "array"}}, "type": "object"}, "GoogleCloudRecommendationengineV1beta1RejoinUserEventsMetadata": {"description": "Metadata for RejoinUserEvents method.", "id": "GoogleCloudRecommendationengineV1beta1RejoinUserEventsMetadata", "properties": {}, "type": "object"}, "GoogleCloudRecommendationengineV1beta1RejoinUserEventsRequest": {"description": "Request message for CatalogRejoin method.", "id": "GoogleCloudRecommendationengineV1beta1RejoinUserEventsRequest", "properties": {"userEventRejoinScope": {"description": "Required. The type of the catalog rejoin to define the scope and range of the user events to be rejoined with catalog items.", "enum": ["USER_EVENT_REJOIN_SCOPE_UNSPECIFIED", "JOINED_EVENTS", "UNJOINED_EVENTS"], "enumDescriptions": ["Rejoin catalogs with all events including both joined events and unjoined events.", "Only rejoin catalogs with joined events.", "Only rejoin catalogs with unjoined events."], "type": "string"}}, "type": "object"}, "GoogleCloudRecommendationengineV1beta1RejoinUserEventsResponse": {"description": "Response message for RejoinUserEvents method.", "id": "GoogleCloudRecommendationengineV1beta1RejoinUserEventsResponse", "properties": {"rejoinedUserEventsCount": {"description": "Number of user events that were joined with latest catalog items.", "format": "int64", "type": "string"}}, "type": "object"}, "GoogleCloudRecommendationengineV1beta1UserEvent": {"description": "UserEvent captures all metadata information recommendation engine needs to know about how end users interact with customers' website.", "id": "GoogleCloudRecommendationengineV1beta1UserEvent", "properties": {"eventDetail": {"$ref": "GoogleCloudRecommendationengineV1beta1EventDetail", "description": "Optional. User event detailed information common across different recommendation types."}, "eventSource": {"description": "Optional. This field should *not* be set when using JavaScript pixel or the Recommendations AI Tag. Defaults to `EVENT_SOURCE_UNSPECIFIED`.", "enum": ["EVENT_SOURCE_UNSPECIFIED", "AUTOML", "ECOMMERCE", "BATCH_UPLOAD"], "enumDescriptions": ["Unspecified event source.", "The event is ingested via a javascript pixel or Recommendations AI Tag through automl datalayer or JS Macros.", "The event is ingested via Recommendations AI Tag through Enhanced Ecommerce datalayer.", "The event is ingested via Import user events API."], "type": "string"}, "eventTime": {"description": "Optional. Only required for ImportUserEvents method. Timestamp of user event created.", "format": "google-datetime", "type": "string"}, "eventType": {"description": "Required. User event type. Allowed values are: * `add-to-cart` Products being added to cart. * `add-to-list` Items being added to a list (shopping list, favorites etc). * `category-page-view` Special pages such as sale or promotion pages viewed. * `checkout-start` User starting a checkout process. * `detail-page-view` Products detail page viewed. * `home-page-view` Homepage viewed. * `page-visit` Generic page visits not included in the event types above. * `purchase-complete` User finishing a purchase. * `refund` Purchased items being refunded or returned. * `remove-from-cart` Products being removed from cart. * `remove-from-list` Items being removed from a list. * `search` Product search. * `shopping-cart-page-view` User viewing a shopping cart. * `impression` List of items displayed. Used by Google Tag Manager.", "type": "string"}, "productEventDetail": {"$ref": "GoogleCloudRecommendationengineV1beta1ProductEventDetail", "description": "Optional. Retail product specific user event metadata. This field is required for the following event types: * `add-to-cart` * `add-to-list` * `category-page-view` * `checkout-start` * `detail-page-view` * `purchase-complete` * `refund` * `remove-from-cart` * `remove-from-list` * `search` This field is optional for the following event types: * `page-visit` * `shopping-cart-page-view` - note that 'product_event_detail' should be set for this unless the shopping cart is empty. This field is not allowed for the following event types: * `home-page-view`"}, "userInfo": {"$ref": "GoogleCloudRecommendationengineV1beta1UserInfo", "description": "Required. User information."}}, "type": "object"}, "GoogleCloudRecommendationengineV1beta1UserEventImportSummary": {"description": "A summary of import result. The UserEventImportSummary summarizes the import status for user events.", "id": "GoogleCloudRecommendationengineV1beta1UserEventImportSummary", "properties": {"joinedEventsCount": {"description": "Count of user events imported with complete existing catalog information.", "format": "int64", "type": "string"}, "unjoinedEventsCount": {"description": "Count of user events imported, but with catalog information not found in the imported catalog.", "format": "int64", "type": "string"}}, "type": "object"}, "GoogleCloudRecommendationengineV1beta1UserEventInlineSource": {"description": "The inline source for the input config for ImportUserEvents method.", "id": "GoogleCloudRecommendationengineV1beta1UserEventInlineSource", "properties": {"userEvents": {"description": "Optional. A list of user events to import. Recommended max of 10k items.", "items": {"$ref": "GoogleCloudRecommendationengineV1beta1UserEvent"}, "type": "array"}}, "type": "object"}, "GoogleCloudRecommendationengineV1beta1UserInfo": {"description": "Information of end users.", "id": "GoogleCloudRecommendationengineV1beta1UserInfo", "properties": {"directUserRequest": {"description": "Optional. Indicates if the request is made directly from the end user in which case the user_agent and ip_address fields can be populated from the HTTP request. This should *not* be set when using the javascript pixel. This flag should be set only if the API request is made directly from the end user such as a mobile app (and not if a gateway or a server is processing and pushing the user events).", "type": "boolean"}, "ipAddress": {"description": "Optional. IP address of the user. This could be either IPv4 (e.g. ************) or IPv6 (e.g. 2001:0db8:85a3:0000:0000:8a2e:0370:7334). This should *not* be set when using the javascript pixel or if `direct_user_request` is set. Used to extract location information for personalization.", "type": "string"}, "userAgent": {"description": "Optional. User agent as included in the HTTP header. UTF-8 encoded string with a length limit of 1 KiB. This should *not* be set when using the JavaScript pixel or if `directUserRequest` is set.", "type": "string"}, "userId": {"description": "Optional. Unique identifier for logged-in user with a length limit of 128 bytes. Required only for logged-in users. Don't set for anonymous users. Don't set the field to the same fixed ID for different users. This mixes the event history of those users together, which results in degraded model quality.", "type": "string"}, "visitorId": {"description": "Required. A unique identifier for tracking visitors with a length limit of 128 bytes. For example, this could be implemented with an HTTP cookie, which should be able to uniquely identify a visitor on a single device. This unique identifier should not change if the visitor logs in or out of the website. Maximum length 128 bytes. Cannot be empty. Don't set the field to the same fixed ID for different users. This mixes the event history of those users together, which results in degraded model quality.", "type": "string"}}, "type": "object"}, "GoogleLongrunningListOperationsResponse": {"description": "The response message for Operations.ListOperations.", "id": "GoogleLongrunningListOperationsResponse", "properties": {"nextPageToken": {"description": "The standard List next-page token.", "type": "string"}, "operations": {"description": "A list of operations that matches the specified filter in the request.", "items": {"$ref": "GoogleLongrunningOperation"}, "type": "array"}}, "type": "object"}, "GoogleLongrunningOperation": {"description": "This resource represents a long-running operation that is the result of a network API call.", "id": "GoogleLongrunningOperation", "properties": {"done": {"description": "If the value is `false`, it means the operation is still in progress. If `true`, the operation is completed, and either `error` or `response` is available.", "type": "boolean"}, "error": {"$ref": "GoogleRpcStatus", "description": "The error result of the operation in case of failure or cancellation."}, "metadata": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata. Any method that returns a long-running operation should document the metadata type, if any.", "type": "object"}, "name": {"description": "The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the `name` should be a resource name ending with `operations/{unique_id}`.", "type": "string"}, "response": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "The normal, successful response of the operation. If the original method returns no data on success, such as `Delete`, the response is `google.protobuf.Empty`. If the original method is standard `Get`/`Create`/`Update`, the response should be the resource. For other methods, the response should have the type `XxxResponse`, where `Xxx` is the original method name. For example, if the original method name is `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.", "type": "object"}}, "type": "object"}, "GoogleProtobufEmpty": {"description": "A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }", "id": "GoogleProtobufEmpty", "properties": {}, "type": "object"}, "GoogleRpcStatus": {"description": "The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).", "id": "GoogleRpcStatus", "properties": {"code": {"description": "The status code, which should be an enum value of google.rpc.Code.", "format": "int32", "type": "integer"}, "details": {"description": "A list of messages that carry the error details. There is a common set of message types for APIs to use.", "items": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "type": "object"}, "type": "array"}, "message": {"description": "A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.", "type": "string"}}, "type": "object"}}, "servicePath": "", "title": "Recommendations AI (Beta)", "version": "v1beta1", "version_module": true}