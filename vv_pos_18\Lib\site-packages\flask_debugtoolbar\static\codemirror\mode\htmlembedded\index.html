<!doctype html>
<html>
  <head>
    <title>CodeMirror: Html Embedded Scripts mode</title>
    <link rel="stylesheet" href="../../lib/codemirror.css">
    <script src="../../lib/codemirror.js"></script>
    <script src="../xml/xml.js"></script>
    <script src="../javascript/javascript.js"></script>
    <script src="../css/css.js"></script>
    <script src="../htmlmixed/htmlmixed.js"></script>
    <script src="htmlembedded.js"></script>
    <style type="text/css">.CodeMirror {border-top: 1px solid black; border-bottom: 1px solid black;}</style>
    <link rel="stylesheet" href="../../doc/docs.css">
  </head>
  <body>
    <h1>CodeMirror: Html Embedded Scripts mode</h1>

<form><textarea id="code" name="code">
<%
function hello(who) {
	return "Hello " + who;
}
%>
This is an example of EJS (embedded javascript)
<p>The program says <%= hello("world") %>.</p>
<script>
	alert("And here is some normal JS code"); // also colored
</script>
</textarea></form>

    <script>
      var editor = CodeMirror.fromTextArea(document.getElementById("code"), {
        lineNumbers: true,
        matchBrackets: true,
        mode: "application/x-ejs",
        indentUnit: 4,
        indentWithTabs: true,
        enterMode: "keep",
        tabMode: "shift"
      });
    </script>

    <p>Mode for html embedded scripts like JSP and ASP.NET. Depends on HtmlMixed which in turn depends on
    JavaScript, CSS and XML.<br />Other dependancies include those of the scriping language chosen.</p>

    <p><strong>MIME types defined:</strong> <code>application/x-aspx</code> (ASP.NET), 
    <code>application/x-ejs</code> (Embedded Javascript), <code>application/x-jsp</code> (JavaServer Pages)</p>
  </body>
</html>
