"""Add item_name to purchase_order_items

Revision ID: ff0992abefee
Revises: cost_history_001
Create Date: 2025-08-18 16:42:16.146502

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'ff0992abefee'
down_revision = 'cost_history_001'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('purchase_order_items', schema=None) as batch_op:
        batch_op.add_column(sa.Column('item_name', sa.String(length=200), nullable=False))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('purchase_order_items', schema=None) as batch_op:
        batch_op.drop_column('item_name')

    # ### end Alembic commands ###
