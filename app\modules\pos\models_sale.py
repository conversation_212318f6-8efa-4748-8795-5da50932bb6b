from app import db
from datetime import datetime
import enum
import random
import string
from app.modules.cash_register.models_cash_register import CashRegister, PaymentMethod, PaymentMethodType

class SaleStatus(enum.Enum):
    PENDING = "pending"
    PAID = "paid"
    CANCELLED = "cancelled"
    VOIDED = "voided"
    KITCHEN_PENDING = "kitchen_pending"
    KITCHEN_READY = "kitchen_ready"
    DELIVERED = "delivered"
    COMPLETED = "completed"
    READY = "ready"  # Adding READY status to fix the linter error

def generate_unique_reference():
    """Génère une référence unique pour une vente"""
    prefix = datetime.utcnow().strftime('%Y%m')
    # Génère une chaîne aléatoire de 6 caractères
    random_str = ''.join(random.choices(string.ascii_uppercase + string.digits, k=6))
    reference = f"{prefix}-{random_str}"

    # Vérifier que la référence n'existe pas déjà
    while Sale.query.filter_by(reference=reference).first() is not None:
        random_str = ''.join(random.choices(string.ascii_uppercase + string.digits, k=6))
        reference = f"{prefix}-{random_str}"

    return reference

class Sale(db.Model):
    __tablename__ = 'sales'
    __table_args__ = (
        db.ForeignKeyConstraint(['customer_id'], ['customers.id'], name='fk_sales_customer'),
        {'extend_existing': True}
    )

    id = db.Column(db.Integer, primary_key=True)
    reference = db.Column(db.String(20), unique=True, nullable=False)
    owner_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    customer_id = db.Column(db.Integer, nullable=True)
    table_number = db.Column(db.String(10))  # Garde pour compatibilité
    table_id = db.Column(db.Integer, db.ForeignKey('tables.id'), nullable=True)  # Nouvelle clé étrangère
    status = db.Column(db.Enum(SaleStatus), default=SaleStatus.PENDING)
    kitchen_status = db.Column(db.String(20), default='pending')

    # Nouveaux champs pour séparer les statuts
    delivery_status = db.Column(db.String(20), default='pending')  # pending, delivered
    payment_status = db.Column(db.String(20), default='pending')   # pending, paid

    # Informations sur la source de la commande
    order_source = db.Column(db.String(20), default='pos')  # pos, online
    online_order_id = db.Column(db.Integer, db.ForeignKey('online_orders.id'), nullable=True)
    customer_user_id = db.Column(db.Integer, db.ForeignKey('customer_users.id'), nullable=True)  # Pour les commandes en ligne

    subtotal = db.Column(db.Float, default=0)
    tax_rate = db.Column(db.Float, default=0)
    tax_amount = db.Column(db.Float, default=0)
    discount_amount = db.Column(db.Float, default=0)
    total = db.Column(db.Float, default=0)
    payment_method = db.Column(PaymentMethodType)
    kitchen_note = db.Column(db.Text)

    # Gestion des couverts et service
    covers_count = db.Column(db.Integer, default=1)  # Nombre de couverts/personnes
    service_type = db.Column(db.String(20), default='dine_in')  # dine_in, takeaway, delivery, drive_thru

    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    paid_at = db.Column(db.DateTime)

    # Relations
    items = db.relationship('SaleItem', backref='sale', lazy='dynamic')
    payments = db.relationship('Payment', backref='sale', lazy='dynamic')
    user = db.relationship('User', foreign_keys=[user_id], backref='sales_as_user')
    owner = db.relationship('User', foreign_keys=[owner_id], backref='sales_as_owner')
    table = db.relationship('Table', foreign_keys=[table_id])

    # Nouvelles relations pour les commandes en ligne
    online_order = db.relationship('OnlineOrder', backref='pos_sale', foreign_keys=[online_order_id])
    customer_user = db.relationship('CustomerUser', backref='pos_sales', foreign_keys=[customer_user_id])

    def __init__(self, *args, **kwargs):
        super(Sale, self).__init__(*args, **kwargs)
        if not self.reference:
            self.reference = generate_unique_reference()

    @property
    def total_ht(self):
        """Total hors taxes"""
        return self.subtotal - self.discount_amount

    @property
    def total_tax(self):
        """Montant total de la TVA"""
        return self.tax_amount

    @property
    def total_ttc(self):
        """Total toutes taxes comprises"""
        return self.total

    @property
    def total_paid(self):
        """Total des paiements déjà effectués"""
        return sum(payment.amount for payment in self.payments)

    @property
    def remaining_amount(self):
        """Montant restant à payer"""
        return max(0, self.total - self.total_paid)

    @property
    def is_partially_paid(self):
        """Vérifie si la vente est partiellement payée"""
        return self.total_paid > 0 and self.remaining_amount > 0

    @property
    def is_fully_paid(self):
        """Vérifie si la vente est entièrement payée"""
        return self.remaining_amount <= 0 and self.total_paid > 0

    @property
    def service_type_display(self):
        """Nom d'affichage du type de service"""
        service_types = {
            'dine_in': 'Sur place',
            'takeaway': 'À emporter',
            'delivery': 'Livraison',
            'drive_thru': 'Service au volant'
        }
        return service_types.get(self.service_type, self.service_type)

    def calculate_totals(self):
        self.subtotal = sum(item.total for item in self.items)
        self.tax_amount = self.subtotal * (self.tax_rate / 100)
        self.total = self.subtotal + self.tax_amount - self.discount_amount
        db.session.commit()

    def add_item(self, product, quantity, price=None):
        if price is None:
            price = product.price

        item = SaleItem(
            sale=self,
            product=product,
            quantity=quantity,
            price=price,
            total=price * quantity
        )
        db.session.add(item)
        self.calculate_totals()

        if product.is_recipe_based and product.recipe:
            product.recipe.use_ingredients(quantity)
        else:
            product.update_stock(quantity)

        return item

    def process_payment(self, amount, method):
        if self.status != SaleStatus.PENDING:
            return False, "La vente n'est pas en attente"

        payment = Payment(
            sale=self,
            amount=amount,
            method=method
        )
        db.session.add(payment)

        if payment.amount >= self.total:
            self.status = SaleStatus.PAID
            self.paid_at = datetime.utcnow()
            self.payment_method = method

            # Enregistrer la vente dans la caisse
            register = CashRegister.get_current(self.owner_id)
            if register:
                success, message = register.add_sale(amount=self.total, sale_id=self.id, user_id=self.user_id, payment_method=method)
                if not success:
                    return False, f"Vente enregistrée mais erreur de caisse: {message}"

        db.session.commit()
        return True, "Paiement traité avec succès"

    def complete_sale(self, user_id):
        """Complete the sale and add it to the cash register"""
        if self.status != SaleStatus.PENDING:
            return False, "La vente n'est pas en attente"

        self.status = SaleStatus.COMPLETED
        self.completed_at = datetime.utcnow()

        # Add the sale to the cash register
        cash_register = CashRegister.query.filter_by(owner_id=self.owner_id).first()
        if cash_register:
            success, message = cash_register.add_sale(self.total, self.id, user_id)
            if not success:
                return False, message

        db.session.commit()
        return True, "Vente complétée"

    def record_cost_history(self):
        """Enregistre l'historique des coûts au moment de la vente"""
        try:
            from app.modules.inventory.services_cost_management import CostManagementService
            CostManagementService.record_cost_for_sale(
                self.items,
                sale_id=self.id,
                reference=self.reference
            )
            return True
        except Exception as e:
            print(f"Erreur lors de l'enregistrement des coûts: {e}")
            return False

    def __repr__(self):
        return f'<Sale {self.reference}>'

class SaleItem(db.Model):
    __tablename__ = 'sale_items'

    id = db.Column(db.Integer, primary_key=True)
    sale_id = db.Column(db.Integer, db.ForeignKey('sales.id'), nullable=False)
    product_id = db.Column(db.Integer, db.ForeignKey('products.id'), nullable=False)
    quantity = db.Column(db.Float, nullable=False)
    price = db.Column(db.Float, nullable=False)
    total = db.Column(db.Float, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Note: La relation 'product' est créée automatiquement via le backref
    # dans Product.sale_items = db.relationship('SaleItem', backref='product', lazy='dynamic')

    def __repr__(self):
        return f'<SaleItem {self.product.name if self.product else "Unknown"} x{self.quantity}>'

class Payment(db.Model):
    __tablename__ = 'payments'

    id = db.Column(db.Integer, primary_key=True)
    sale_id = db.Column(db.Integer, db.ForeignKey('sales.id'), nullable=False)
    amount = db.Column(db.Float, nullable=False)
    method = db.Column(PaymentMethodType, nullable=False)
    reference = db.Column(db.String(50))  # Pour les paiements par carte ou chèque
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def __init__(self, **kwargs):
        # Ignorer les paramètres non supportés pour éviter les erreurs
        supported_params = ['sale_id', 'sale', 'amount', 'method', 'reference', 'created_at']
        filtered_kwargs = {k: v for k, v in kwargs.items() if k in supported_params}
        super(Payment, self).__init__(**filtered_kwargs)

    def __repr__(self):
        return f'<Payment {self.id} for Sale {self.sale_id}>'