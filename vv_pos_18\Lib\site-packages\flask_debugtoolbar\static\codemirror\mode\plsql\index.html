<!doctype html>
<html>
  <head>
    <title>CodeMirror: Oracle PL/SQL mode</title>
    <link rel="stylesheet" href="../../lib/codemirror.css">
    <script src="../../lib/codemirror.js"></script>
    <script src="plsql.js"></script>
    <link rel="stylesheet" href="../../doc/docs.css">
    <style>.CodeMirror {border: 2px inset #dee;}</style>
  </head>
  <body>
    <h1>CodeMirror: Oracle PL/SQL mode</h1>

<form><textarea id="code" name="code">
-- Oracle PL/SQL Code Demo
/*
   based on c-like mode, adapted to PL/SQL by <PERSON> ( http://www.oracle-and-apex.com/ )
   April 2011
*/
DECLARE
    vIdx    NUMBER;
    vString VARCHAR2(100);
    cText   CONSTANT VARCHAR2(100) := 'That''s it! Have fun with CodeMirror 2';
BEGIN
    vIdx := 0;
    --
    FOR rDATA IN
      ( SELECT *
          FROM EMP
         ORDER BY EMPNO
      )
    LOOP
        vIdx    := vIdx + 1;
        vString := rDATA.EMPNO || ' - ' || rDATA.ENAME;
        --
        UPDATE EMP
           SET SAL   = SAL * 101/100
         WHERE EMPNO = rDATA.EMPNO
        ;
    END LOOP;
    --
    SYS.DBMS_OUTPUT.Put_Line (cText);
END;
--
</textarea></form>

    <script>
      var editor = CodeMirror.fromTextArea(document.getElementById("code"), {
        lineNumbers: true,
        matchBrackets: true,
        indentUnit: 4,
        mode: "text/x-plsql"
      });
    </script>

    <p>
        Simple mode that handles Oracle PL/SQL language (and Oracle SQL, of course).
    </p>

    <p><strong>MIME type defined:</strong> <code>text/x-plsql</code>
    (PLSQL code)
</html>
