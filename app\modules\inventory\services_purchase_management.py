"""
Service de gestion des achats et modifications de prix
"""
from app import db
from app.modules.inventory.models_purchase_order import PurchaseOrder, PurchaseOrderItem
from app.modules.inventory.models_product import Product
from app.modules.inventory.models_ingredient import Ingredient
from app.modules.inventory.services_cost_management import CostManagementService
from flask_login import current_user
import json


class PurchaseManagementService:
    """Service pour gérer les achats et les modifications de prix"""
    
    @staticmethod
    def create_purchase_order_from_cart(cart_data, supplier_id=None, notes=None):
        """Crée une commande d'achat à partir des données du panier POS"""
        try:
            # Créer la commande d'achat
            purchase_order = PurchaseOrder(
                supplier_id=supplier_id,
                user_id=current_user.id,
                owner_id=current_user.id,
                notes=notes
            )
            
            db.session.add(purchase_order)
            db.session.flush()  # Pour obtenir l'ID
            
            # Ajouter les articles
            total_amount = 0
            for cart_item in cart_data:
                item_type = cart_item.get('type')
                item_id = cart_item.get('id')
                quantity = float(cart_item.get('quantity', 0))
                unit_price = float(cart_item.get('price', 0))
                original_price = float(cart_item.get('original_price', unit_price))
                
                if item_type == 'product':
                    product = Product.query.get(item_id)
                    if product and product.owner_id == current_user.id:
                        purchase_item = PurchaseOrderItem(
                            purchase_order_id=purchase_order.id,
                            product_id=product.id,
                            quantity=quantity,
                            unit_price=unit_price,
                            original_unit_price=original_price,
                            total_price=quantity * unit_price
                        )
                        db.session.add(purchase_item)
                        total_amount += quantity * unit_price
                
                elif item_type == 'ingredient':
                    ingredient = Ingredient.query.get(item_id)
                    if ingredient and ingredient.owner_id == current_user.id:
                        purchase_item = PurchaseOrderItem(
                            purchase_order_id=purchase_order.id,
                            ingredient_id=ingredient.id,
                            quantity=quantity,
                            unit_price=unit_price,
                            original_unit_price=original_price,
                            total_price=quantity * unit_price
                        )
                        db.session.add(purchase_item)
                        total_amount += quantity * unit_price
            
            purchase_order.subtotal = total_amount
            purchase_order.total_amount = total_amount
            
            db.session.commit()
            return purchase_order
            
        except Exception as e:
            db.session.rollback()
            print(f"Erreur lors de la création de la commande: {e}")
            return None
    
    @staticmethod
    def update_item_price(purchase_order_id, item_id, new_price, item_type='product'):
        """Met à jour le prix d'un article dans une commande d'achat"""
        try:
            purchase_order = PurchaseOrder.query.get(purchase_order_id)
            if not purchase_order or purchase_order.owner_id != current_user.id:
                return False
            
            # Trouver l'article
            purchase_item = None
            for item in purchase_order.items:
                if item_type == 'product' and item.product_id == item_id:
                    purchase_item = item
                    break
                elif item_type == 'ingredient' and item.ingredient_id == item_id:
                    purchase_item = item
                    break
            
            if not purchase_item:
                return False
            
            # Sauvegarder le prix original si ce n'est pas déjà fait
            if not purchase_item.original_unit_price:
                purchase_item.original_unit_price = purchase_item.unit_price
            
            # Mettre à jour le prix
            purchase_item.unit_price = new_price
            purchase_item.total_price = purchase_item.quantity * new_price
            
            # Recalculer les totaux
            purchase_order.calculate_totals()
            
            db.session.commit()
            return True
            
        except Exception as e:
            db.session.rollback()
            print(f"Erreur lors de la mise à jour du prix: {e}")
            return False
    
    @staticmethod
    def apply_global_discount(purchase_order_id, discount_amount, discount_type='amount', apply_to_items=False):
        """Applique une remise globale à une commande d'achat"""
        try:
            purchase_order = PurchaseOrder.query.get(purchase_order_id)
            if not purchase_order or purchase_order.owner_id != current_user.id:
                return False
            
            return CostManagementService.apply_discount_to_purchase_order(
                purchase_order, discount_amount, discount_type, apply_to_items
            )
            
        except Exception as e:
            print(f"Erreur lors de l'application de la remise: {e}")
            return False
    
    @staticmethod
    def receive_merchandise(purchase_order_id, received_items):
        """Marque la marchandise comme reçue et met à jour les stocks et coûts moyens"""
        try:
            purchase_order = PurchaseOrder.query.get(purchase_order_id)
            if not purchase_order or purchase_order.owner_id != current_user.id:
                return False
            
            # Mettre à jour les quantités reçues
            for item_data in received_items:
                item_id = item_data.get('item_id')
                received_qty = float(item_data.get('received_quantity', 0))
                
                purchase_item = PurchaseOrderItem.query.get(item_id)
                if purchase_item and purchase_item.purchase_order_id == purchase_order_id:
                    purchase_item.received_quantity = received_qty
            
            # Mettre à jour les coûts moyens et stocks
            CostManagementService.update_average_cost_from_purchase(purchase_order)
            
            # Marquer la commande comme reçue si tout est reçu
            all_received = all(
                item.received_quantity >= item.quantity 
                for item in purchase_order.items
            )
            
            if all_received:
                from app.modules.inventory.models_purchase_order import PurchaseOrderStatus
                purchase_order.status = PurchaseOrderStatus.RECEIVED
            
            db.session.commit()
            return True
            
        except Exception as e:
            db.session.rollback()
            print(f"Erreur lors de la réception: {e}")
            return False
    
    @staticmethod
    def get_purchase_order_summary(purchase_order_id):
        """Récupère un résumé détaillé d'une commande d'achat"""
        purchase_order = PurchaseOrder.query.get(purchase_order_id)
        if not purchase_order or purchase_order.owner_id != current_user.id:
            return None
        
        items_summary = []
        for item in purchase_order.items:
            item_info = {
                'id': item.id,
                'name': item.item_name,
                'type': item.item_type,
                'quantity': item.quantity,
                'unit_price': item.unit_price,
                'original_unit_price': item.original_unit_price,
                'total_price': item.total_price,
                'received_quantity': item.received_quantity,
                'discount_amount': item.discount_amount or 0,
                'current_stock': item.current_stock
            }
            
            # Ajouter les informations de coût moyen actuel
            if item.product:
                item_info['current_average_cost'] = item.product.get_current_cost()
                item_info['reference_price'] = item.product.cost_price
            elif item.ingredient:
                item_info['current_average_cost'] = item.ingredient.get_current_cost()
                item_info['reference_price'] = item.ingredient.price_per_unit
            
            items_summary.append(item_info)
        
        return {
            'purchase_order': purchase_order,
            'items': items_summary,
            'total_items': len(items_summary),
            'total_quantity': sum(item['quantity'] for item in items_summary),
            'subtotal': purchase_order.subtotal,
            'discount_amount': purchase_order.discount_amount or 0,
            'discount_type': purchase_order.discount_type,
            'total_amount': purchase_order.total_amount
        }
    
    @staticmethod
    def get_price_modification_suggestions(item_id, item_type):
        """Suggère des modifications de prix basées sur l'historique"""
        try:
            # Récupérer l'analyse des coûts
            cost_analysis = CostManagementService.get_cost_analysis(item_id, item_type, days=90)
            
            if item_type == 'product':
                item = Product.query.get(item_id)
                current_reference = item.cost_price if item else 0
            else:
                item = Ingredient.query.get(item_id)
                current_reference = item.price_per_unit if item else 0
            
            suggestions = {
                'current_reference_price': current_reference,
                'current_average_cost': item.get_current_cost() if item else 0,
                'historical_average': cost_analysis['average_cost'],
                'historical_min': cost_analysis['min_cost'],
                'historical_max': cost_analysis['max_cost'],
                'suggested_ranges': {
                    'conservative': current_reference * 0.95,  # -5%
                    'moderate': current_reference * 1.05,     # +5%
                    'aggressive': current_reference * 1.15    # +15%
                }
            }
            
            return suggestions
            
        except Exception as e:
            print(f"Erreur lors de la génération des suggestions: {e}")
            return None
