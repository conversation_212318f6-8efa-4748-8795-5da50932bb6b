"""add_average_cost_and_discount_fields

Revision ID: 9da43905a5b9
Revises: c61d2e3891bd
Create Date: 2025-08-17 13:44:35.308177

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import sqlite

# revision identifiers, used by Alembic.
revision = '9da43905a5b9'
down_revision = 'c61d2e3891bd'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('_alembic_tmp_cash_operations')
    with op.batch_alter_table('cash_operations', schema=None) as batch_op:
        batch_op.alter_column('type',
               existing_type=sa.VARCHAR(length=12),
               type_=sa.Enum('OPENING', 'CLOSING', 'CASH_IN', 'CASH_OUT', 'SALE', 'BANK_DEPOSIT', 'SUPPLIER_PAYMENT', name='cashregisteroperationtype'),
               existing_nullable=False)

    with op.batch_alter_table('ingredients', schema=None) as batch_op:
        batch_op.add_column(sa.Column('average_cost', sa.Float(), nullable=True))
        batch_op.add_column(sa.Column('updated_at', sa.DateTime(), nullable=True))

    with op.batch_alter_table('products', schema=None) as batch_op:
        batch_op.add_column(sa.Column('average_cost', sa.Float(), nullable=True))

    with op.batch_alter_table('purchase_order_items', schema=None) as batch_op:
        batch_op.add_column(sa.Column('original_unit_price', sa.Float(), nullable=True))
        batch_op.add_column(sa.Column('discount_amount', sa.Float(), nullable=True))

    with op.batch_alter_table('purchase_orders', schema=None) as batch_op:
        batch_op.add_column(sa.Column('discount_amount', sa.Float(), nullable=True))
        batch_op.add_column(sa.Column('discount_type', sa.String(length=20), nullable=True))
        batch_op.add_column(sa.Column('apply_discount_to_items', sa.Boolean(), nullable=True))

    with op.batch_alter_table('suppliers', schema=None) as batch_op:
        batch_op.create_foreign_key('fk_suppliers_category_id', 'supplier_categories', ['category_id'], ['id'])

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('suppliers', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')

    with op.batch_alter_table('purchase_orders', schema=None) as batch_op:
        batch_op.drop_column('apply_discount_to_items')
        batch_op.drop_column('discount_type')
        batch_op.drop_column('discount_amount')

    with op.batch_alter_table('purchase_order_items', schema=None) as batch_op:
        batch_op.drop_column('discount_amount')
        batch_op.drop_column('original_unit_price')

    with op.batch_alter_table('products', schema=None) as batch_op:
        batch_op.drop_column('average_cost')

    with op.batch_alter_table('ingredients', schema=None) as batch_op:
        batch_op.drop_column('updated_at')
        batch_op.drop_column('average_cost')

    with op.batch_alter_table('cash_operations', schema=None) as batch_op:
        batch_op.alter_column('type',
               existing_type=sa.Enum('OPENING', 'CLOSING', 'CASH_IN', 'CASH_OUT', 'SALE', 'BANK_DEPOSIT', 'SUPPLIER_PAYMENT', name='cashregisteroperationtype'),
               type_=sa.VARCHAR(length=12),
               existing_nullable=False)

    op.create_table('_alembic_tmp_cash_operations',
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('register_id', sa.INTEGER(), nullable=False),
    sa.Column('owner_id', sa.INTEGER(), nullable=False),
    sa.Column('user_id', sa.INTEGER(), nullable=False),
    sa.Column('type', sa.VARCHAR(length=16), nullable=False),
    sa.Column('amount', sa.FLOAT(), nullable=False),
    sa.Column('initial_amount', sa.FLOAT(), nullable=True),
    sa.Column('final_amount', sa.FLOAT(), nullable=True),
    sa.Column('reason', sa.VARCHAR(length=50), nullable=True),
    sa.Column('source', sa.VARCHAR(length=50), nullable=True),
    sa.Column('payment_method', sa.VARCHAR(), nullable=True),
    sa.Column('note', sa.TEXT(), nullable=True),
    sa.Column('table_number', sa.VARCHAR(length=10), nullable=True),
    sa.Column('linked_objects', sqlite.JSON(), nullable=True),
    sa.Column('created_at', sa.DATETIME(), nullable=True),
    sa.Column('date', sa.DATETIME(), nullable=True),
    sa.ForeignKeyConstraint(['owner_id'], ['users.id'], ),
    sa.ForeignKeyConstraint(['register_id'], ['cash_register.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###
