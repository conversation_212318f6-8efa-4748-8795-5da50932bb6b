from .. import Provider as PhoneNumberProvider


class Provider(PhoneNumberProvider):
    # as per https://en.wikipedia.org/wiki/Telephone_numbers_in_Thailand
    formats = (
        # landline (9 digits, starts with 02, 03, 04, 05, or 07)
        "+66 2### ####",
        "+662 ### ####",
        "+66 (0) 2### ####",
        "02#######",
        "0 2### ####",
        "02# ######",
        "02#-######",
        "0-2###-####",
        "02 ### ####",
        "+66 3### ####",
        "+663 ### ####",
        "+66 (0) 3### ####",
        "03#######",
        "0 3### ####",
        "03# ######",
        "03#-######",
        "0-3###-####",
        "03 ### ####",
        "+66 4### ####",
        "+664 ### ####",
        "+66 (0) 4### ####",
        "04#######",
        "0 4### ####",
        "04# ######",
        "04#-######",
        "0-4###-####",
        "04 ### ####",
        "+66 5### ####",
        "+665 ### ####",
        "+66 (0) 5### ####",
        "05#######",
        "0 5### ####",
        "05# ######",
        "05#-######",
        "0-5###-####",
        "05 ### ####",
        "+66 7### ####",
        "+667 ### ####",
        "+66 (0) 7### ####",
        "07#######",
        "0 7### ####",
        "07# ######",
        "07#-######",
        "0-7###-####",
        "07 ### ####",
        # mobile (10 digits, starts with 06, 08, or 09)
        "+66 6## ### ###",
        "+66 (0) 6## ### ###",
        "06########",
        "0 6## ### ###",
        "06# ### ####",
        "06#-###-####",
        "+66 8## ### ###",
        "+66 (0) 8## ### ###",
        "08########",
        "0 8## ### ###",
        "08# ### ####",
        "08#-###-####",
        "+66 9## ### ###",
        "+66 (0) 9## ### ###",
        "09########",
        "0 9## ### ###",
        "09# ### ####",
        "09#-###-####",
    )
