from app import db
from datetime import datetime

class CostHistory(db.Model):
    """Historique des coûts moyens pour les calculs de rentabilité"""
    __tablename__ = 'cost_history'
    
    id = db.Column(db.Integer, primary_key=True)
    
    # L'article peut être soit un produit soit un ingrédient
    product_id = db.Column(db.In<PERSON>ger, db.<PERSON>ey('products.id'), nullable=True)
    ingredient_id = db.Column(db.Integer, db.ForeignKey('ingredients.id'), nullable=True)
    
    # Coûts au moment de l'enregistrement
    cost_price = db.Column(db.Float, nullable=False)  # Prix d'achat référentiel
    average_cost = db.Column(db.Float, nullable=False)  # Coût moyen au moment de la vente
    
    # Référence de la vente ou transaction
    sale_id = db.Column(db.Integer, db.<PERSON>('sales.id'), nullable=True)
    online_order_id = db.Column(db.Integer, nullable=True)  # Pour les commandes en ligne
    reference = db.Column(db.String(50))  # Référence générale
    
    # Métadonnées
    owner_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relations
    product = db.relationship('Product', backref='cost_history')
    ingredient = db.relationship('Ingredient', backref='cost_history')
    sale = db.relationship('Sale', backref='cost_history')
    owner = db.relationship('User', backref='cost_history')
    
    def __repr__(self):
        item_name = self.product.name if self.product else self.ingredient.name
        return f'<CostHistory {item_name} - {self.average_cost}>'
    
    @property
    def item_name(self):
        """Nom de l'article (produit ou ingrédient)"""
        return self.product.name if self.product else self.ingredient.name
    
    @property
    def item_type(self):
        """Type d'article (product ou ingredient)"""
        return 'product' if self.product else 'ingredient'
    
    @classmethod
    def record_cost_for_sale(cls, item, sale_id=None, online_order_id=None, reference=None, owner_id=None):
        """Enregistre le coût moyen au moment d'une vente"""
        if not item:
            return None
        
        # Déterminer le type d'article
        product_id = item.id if hasattr(item, 'has_recipe') else None
        ingredient_id = item.id if not hasattr(item, 'has_recipe') else None
        
        cost_history = cls(
            product_id=product_id,
            ingredient_id=ingredient_id,
            cost_price=item.cost_price if hasattr(item, 'cost_price') else item.price_per_unit,
            average_cost=item.get_current_cost(),
            sale_id=sale_id,
            online_order_id=online_order_id,
            reference=reference,
            owner_id=owner_id or item.owner_id
        )
        
        db.session.add(cost_history)
        return cost_history
