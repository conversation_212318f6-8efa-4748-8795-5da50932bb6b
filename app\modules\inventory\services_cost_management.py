"""
Service de gestion des coûts moyens pour les produits et ingrédients
"""
from app import db
from app.modules.inventory.models_cost_history import CostHistory
from app.modules.inventory.models_product import Product
from app.modules.inventory.models_ingredient import Ingredient
from app.modules.inventory.models_purchase_order import Purchase<PERSON><PERSON><PERSON>, PurchaseOrderItem
from datetime import datetime
from flask_login import current_user


class CostManagementService:
    """Service pour gérer les coûts moyens et l'historique des coûts"""
    
    @staticmethod
    def update_average_cost_from_purchase(purchase_order):
        """Met à jour les coûts moyens lors de la réception d'une commande d'achat"""
        if not purchase_order or not purchase_order.items:
            return False
        
        try:
            for item in purchase_order.items:
                if item.received_quantity > 0:
                    CostManagementService._update_item_average_cost(item)
            
            db.session.commit()
            return True
        except Exception as e:
            db.session.rollback()
            print(f"Erreur lors de la mise à jour des coûts moyens: {e}")
            return False
    
    @staticmethod
    def _update_item_average_cost(purchase_item):
        """Met à jour le coût moyen d'un article spécifique"""
        if purchase_item.product and not purchase_item.product.has_recipe:
            # Produit sans recette
            product = purchase_item.product
            product.update_average_cost(purchase_item.received_quantity, purchase_item.unit_price)
            product.stock_quantity += purchase_item.received_quantity
            
        elif purchase_item.ingredient:
            # Ingrédient
            ingredient = purchase_item.ingredient
            ingredient.update_average_cost(purchase_item.received_quantity, purchase_item.unit_price)
            ingredient.stock_quantity += purchase_item.received_quantity
    
    @staticmethod
    def record_cost_for_sale(sale_items, sale_id=None, online_order_id=None, reference=None):
        """Enregistre les coûts moyens au moment d'une vente pour l'historique"""
        try:
            for sale_item in sale_items:
                if hasattr(sale_item, 'product') and sale_item.product:
                    product = sale_item.product
                    if product.has_recipe:
                        # Pour les produits avec recette, enregistrer le coût des ingrédients
                        CostManagementService._record_recipe_costs(
                            product, sale_item.quantity, sale_id, online_order_id, reference
                        )
                    else:
                        # Produit simple
                        CostHistory.record_cost_for_sale(
                            product, sale_id, online_order_id, reference, current_user.id
                        )
            
            db.session.commit()
            return True
        except Exception as e:
            db.session.rollback()
            print(f"Erreur lors de l'enregistrement des coûts: {e}")
            return False
    
    @staticmethod
    def _record_recipe_costs(product, quantity_sold, sale_id, online_order_id, reference):
        """Enregistre les coûts des ingrédients pour un produit avec recette"""
        if not product.recipe or not product.recipe.items:
            return
        
        for recipe_item in product.recipe.items:
            ingredient = recipe_item.ingredient
            ingredient_quantity_used = recipe_item.quantity * quantity_sold
            
            # Créer un enregistrement d'historique pour chaque ingrédient utilisé
            cost_history = CostHistory(
                ingredient_id=ingredient.id,
                cost_price=ingredient.price_per_unit,
                average_cost=ingredient.get_current_cost(),
                sale_id=sale_id,
                online_order_id=online_order_id,
                reference=f"{reference}_recipe_{product.name}",
                owner_id=current_user.id
            )
            db.session.add(cost_history)
    
    @staticmethod
    def calculate_sale_cost(sale_items):
        """Calcule le coût total d'une vente basé sur les coûts moyens actuels"""
        total_cost = 0
        
        for sale_item in sale_items:
            if hasattr(sale_item, 'product') and sale_item.product:
                product = sale_item.product
                if product.has_recipe:
                    # Coût basé sur la recette
                    item_cost = product.calculate_recipe_cost() * sale_item.quantity
                else:
                    # Coût basé sur le coût moyen
                    item_cost = product.get_current_cost() * sale_item.quantity
                
                total_cost += item_cost
        
        return total_cost
    
    @staticmethod
    def get_historical_cost(item_id, item_type, sale_id=None, online_order_id=None):
        """Récupère le coût historique d'un article pour une vente spécifique"""
        query = CostHistory.query.filter_by(owner_id=current_user.id)
        
        if item_type == 'product':
            query = query.filter_by(product_id=item_id)
        else:
            query = query.filter_by(ingredient_id=item_id)
        
        if sale_id:
            query = query.filter_by(sale_id=sale_id)
        elif online_order_id:
            query = query.filter_by(online_order_id=online_order_id)
        
        cost_record = query.first()
        return cost_record.average_cost if cost_record else 0
    
    @staticmethod
    def apply_discount_to_purchase_order(purchase_order, discount_amount, discount_type='amount', apply_to_items=False):
        """Applique une remise à une commande d'achat"""
        if not purchase_order or discount_amount <= 0:
            return False
        
        try:
            purchase_order.discount_amount = discount_amount
            purchase_order.discount_type = discount_type
            purchase_order.apply_discount_to_items = apply_to_items
            
            if apply_to_items:
                purchase_order.apply_discount_to_item_prices()
            
            purchase_order.calculate_totals()
            db.session.commit()
            return True
        except Exception as e:
            db.session.rollback()
            print(f"Erreur lors de l'application de la remise: {e}")
            return False
    
    @staticmethod
    def get_cost_analysis(item_id, item_type, days=30):
        """Analyse l'évolution des coûts d'un article sur une période"""
        from datetime import timedelta
        
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(days=days)
        
        query = CostHistory.query.filter(
            CostHistory.owner_id == current_user.id,
            CostHistory.created_at.between(start_date, end_date)
        )
        
        if item_type == 'product':
            query = query.filter_by(product_id=item_id)
        else:
            query = query.filter_by(ingredient_id=item_id)
        
        cost_records = query.order_by(CostHistory.created_at).all()
        
        return {
            'records': cost_records,
            'count': len(cost_records),
            'average_cost': sum(r.average_cost for r in cost_records) / len(cost_records) if cost_records else 0,
            'min_cost': min(r.average_cost for r in cost_records) if cost_records else 0,
            'max_cost': max(r.average_cost for r in cost_records) if cost_records else 0
        }
    
    @staticmethod
    def initialize_average_costs():
        """Initialise les coûts moyens pour les articles existants qui n'en ont pas"""
        try:
            # Produits sans recette
            products = Product.query.filter(
                Product.owner_id == current_user.id,
                Product.has_recipe == False,
                Product.average_cost == 0
            ).all()
            
            for product in products:
                if product.cost_price and product.cost_price > 0:
                    product.average_cost = product.cost_price
            
            # Ingrédients
            ingredients = Ingredient.query.filter(
                Ingredient.owner_id == current_user.id,
                Ingredient.average_cost == 0
            ).all()
            
            for ingredient in ingredients:
                if ingredient.price_per_unit and ingredient.price_per_unit > 0:
                    ingredient.average_cost = ingredient.price_per_unit
            
            db.session.commit()
            return True
        except Exception as e:
            db.session.rollback()
            print(f"Erreur lors de l'initialisation des coûts moyens: {e}")
            return False
