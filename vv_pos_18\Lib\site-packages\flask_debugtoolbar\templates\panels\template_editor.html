<!doctype html>
<!--[if gt IE 8]><!--> <html class="no-js" lang="en"> <!--<![endif]-->
<head>
  <base href="{{ request.path }}">
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta http-equiv="Pragma" content="no-cache">
  <meta name="description" content="{% block meta_description %}Description{% endblock %}">
  <meta name="author" content="{% block meta_author %}Author{% endblock %}">
  <meta name="viewport" content="width=device-width,initial-scale=1">
  <title>Editing {{ templates[0].name }}</title>
  <script src="{{ static_path }}js/jquery.js"></script>
  <!-- Temporarily adding jquery-migrate during the Jquery upgrade process, this can be removed post-upgrade -->
  <script src="{{ static_path }}js/jquery-migrate.js"></script>
  <link rel="stylesheet" href="{{ static_path }}codemirror/codemirror.css">
  <link rel="stylesheet" href="{{ static_path }}codemirror/theme/rubyblue.css">
  <link rel="stylesheet" href="{{ static_path }}codemirror/theme/lesser-dark.css">
  <style>
    {% with %}
    {% set toolbar_height = 25 %}
    {% set editor_width = request.cookies.get('fldt_editor_size')|int(40) %}

    #flDebugPanel, #flDebugEditor, #flDebugPreview, #flDebugSplitter, #flDebugToolbar {
      box-sizing: border-box;
      -moz-box-sizing: border-box;
      -ms-box-sizing: border-box;
      border: none;
    }
    #flDebugPanel, #flDebugPreview { position: fixed; }
    #flDebugPanel > div { position: absolute; }

    #flDebugPanel { width: {{ editor_width }}%; height: 100%; top: 0; left: 0; bottom: 0; }
    #flDebugToolbar { top: 0; width: 100%; height: {{ toolbar_height }}px; border-bottom: 1px solid black; padding: 2px 4px; }
    #flDebugEditor { top: {{ toolbar_height }}px; bottom: 0; width: 100%; }
    #flDebugPreview { top: 0; bottom: 0; right: 0; height: 100%; width: {{ 100 - editor_width }}%; }

    /* need a dummy element over the page so that drag events don't get captured by the preview iframe */
    #flDebugDragHandler { display: none; position: fixed; width: 100%; height: 100%; z-index: 1000; }
    #flDebugSplitter { width: 8px; height: 100%; top: 0; bottom: 0; right: -1px; z-index: 999; cursor: e-resize; border-right: 1px solid black; }

    .CodeMirror, .CodeMirror-scroll { height: 100%; }
    .syntax-error { background: red !important; }

    #flDebugToolbar button { margin: 0; }
    #flDebugSave { float: left; }
    #flDebugClose { float: right; }
    {% endwith %}
  </style>
</head>
<body>
  <div id="flDebugDragHandler"></div>
  <div id="flDebugPanel">
    <div id="flDebugToolbar">
      <button id="flDebugSave">Save</button>
      <button id="flDebugClose">Close</button>
    </div>
    <div id="flDebugEditor">
      <textarea name="{{ templates[0].name }}" id="code">{{ templates[0].source }}</textarea>
    </div>
    <div id="flDebugSplitter"></div>
  </div>
  <iframe id="flDebugPreview"></iframe>

  <script src="{{ static_path }}codemirror/codemirror.js"></script>
  <script src="{{ static_path }}codemirror/util/closetag.js"></script>
  <script src="{{ static_path }}codemirror/util/overlay.js"></script>
  <script src="{{ static_path }}codemirror/mode/xml/xml.js"></script>
  <script src="{{ static_path }}codemirror/mode/javascript/javascript.js"></script>
  <script src="{{ static_path }}codemirror/mode/css/css.js"></script>
  <script src="{{ static_path }}codemirror/mode/htmlmixed/htmlmixed.js"></script>
  <script src="{{ static_path }}codemirror/mode/jinja2/jinja2.js"></script>
  <script>{% raw %}
    $('#flDebugDragHandler')
      .mousemove(function(e) {
            var size = 100 * e.pageX / $(document).width();
            $('#flDebugPanel').css('width', size + '%');
            $('#flDebugPreview').css('width', (100 - size) + '%');
            return false;
          })
      .mouseup(function(e) {
            $(this).hide();
            var size = Math.round(100 * e.pageX / $(document).width());
            document.cookie = 'fldt_editor_size=' + size;
            return false;
          });

    $('#flDebugSplitter').mousedown(function() {
      $('#flDebugDragHandler').show();
      return false;
    });

    $('#flDebugSave').click(function() {
      $.ajax({
        type: 'POST'
      , url: document.baseURI + '/save'
      , data: {content: editor.getValue()}
      });
    });

    $('#flDebugClose').click(function() {
      document.location = document.location;
    });

    CodeMirror.defineMode("html+jinja2", function(config, parserConf) {
      return CodeMirror.overlayParser(CodeMirror.getMode(config, "htmlmixed"), CodeMirror.getMode(config, 'jinja2'));
    });
    CodeMirror.defineMIME("text/html", "html+jinja2");

    var delay;
    // Initialize CodeMirror editor with a nice html5 canvas demo.
    var editor = CodeMirror.fromTextArea(document.getElementById('code'), {
      mode: 'text/html'
    , theme: 'rubyblue'
    , lineNumbers: true
    , tabMode: 'indent'
    , onChange: function() {
        clearTimeout(delay);
        delay = setTimeout(updatePreview, 300);
      }
    });

    var previewFrame = document.getElementById('flDebugPreview');
    var preview = previewFrame.contentDocument || previewFrame.contentWindow.document;
    var errorLine = null;

    function updatePreview() {
      $.ajax({
        type: 'POST'
      , url: ''
      , data: {content: editor.getValue()}
      , success: function(data, status, xhr) {
          if (errorLine != null) {
            editor.setLineClass(errorLine, null, null);
          }
          preview.open();
          preview.write(xhr.responseText);
          preview.close();
          $('head', preview).append('<base target="_top">');
        }
      , error: function(xhr, status, error) {
          if (status === 'error') {
            var data = JSON.parse(xhr.responseText);
            console.log(data);
            if (errorLine != null) {
              editor.setLineClass(errorLine, null, null);
            }
            errorLine = editor.setLineClass(data.lineno - 1, null, 'syntax-error');
          }
        }
      });
    }

    setTimeout(updatePreview, 300);
    {% endraw %}</script>
</body>
</html>
