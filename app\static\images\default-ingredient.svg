<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100" width="100" height="100">
  <defs>
    <linearGradient id="leafGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4CAF50;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2E7D32;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#E8F5E8;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#C8E6C9;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background circle -->
  <circle cx="50" cy="50" r="45" fill="url(#bgGradient)" stroke="#4CAF50" stroke-width="2"/>
  
  <!-- Leaf shape -->
  <path d="M30 50 Q35 30, 50 35 Q65 30, 70 50 Q65 70, 50 65 Q35 70, 30 50 Z" 
        fill="url(#leafGradient)" stroke="#2E7D32" stroke-width="1"/>
  
  <!-- Leaf vein -->
  <path d="M40 45 Q50 40, 60 45 M45 50 Q50 48, 55 50 M42 55 Q50 52, 58 55" 
        stroke="#1B5E20" stroke-width="1" fill="none" opacity="0.7"/>
  
  <!-- Small decorative elements -->
  <circle cx="25" cy="35" r="3" fill="#81C784" opacity="0.6"/>
  <circle cx="75" cy="35" r="2" fill="#81C784" opacity="0.6"/>
  <circle cx="25" cy="65" r="2" fill="#81C784" opacity="0.6"/>
  <circle cx="75" cy="65" r="3" fill="#81C784" opacity="0.6"/>
</svg>
