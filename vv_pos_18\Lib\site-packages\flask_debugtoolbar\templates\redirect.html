<html>
  <head>
    <title>Redirect intercepted</title>
  </head>
  <body>
    <h1>Redirect ({{ redirect_code }})</h1>
    <p>Location: <a href="{{ redirect_to }}">{{ redirect_to }}</a></p>
    <p class="flDebugNotice">
      The Flask Debug Toolbar has intercepted a redirect to the above URL for
      debug viewing purposes.  You can click the above link to continue with the
      redirect as normal.  If you'd like to disable this feature, you can set the
      config variable <code>DEBUG_TB_INTERCEPT_REDIRECTS</code> to <code>False</code>.
    </p>
  </body>
</html>
