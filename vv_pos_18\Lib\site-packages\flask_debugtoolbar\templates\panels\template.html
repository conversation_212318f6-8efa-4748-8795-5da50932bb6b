{% if templates %}
  {% if editable %}
    <a href="/_debug_toolbar/views/template/{{ key }}" onclick="return fldt.load_href(this.href);">Edit templates</a>
  {% endif %}
  {% for template in templates %}
    <h4>{{ template.template.name }}</h4>
    <table>
      <thead>
        <tr>
          <th>Variable</th>
          <th>Value</th>
        </tr>
      </thead>
      <tbody>
        {% for k, v in template.context|dictsort %}
        <tr class="{{ loop.cycle('flDebugOdd', 'flDebugEven') }}">
          <td>{{ k }}</td>
          <td>{{ v|printable }}</td>
        </tr>
        {% endfor %}
      </tbody>
    </table>
  {% endfor %}
{% else %}
  <p>No template rendered</p>
{% endif %}
