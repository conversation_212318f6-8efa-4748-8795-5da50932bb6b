{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/cloud-platform": {"description": "See, edit, configure, and delete your Google Cloud data and see the email address for your Google Account."}}}}, "basePath": "", "baseUrl": "https://advisorynotifications.googleapis.com/", "batchPath": "batch", "canonicalName": "Advisorynotifications", "description": "An API for accessing Advisory Notifications in Google Cloud", "discoveryVersion": "v1", "documentationLink": "https://cloud.google.com/advisory-notifications", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "advisorynotifications:v1", "kind": "discovery#restDescription", "mtlsRootUrl": "https://advisorynotifications.mtls.googleapis.com/", "name": "advisorynotifications", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"organizations": {"resources": {"locations": {"methods": {"getSettings": {"description": "Get notification settings.", "flatPath": "v1/organizations/{organizationsId}/locations/{locationsId}/settings", "httpMethod": "GET", "id": "advisorynotifications.organizations.locations.getSettings", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the settings to retrieve. Format: organizations/{organization}/locations/{location}/settings or projects/{projects}/locations/{location}/settings.", "location": "path", "pattern": "^organizations/[^/]+/locations/[^/]+/settings$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleCloudAdvisorynotificationsV1Settings"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "updateSettings": {"description": "Update notification settings.", "flatPath": "v1/organizations/{organizationsId}/locations/{locationsId}/settings", "httpMethod": "PATCH", "id": "advisorynotifications.organizations.locations.updateSettings", "parameterOrder": ["name"], "parameters": {"name": {"description": "Identifier. The resource name of the settings to retrieve. Format: organizations/{organization}/locations/{location}/settings or projects/{projects}/locations/{location}/settings.", "location": "path", "pattern": "^organizations/[^/]+/locations/[^/]+/settings$", "required": true, "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "GoogleCloudAdvisorynotificationsV1Settings"}, "response": {"$ref": "GoogleCloudAdvisorynotificationsV1Settings"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"notifications": {"methods": {"get": {"description": "Gets a notification.", "flatPath": "v1/organizations/{organizationsId}/locations/{locationsId}/notifications/{notificationsId}", "httpMethod": "GET", "id": "advisorynotifications.organizations.locations.notifications.get", "parameterOrder": ["name"], "parameters": {"languageCode": {"description": "ISO code for requested localization language. If unset, will be interpereted as \"en\". If the requested language is valid, but not supported for this notification, English will be returned with an \"Not applicable\" LocalizationState. If the ISO code is invalid (i.e. not a real language), this RPC will throw an error.", "location": "query", "type": "string"}, "name": {"description": "Required. A name of the notification to retrieve. Format: organizations/{organization}/locations/{location}/notifications/{notification} or projects/{projects}/locations/{location}/notifications/{notification}.", "location": "path", "pattern": "^organizations/[^/]+/locations/[^/]+/notifications/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleCloudAdvisorynotificationsV1Notification"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists notifications under a given parent.", "flatPath": "v1/organizations/{organizationsId}/locations/{locationsId}/notifications", "httpMethod": "GET", "id": "advisorynotifications.organizations.locations.notifications.list", "parameterOrder": ["parent"], "parameters": {"languageCode": {"description": "ISO code for requested localization language. If unset, will be interpereted as \"en\". If the requested language is valid, but not supported for this notification, English will be returned with an \"Not applicable\" LocalizationState. If the ISO code is invalid (i.e. not a real language), this RPC will throw an error.", "location": "query", "type": "string"}, "pageSize": {"description": "The maximum number of notifications to return. The service may return fewer than this value. If unspecified or equal to 0, at most 50 notifications will be returned. The maximum value is 50; values above 50 will be coerced to 50.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token returned from a previous request. When paginating, all other parameters provided in the request must match the call that returned the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent, which owns this collection of notifications. Must be of the form \"organizations/{organization}/locations/{location}\" or \"projects/{project}/locations/{location}\".", "location": "path", "pattern": "^organizations/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "view": {"description": "Specifies which parts of the notification resource should be returned in the response.", "enum": ["NOTIFICATION_VIEW_UNSPECIFIED", "BASIC", "FULL"], "enumDescriptions": ["Not specified, equivalent to BASIC.", "Server responses only include title, creation time and Notification ID. Note: for internal use responses also include the last update time, the latest message text and whether notification has attachments.", "Include everything."], "location": "query", "type": "string"}}, "path": "v1/{+parent}/notifications", "response": {"$ref": "GoogleCloudAdvisorynotificationsV1ListNotificationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}}}, "projects": {"resources": {"locations": {"methods": {"getSettings": {"description": "Get notification settings.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/settings", "httpMethod": "GET", "id": "advisorynotifications.projects.locations.getSettings", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the settings to retrieve. Format: organizations/{organization}/locations/{location}/settings or projects/{projects}/locations/{location}/settings.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/settings$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleCloudAdvisorynotificationsV1Settings"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "updateSettings": {"description": "Update notification settings.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/settings", "httpMethod": "PATCH", "id": "advisorynotifications.projects.locations.updateSettings", "parameterOrder": ["name"], "parameters": {"name": {"description": "Identifier. The resource name of the settings to retrieve. Format: organizations/{organization}/locations/{location}/settings or projects/{projects}/locations/{location}/settings.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/settings$", "required": true, "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "GoogleCloudAdvisorynotificationsV1Settings"}, "response": {"$ref": "GoogleCloudAdvisorynotificationsV1Settings"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"notifications": {"methods": {"get": {"description": "Gets a notification.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/notifications/{notificationsId}", "httpMethod": "GET", "id": "advisorynotifications.projects.locations.notifications.get", "parameterOrder": ["name"], "parameters": {"languageCode": {"description": "ISO code for requested localization language. If unset, will be interpereted as \"en\". If the requested language is valid, but not supported for this notification, English will be returned with an \"Not applicable\" LocalizationState. If the ISO code is invalid (i.e. not a real language), this RPC will throw an error.", "location": "query", "type": "string"}, "name": {"description": "Required. A name of the notification to retrieve. Format: organizations/{organization}/locations/{location}/notifications/{notification} or projects/{projects}/locations/{location}/notifications/{notification}.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/notifications/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleCloudAdvisorynotificationsV1Notification"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists notifications under a given parent.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/notifications", "httpMethod": "GET", "id": "advisorynotifications.projects.locations.notifications.list", "parameterOrder": ["parent"], "parameters": {"languageCode": {"description": "ISO code for requested localization language. If unset, will be interpereted as \"en\". If the requested language is valid, but not supported for this notification, English will be returned with an \"Not applicable\" LocalizationState. If the ISO code is invalid (i.e. not a real language), this RPC will throw an error.", "location": "query", "type": "string"}, "pageSize": {"description": "The maximum number of notifications to return. The service may return fewer than this value. If unspecified or equal to 0, at most 50 notifications will be returned. The maximum value is 50; values above 50 will be coerced to 50.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token returned from a previous request. When paginating, all other parameters provided in the request must match the call that returned the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent, which owns this collection of notifications. Must be of the form \"organizations/{organization}/locations/{location}\" or \"projects/{project}/locations/{location}\".", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "view": {"description": "Specifies which parts of the notification resource should be returned in the response.", "enum": ["NOTIFICATION_VIEW_UNSPECIFIED", "BASIC", "FULL"], "enumDescriptions": ["Not specified, equivalent to BASIC.", "Server responses only include title, creation time and Notification ID. Note: for internal use responses also include the last update time, the latest message text and whether notification has attachments.", "Include everything."], "location": "query", "type": "string"}}, "path": "v1/{+parent}/notifications", "response": {"$ref": "GoogleCloudAdvisorynotificationsV1ListNotificationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}}}}, "revision": "20240707", "rootUrl": "https://advisorynotifications.googleapis.com/", "schemas": {"GoogleCloudAdvisorynotificationsV1Attachment": {"description": "Attachment with specific information about the issue.", "id": "GoogleCloudAdvisorynotificationsV1Attachment", "properties": {"csv": {"$ref": "GoogleCloudAdvisorynotificationsV1Csv", "description": "A CSV file attachment. Max size is 10 MB."}, "displayName": {"description": "The title of the attachment.", "type": "string"}}, "type": "object"}, "GoogleCloudAdvisorynotificationsV1Csv": {"description": "A representation of a CSV file attachment, as a list of column headers and a list of data rows.", "id": "GoogleCloudAdvisorynotificationsV1Csv", "properties": {"dataRows": {"description": "The list of data rows in a CSV file, as string arrays rather than as a single comma-separated string.", "items": {"$ref": "GoogleCloudAdvisorynotificationsV1CsvCsvRow"}, "type": "array"}, "headers": {"description": "The list of headers for data columns in a CSV file.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleCloudAdvisorynotificationsV1CsvCsvRow": {"description": "A representation of a single data row in a CSV file.", "id": "GoogleCloudAdvisorynotificationsV1CsvCsvRow", "properties": {"entries": {"description": "The data entries in a CSV file row, as a string array rather than a single comma-separated string.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleCloudAdvisorynotificationsV1ListNotificationsResponse": {"description": "Response of ListNotifications endpoint.", "id": "GoogleCloudAdvisorynotificationsV1ListNotificationsResponse", "properties": {"nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "notifications": {"description": "List of notifications under a given parent.", "items": {"$ref": "GoogleCloudAdvisorynotificationsV1Notification"}, "type": "array"}, "totalSize": {"description": "Estimation of a total number of notifications.", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleCloudAdvisorynotificationsV1Message": {"description": "A message which contains notification details.", "id": "GoogleCloudAdvisorynotificationsV1Message", "properties": {"attachments": {"description": "The attachments to download.", "items": {"$ref": "GoogleCloudAdvisorynotificationsV1Attachment"}, "type": "array"}, "body": {"$ref": "GoogleCloudAdvisorynotificationsV1MessageBody", "description": "The message content."}, "createTime": {"description": "The Message creation timestamp.", "format": "google-datetime", "type": "string"}, "localizationTime": {"description": "Time when Message was localized", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleCloudAdvisorynotificationsV1MessageBody": {"description": "A message body containing text.", "id": "GoogleCloudAdvisorynotificationsV1MessageBody", "properties": {"text": {"$ref": "GoogleCloudAdvisorynotificationsV1Text", "description": "The text content of the message body."}}, "type": "object"}, "GoogleCloudAdvisorynotificationsV1Notification": {"description": "A notification object for notifying customers about security and privacy issues.", "id": "GoogleCloudAdvisorynotificationsV1Notification", "properties": {"createTime": {"description": "Output only. Time the notification was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "messages": {"description": "A list of messages in the notification.", "items": {"$ref": "GoogleCloudAdvisorynotificationsV1Message"}, "type": "array"}, "name": {"description": "The resource name of the notification. Format: organizations/{organization}/locations/{location}/notifications/{notification} or projects/{project}/locations/{location}/notifications/{notification}.", "type": "string"}, "notificationType": {"description": "Type of notification", "enum": ["NOTIFICATION_TYPE_UNSPECIFIED", "NOTIFICATION_TYPE_SECURITY_PRIVACY_ADVISORY", "NOTIFICATION_TYPE_SENSITIVE_ACTIONS", "NOTIFICATION_TYPE_SECURITY_MSA", "NOTIFICATION_TYPE_THREAT_HORIZONS"], "enumDescriptions": ["Default type", "Security and privacy advisory notifications", "Sensitive action notifications", "General security MSA", "Threat horizons MSA"], "type": "string"}, "subject": {"$ref": "GoogleCloudAdvisorynotificationsV1Subject", "description": "The subject line of the notification."}}, "type": "object"}, "GoogleCloudAdvisorynotificationsV1NotificationSettings": {"description": "Settings for each NotificationType.", "id": "GoogleCloudAdvisorynotificationsV1NotificationSettings", "properties": {"enabled": {"description": "Whether the associated NotificationType is enabled.", "type": "boolean"}}, "type": "object"}, "GoogleCloudAdvisorynotificationsV1Settings": {"description": "Settings for Advisory Notifications.", "id": "GoogleCloudAdvisorynotificationsV1Settings", "properties": {"etag": {"description": "Required. Fingerprint for optimistic concurrency returned in Get requests. Must be provided for Update requests. If the value provided does not match the value known to the server, ABORTED will be thrown, and the client should retry the read-modify-write cycle.", "type": "string"}, "name": {"description": "Identifier. The resource name of the settings to retrieve. Format: organizations/{organization}/locations/{location}/settings or projects/{projects}/locations/{location}/settings.", "type": "string"}, "notificationSettings": {"additionalProperties": {"$ref": "GoogleCloudAdvisorynotificationsV1NotificationSettings"}, "description": "Required. Map of each notification type and its settings to get/set all settings at once. The server will validate the value for each notification type.", "type": "object"}}, "type": "object"}, "GoogleCloudAdvisorynotificationsV1Subject": {"description": "A subject line of a notification.", "id": "GoogleCloudAdvisorynotificationsV1Subject", "properties": {"text": {"$ref": "GoogleCloudAdvisorynotificationsV1Text", "description": "The text content."}}, "type": "object"}, "GoogleCloudAdvisorynotificationsV1Text": {"description": "A text object containing the English text and its localized copies.", "id": "GoogleCloudAdvisorynotificationsV1Text", "properties": {"enText": {"description": "The English copy.", "type": "string"}, "localizationState": {"description": "Status of the localization.", "enum": ["LOCALIZATION_STATE_UNSPECIFIED", "LOCALIZATION_STATE_NOT_APPLICABLE", "LOCALIZATION_STATE_PENDING", "LOCALIZATION_STATE_COMPLETED"], "enumDescriptions": ["Not used.", "Localization is not applicable for requested language. This can happen when: - The requested language was not supported by Advisory Notifications at the time of localization (including notifications created before the localization feature was launched). - The requested language is English, so only the English text is returned.", "Localization for requested language is in progress, and not ready yet.", "Localization for requested language is completed."], "type": "string"}, "localizedText": {"description": "The requested localized copy (if applicable).", "type": "string"}}, "type": "object"}}, "servicePath": "", "title": "Advisory Notifications API", "version": "v1", "version_module": true}