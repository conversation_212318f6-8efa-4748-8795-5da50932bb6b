{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/cloud-platform": {"description": "See, edit, configure, and delete your Google Cloud data and see the email address for your Google Account."}}}}, "basePath": "", "baseUrl": "https://datamigration.googleapis.com/", "batchPath": "batch", "canonicalName": "Database Migration Service", "description": "Manage Cloud Database Migration Service resources on Google Cloud Platform.", "discoveryVersion": "v1", "documentationLink": "https://cloud.google.com/database-migration/", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "datamigration:v1beta1", "kind": "discovery#restDescription", "mtlsRootUrl": "https://datamigration.mtls.googleapis.com/", "name": "datamigration", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"projects": {"resources": {"locations": {"methods": {"get": {"description": "Gets information about a location.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}", "httpMethod": "GET", "id": "datamigration.projects.locations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Resource name for the location.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Location"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists information about the supported locations for this service.", "flatPath": "v1beta1/projects/{projectsId}/locations", "httpMethod": "GET", "id": "datamigration.projects.locations.list", "parameterOrder": ["name"], "parameters": {"extraLocationTypes": {"description": "Optional. A list of extra location types that should be used as conditions for controlling the visibility of the locations.", "location": "query", "repeated": true, "type": "string"}, "filter": {"description": "A filter to narrow down results to a preferred subset. The filtering language accepts strings like `\"displayName=tokyo\"`, and is documented in more detail in [AIP-160](https://google.aip.dev/160).", "location": "query", "type": "string"}, "name": {"description": "The resource that owns the locations collection, if applicable.", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "The maximum number of results to return. If not set, the service selects a default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token received from the `next_page_token` field in the response. Send that page token to receive the subsequent page.", "location": "query", "type": "string"}}, "path": "v1beta1/{+name}/locations", "response": {"$ref": "ListLocationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"connectionProfiles": {"methods": {"create": {"description": "Creates a new connection profile in a given project and location.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/connectionProfiles", "httpMethod": "POST", "id": "datamigration.projects.locations.connectionProfiles.create", "parameterOrder": ["parent"], "parameters": {"connectionProfileId": {"description": "Required. The connection profile identifier.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent, which owns this collection of connection profiles.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "A unique id used to identify the request. If the server receives two requests with the same id, then the second request will be ignored. It is recommended to always set this value to a UUID. The id must contain only letters (a-z, A-Z), numbers (0-9), underscores (_), and hyphens (-). The maximum length is 40 characters.", "location": "query", "type": "string"}}, "path": "v1beta1/{+parent}/connectionProfiles", "request": {"$ref": "ConnectionProfile"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a single Database Migration Service connection profile. A connection profile can only be deleted if it is not in use by any active migration jobs.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/connectionProfiles/{connectionProfilesId}", "httpMethod": "DELETE", "id": "datamigration.projects.locations.connectionProfiles.delete", "parameterOrder": ["name"], "parameters": {"force": {"description": "In case of force delete, the CloudSQL replica database is also deleted (only for CloudSQL connection profile).", "location": "query", "type": "boolean"}, "name": {"description": "Required. Name of the connection profile resource to delete.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/connectionProfiles/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "A unique id used to identify the request. If the server receives two requests with the same id, then the second request will be ignored. It is recommended to always set this value to a UUID. The id must contain only letters (a-z, A-Z), numbers (0-9), underscores (_), and hyphens (-). The maximum length is 40 characters.", "location": "query", "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of a single connection profile.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/connectionProfiles/{connectionProfilesId}", "httpMethod": "GET", "id": "datamigration.projects.locations.connectionProfiles.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the connection profile resource to get.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/connectionProfiles/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "ConnectionProfile"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getIamPolicy": {"description": "Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/connectionProfiles/{connectionProfilesId}:getIamPolicy", "httpMethod": "GET", "id": "datamigration.projects.locations.connectionProfiles.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"options.requestedPolicyVersion": {"description": "Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "location": "query", "type": "integer"}, "resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/connectionProfiles/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+resource}:getIamPolicy", "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Retrieve a list of all connection profiles in a given project and location.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/connectionProfiles", "httpMethod": "GET", "id": "datamigration.projects.locations.connectionProfiles.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "A filter expression that filters connection profiles listed in the response. The expression must specify the field name, a comparison operator, and the value that you want to use for filtering. The value must be a string, a number, or a boolean. The comparison operator must be either =, !=, >, or <. For example, list connection profiles created this year by specifying **createTime %gt; 2020-01-01T00:00:00.000000000Z**. You can also filter nested fields. For example, you could specify **mySql.username = %lt;my_username%gt;** to list all connection profiles configured to connect with a specific username.", "location": "query", "type": "string"}, "orderBy": {"description": "the order by fields for the result.", "location": "query", "type": "string"}, "pageSize": {"description": "The maximum number of connection profiles to return. The service may return fewer than this value. If unspecified, at most 50 connection profiles will be returned. The maximum value is 1000; values above 1000 will be coerced to 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `ListConnectionProfiles` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListConnectionProfiles` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent, which owns this collection of connection profiles.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/connectionProfiles", "response": {"$ref": "ListConnectionProfilesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Update the configuration of a single connection profile.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/connectionProfiles/{connectionProfilesId}", "httpMethod": "PATCH", "id": "datamigration.projects.locations.connectionProfiles.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of this connection profile resource in the form of projects/{project}/locations/{location}/connectionProfiles/{connectionProfile}.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/connectionProfiles/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "A unique id used to identify the request. If the server receives two requests with the same id, then the second request will be ignored. It is recommended to always set this value to a UUID. The id must contain only letters (a-z, A-Z), numbers (0-9), underscores (_), and hyphens (-). The maximum length is 40 characters.", "location": "query", "type": "string"}, "updateMask": {"description": "Required. Field mask is used to specify the fields to be overwritten in the connection profile resource by the update.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1beta1/{+name}", "request": {"$ref": "ConnectionProfile"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/connectionProfiles/{connectionProfilesId}:setIamPolicy", "httpMethod": "POST", "id": "datamigration.projects.locations.connectionProfiles.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/connectionProfiles/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+resource}:setIamPolicy", "request": {"$ref": "SetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"description": "Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may \"fail open\" without warning.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/connectionProfiles/{connectionProfilesId}:testIamPermissions", "httpMethod": "POST", "id": "datamigration.projects.locations.connectionProfiles.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/connectionProfiles/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+resource}:testIamPermissions", "request": {"$ref": "TestIamPermissionsRequest"}, "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "migrationJobs": {"methods": {"create": {"description": "Creates a new migration job in a given project and location.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/migrationJobs", "httpMethod": "POST", "id": "datamigration.projects.locations.migrationJobs.create", "parameterOrder": ["parent"], "parameters": {"migrationJobId": {"description": "Required. The ID of the instance to create.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent, which owns this collection of migration jobs.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "A unique id used to identify the request. If the server receives two requests with the same id, then the second request will be ignored. It is recommended to always set this value to a UUID. The id must contain only letters (a-z, A-Z), numbers (0-9), underscores (_), and hyphens (-). The maximum length is 40 characters.", "location": "query", "type": "string"}}, "path": "v1beta1/{+parent}/migrationJobs", "request": {"$ref": "<PERSON><PERSON><PERSON><PERSON>"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a single migration job.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/migrationJobs/{migrationJobsId}", "httpMethod": "DELETE", "id": "datamigration.projects.locations.migrationJobs.delete", "parameterOrder": ["name"], "parameters": {"force": {"description": "The destination CloudSQL connection profile is always deleted with the migration job. In case of force delete, the destination CloudSQL replica database is also deleted.", "location": "query", "type": "boolean"}, "name": {"description": "Required. Name of the migration job resource to delete.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/migrationJobs/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "A unique id used to identify the request. If the server receives two requests with the same id, then the second request will be ignored. It is recommended to always set this value to a UUID. The id must contain only letters (a-z, A-Z), numbers (0-9), underscores (_), and hyphens (-). The maximum length is 40 characters.", "location": "query", "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "generateSshScript": {"description": "Generate a SSH configuration script to configure the reverse SSH connectivity.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/migrationJobs/{migrationJobsId}:generateSshScript", "httpMethod": "POST", "id": "datamigration.projects.locations.migrationJobs.generateSshScript", "parameterOrder": ["migrationJob"], "parameters": {"migrationJob": {"description": "Name of the migration job resource to generate the SSH script.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/migrationJobs/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+migrationJob}:generateSshScript", "request": {"$ref": "GenerateSshScriptRequest"}, "response": {"$ref": "SshScript"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of a single migration job.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/migrationJobs/{migrationJobsId}", "httpMethod": "GET", "id": "datamigration.projects.locations.migrationJobs.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the migration job resource to get.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/migrationJobs/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "<PERSON><PERSON><PERSON><PERSON>"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getIamPolicy": {"description": "Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/migrationJobs/{migrationJobsId}:getIamPolicy", "httpMethod": "GET", "id": "datamigration.projects.locations.migrationJobs.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"options.requestedPolicyVersion": {"description": "Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "location": "query", "type": "integer"}, "resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/migrationJobs/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+resource}:getIamPolicy", "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists migration jobs in a given project and location.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/migrationJobs", "httpMethod": "GET", "id": "datamigration.projects.locations.migrationJobs.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "A filter expression that filters migration jobs listed in the response. The expression must specify the field name, a comparison operator, and the value that you want to use for filtering. The value must be a string, a number, or a boolean. The comparison operator must be either =, !=, >, or <. For example, list migration jobs created this year by specifying **createTime %gt; 2020-01-01T00:00:00.000000000Z.** You can also filter nested fields. For example, you could specify **reverseSshConnectivity.vmIp = \"*******\"** to select all migration jobs connecting through the specific SSH tunnel bastion.", "location": "query", "type": "string"}, "orderBy": {"description": "Sort the results based on the migration job name. Valid values are: \"name\", \"name asc\", and \"name desc\".", "location": "query", "type": "string"}, "pageSize": {"description": "The maximum number of migration jobs to return. The service may return fewer than this value. If unspecified, at most 50 migration jobs will be returned. The maximum value is 1000; values above 1000 will be coerced to 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The nextPageToken value received in the previous call to migrationJobs.list, used in the subsequent request to retrieve the next page of results. On first call this should be left blank. When paginating, all other parameters provided to migrationJobs.list must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent, which owns this collection of migrationJobs.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/migrationJobs", "response": {"$ref": "ListMigrationJobsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates the parameters of a single migration job.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/migrationJobs/{migrationJobsId}", "httpMethod": "PATCH", "id": "datamigration.projects.locations.migrationJobs.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name (URI) of this migration job resource, in the form of: projects/{project}/locations/{location}/migrationJobs/{migrationJob}.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/migrationJobs/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "A unique id used to identify the request. If the server receives two requests with the same id, then the second request will be ignored. It is recommended to always set this value to a UUID. The id must contain only letters (a-z, A-Z), numbers (0-9), underscores (_), and hyphens (-). The maximum length is 40 characters.", "location": "query", "type": "string"}, "updateMask": {"description": "Required. Field mask is used to specify the fields to be overwritten in the migration job resource by the update.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1beta1/{+name}", "request": {"$ref": "<PERSON><PERSON><PERSON><PERSON>"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "promote": {"description": "Promote a migration job, stopping replication to the destination and promoting the destination to be a standalone database.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/migrationJobs/{migrationJobsId}:promote", "httpMethod": "POST", "id": "datamigration.projects.locations.migrationJobs.promote", "parameterOrder": ["name"], "parameters": {"name": {"description": "Name of the migration job resource to promote.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/migrationJobs/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}:promote", "request": {"$ref": "PromoteMigrationJobRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "restart": {"description": "Restart a stopped or failed migration job, resetting the destination instance to its original state and starting the migration process from scratch.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/migrationJobs/{migrationJobsId}:restart", "httpMethod": "POST", "id": "datamigration.projects.locations.migrationJobs.restart", "parameterOrder": ["name"], "parameters": {"name": {"description": "Name of the migration job resource to restart.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/migrationJobs/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}:restart", "request": {"$ref": "RestartMigrationJobRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "resume": {"description": "Resume a migration job that is currently stopped and is resumable (was stopped during CDC phase).", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/migrationJobs/{migrationJobsId}:resume", "httpMethod": "POST", "id": "datamigration.projects.locations.migrationJobs.resume", "parameterOrder": ["name"], "parameters": {"name": {"description": "Name of the migration job resource to resume.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/migrationJobs/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}:resume", "request": {"$ref": "ResumeMigrationJobRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/migrationJobs/{migrationJobsId}:setIamPolicy", "httpMethod": "POST", "id": "datamigration.projects.locations.migrationJobs.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/migrationJobs/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+resource}:setIamPolicy", "request": {"$ref": "SetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "start": {"description": "Start an already created migration job.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/migrationJobs/{migrationJobsId}:start", "httpMethod": "POST", "id": "datamigration.projects.locations.migrationJobs.start", "parameterOrder": ["name"], "parameters": {"name": {"description": "Name of the migration job resource to start.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/migrationJobs/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}:start", "request": {"$ref": "StartMigrationJobRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "stop": {"description": "Stops a running migration job.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/migrationJobs/{migrationJobsId}:stop", "httpMethod": "POST", "id": "datamigration.projects.locations.migrationJobs.stop", "parameterOrder": ["name"], "parameters": {"name": {"description": "Name of the migration job resource to stop.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/migrationJobs/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}:stop", "request": {"$ref": "StopMigrationJobRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"description": "Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may \"fail open\" without warning.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/migrationJobs/{migrationJobsId}:testIamPermissions", "httpMethod": "POST", "id": "datamigration.projects.locations.migrationJobs.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/migrationJobs/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+resource}:testIamPermissions", "request": {"$ref": "TestIamPermissionsRequest"}, "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "verify": {"description": "Verify a migration job, making sure the destination can reach the source and that all configuration and prerequisites are met.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/migrationJobs/{migrationJobsId}:verify", "httpMethod": "POST", "id": "datamigration.projects.locations.migrationJobs.verify", "parameterOrder": ["name"], "parameters": {"name": {"description": "Name of the migration job resource to verify.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/migrationJobs/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}:verify", "request": {"$ref": "VerifyMigrationJobRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "operations": {"methods": {"cancel": {"description": "Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}:cancel", "httpMethod": "POST", "id": "datamigration.projects.locations.operations.cancel", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource to be cancelled.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}:cancel", "request": {"$ref": "CancelOperationRequest"}, "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}", "httpMethod": "DELETE", "id": "datamigration.projects.locations.operations.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource to be deleted.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}", "httpMethod": "GET", "id": "datamigration.projects.locations.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/operations", "httpMethod": "GET", "id": "datamigration.projects.locations.operations.list", "parameterOrder": ["name"], "parameters": {"filter": {"description": "The standard list filter.", "location": "query", "type": "string"}, "name": {"description": "The name of the operation's parent resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "The standard list page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The standard list page token.", "location": "query", "type": "string"}}, "path": "v1beta1/{+name}/operations", "response": {"$ref": "ListOperationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}}}}, "revision": "20250409", "rootUrl": "https://datamigration.googleapis.com/", "schemas": {"AuditConfig": {"description": "Specifies the audit configuration for a service. The configuration determines which permission types are logged, and what identities, if any, are exempted from logging. An AuditConfig must have one or more AuditLogConfigs. If there are AuditConfigs for both `allServices` and a specific service, the union of the two AuditConfigs is used for that service: the log_types specified in each AuditConfig are enabled, and the exempted_members in each AuditLogConfig are exempted. Example Policy with multiple AuditConfigs: { \"audit_configs\": [ { \"service\": \"allServices\", \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\", \"exempted_members\": [ \"user:<EMAIL>\" ] }, { \"log_type\": \"DATA_WRITE\" }, { \"log_type\": \"ADMIN_READ\" } ] }, { \"service\": \"sampleservice.googleapis.com\", \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\" }, { \"log_type\": \"DATA_WRITE\", \"exempted_members\": [ \"user:<EMAIL>\" ] } ] } ] } For sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ logging. It also exempts `<EMAIL>` from DATA_READ logging, and `<EMAIL>` from DATA_WRITE logging.", "id": "AuditConfig", "properties": {"auditLogConfigs": {"description": "The configuration for logging of each type of permission.", "items": {"$ref": "AuditLogConfig"}, "type": "array"}, "service": {"description": "Specifies a service that will be enabled for audit logging. For example, `storage.googleapis.com`, `cloudsql.googleapis.com`. `allServices` is a special value that covers all services.", "type": "string"}}, "type": "object"}, "AuditLogConfig": {"description": "Provides the configuration for logging a type of permissions. Example: { \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\", \"exempted_members\": [ \"user:<EMAIL>\" ] }, { \"log_type\": \"DATA_WRITE\" } ] } This enables 'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from DATA_READ logging.", "id": "AuditLogConfig", "properties": {"exemptedMembers": {"description": "Specifies the identities that do not cause logging for this type of permission. Follows the same format of Binding.members.", "items": {"type": "string"}, "type": "array"}, "logType": {"description": "The log type that this config enables.", "enum": ["LOG_TYPE_UNSPECIFIED", "ADMIN_READ", "DATA_WRITE", "DATA_READ"], "enumDescriptions": ["Default case. Should never be this.", "Admin reads. Example: CloudIAM getIamPolicy", "Data writes. Example: CloudSQL Users create", "Data reads. Example: CloudSQL Users list"], "type": "string"}}, "type": "object"}, "Binding": {"description": "Associates `members`, or principals, with a `role`.", "id": "Binding", "properties": {"condition": {"$ref": "Expr", "description": "The condition that is associated with this binding. If the condition evaluates to `true`, then this binding applies to the current request. If the condition evaluates to `false`, then this binding does not apply to the current request. However, a different role binding might grant the same role to one or more of the principals in this binding. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies)."}, "members": {"description": "Specifies the principals requesting access for a Google Cloud resource. `members` can have the following values: * `allUsers`: A special identifier that represents anyone who is on the internet; with or without a Google account. * `allAuthenticatedUsers`: A special identifier that represents anyone who is authenticated with a Google account or a service account. Does not include identities that come from external identity providers (IdPs) through identity federation. * `user:{emailid}`: An email address that represents a specific Google account. For example, `<EMAIL>` . * `serviceAccount:{emailid}`: An email address that represents a Google service account. For example, `<EMAIL>`. * `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`: An identifier for a [Kubernetes service account](https://cloud.google.com/kubernetes-engine/docs/how-to/kubernetes-service-accounts). For example, `my-project.svc.id.goog[my-namespace/my-kubernetes-sa]`. * `group:{emailid}`: An email address that represents a Google group. For example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain (primary) that represents all the users of that domain. For example, `google.com` or `example.com`. * `principal://iam.googleapis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: A single identity in a workforce identity pool. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/group/{group_id}`: All workforce identities in a group. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/attribute.{attribute_name}/{attribute_value}`: All workforce identities with a specific attribute value. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/*`: All identities in a workforce identity pool. * `principal://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/subject/{subject_attribute_value}`: A single identity in a workload identity pool. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/group/{group_id}`: A workload identity pool group. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/attribute.{attribute_name}/{attribute_value}`: All identities in a workload identity pool with a certain attribute. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/*`: All identities in a workload identity pool. * `deleted:user:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a user that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the user is recovered, this value reverts to `user:{emailid}` and the recovered user retains the role in the binding. * `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a service account that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the service account is undeleted, this value reverts to `serviceAccount:{emailid}` and the undeleted service account retains the role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a Google group that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the group is recovered, this value reverts to `group:{emailid}` and the recovered group retains the role in the binding. * `deleted:principal://iam.googleapis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: Deleted single identity in a workforce identity pool. For example, `deleted:principal://iam.googleapis.com/locations/global/workforcePools/my-pool-id/subject/my-subject-attribute-value`.", "items": {"type": "string"}, "type": "array"}, "role": {"description": "Role that is assigned to the list of `members`, or principals. For example, `roles/viewer`, `roles/editor`, or `roles/owner`. For an overview of the IAM roles and permissions, see the [IAM documentation](https://cloud.google.com/iam/docs/roles-overview). For a list of the available pre-defined roles, see [here](https://cloud.google.com/iam/docs/understanding-roles).", "type": "string"}}, "type": "object"}, "CancelOperationRequest": {"description": "The request message for Operations.CancelOperation.", "id": "CancelOperationRequest", "properties": {}, "type": "object"}, "CloudSqlConnectionProfile": {"description": "Specifies required connection parameters, and, optionally, the parameters required to create a Cloud SQL destination database instance.", "id": "CloudSqlConnectionProfile", "properties": {"cloudSqlId": {"description": "Output only. The Cloud SQL instance ID that this connection profile is associated with.", "readOnly": true, "type": "string"}, "privateIp": {"description": "Output only. The Cloud SQL database instance's private IP.", "readOnly": true, "type": "string"}, "publicIp": {"description": "Output only. The Cloud SQL database instance's public IP.", "readOnly": true, "type": "string"}, "settings": {"$ref": "CloudSqlSettings", "description": "Immutable. Metadata used to create the destination Cloud SQL database."}}, "type": "object"}, "CloudSqlSettings": {"description": "Settings for creating a Cloud SQL database instance.", "id": "CloudSqlSettings", "properties": {"activationPolicy": {"description": "The activation policy specifies when the instance is activated; it is applicable only when the instance state is 'RUNNABLE'. Valid values: 'ALWAYS': The instance is on, and remains so even in the absence of connection requests. `NEVER`: The instance is off; it is not activated, even if a connection request arrives.", "enum": ["SQL_ACTIVATION_POLICY_UNSPECIFIED", "ALWAYS", "NEVER"], "enumDescriptions": ["unspecified policy.", "The instance is always up and running.", "The instance should never spin up."], "type": "string"}, "autoStorageIncrease": {"description": "[default: ON] If you enable this setting, Cloud SQL checks your available storage every 30 seconds. If the available storage falls below a threshold size, Cloud SQL automatically adds additional storage capacity. If the available storage repeatedly falls below the threshold size, Cloud SQL continues to add storage until it reaches the maximum of 30 TB.", "type": "boolean"}, "dataDiskSizeGb": {"description": "The storage capacity available to the database, in GB. The minimum (and default) size is 10GB.", "format": "int64", "type": "string"}, "dataDiskType": {"description": "The type of storage: `PD_SSD` (default) or `PD_HDD`.", "enum": ["SQL_DATA_DISK_TYPE_UNSPECIFIED", "PD_SSD", "PD_HDD"], "enumDescriptions": ["Unspecified.", "SSD disk.", "HDD disk."], "type": "string"}, "databaseFlags": {"additionalProperties": {"type": "string"}, "description": "The database flags passed to the Cloud SQL instance at startup. An object containing a list of \"key\": value pairs. Example: { \"name\": \"wrench\", \"mass\": \"1.3kg\", \"count\": \"3\" }.", "type": "object"}, "databaseVersion": {"description": "The database engine type and version.", "enum": ["SQL_DATABASE_VERSION_UNSPECIFIED", "MYSQL_5_6", "MYSQL_5_7", "MYSQL_8_0"], "enumDescriptions": ["Unspecified version.", "MySQL 5.6.", "MySQL 5.7.", "MySQL 8.0."], "type": "string"}, "ipConfig": {"$ref": "SqlIpConfig", "description": "The settings for IP Management. This allows to enable or disable the instance IP and manage which external networks can connect to the instance. The IPv4 address cannot be disabled."}, "rootPassword": {"description": "Input only. Initial root password.", "type": "string"}, "rootPasswordSet": {"description": "Output only. Indicates If this connection profile root password is stored.", "readOnly": true, "type": "boolean"}, "sourceId": {"description": "The Database Migration Service source connection profile ID, in the format: `projects/my_project_name/locations/us-central1/connectionProfiles/connection_profile_ID`", "type": "string"}, "storageAutoResizeLimit": {"description": "The maximum size to which storage capacity can be automatically increased. The default value is 0, which specifies that there is no limit.", "format": "int64", "type": "string"}, "tier": {"description": "The tier (or machine type) for this instance, for example: `db-n1-standard-1` (MySQL instances). For more information, see [Cloud SQL Instance Settings](https://cloud.google.com/sql/docs/mysql/instance-settings).", "type": "string"}, "userLabels": {"additionalProperties": {"type": "string"}, "description": "The resource labels for a Cloud SQL instance to use to annotate any related underlying resources such as Compute Engine VMs. An object containing a list of \"key\": \"value\" pairs. Example: `{ \"name\": \"wrench\", \"mass\": \"18kg\", \"count\": \"3\" }`.", "type": "object"}, "zone": {"description": "The Google Cloud Platform zone where your Cloud SQL database instance is located.", "type": "string"}}, "type": "object"}, "ConnectionProfile": {"description": "A connection profile definition.", "id": "ConnectionProfile", "properties": {"cloudsql": {"$ref": "CloudSqlConnectionProfile", "description": "A CloudSQL database connection profile."}, "createTime": {"description": "Output only. The timestamp when the resource was created. A timestamp in RFC3339 UTC \"Zulu\" format, accurate to nanoseconds. Example: \"2014-10-02T15:01:23.045123456Z\".", "format": "google-datetime", "readOnly": true, "type": "string"}, "displayName": {"description": "The connection profile display name.", "type": "string"}, "error": {"$ref": "Status", "description": "Output only. The error details in case of state FAILED.", "readOnly": true}, "labels": {"additionalProperties": {"type": "string"}, "description": "The resource labels for connection profile to use to annotate any related underlying resources such as Compute Engine VMs. An object containing a list of \"key\": \"value\" pairs. Example: `{ \"name\": \"wrench\", \"mass\": \"1.3kg\", \"count\": \"3\" }`.", "type": "object"}, "mysql": {"$ref": "MySqlConnectionProfile", "description": "A MySQL database connection profile."}, "name": {"description": "The name of this connection profile resource in the form of projects/{project}/locations/{location}/connectionProfiles/{connectionProfile}.", "type": "string"}, "provider": {"description": "The database provider.", "enum": ["DATABASE_PROVIDER_UNSPECIFIED", "CLOUDSQL", "RDS"], "enumDescriptions": ["The database provider is unknown.", "CloudSQL runs the database.", "RDS runs the database."], "type": "string"}, "state": {"description": "The current connection profile state (e.g. DRAFT, READY, or FAILED).", "enum": ["STATE_UNSPECIFIED", "DRAFT", "CREATING", "READY", "UPDATING", "DELETING", "DELETED", "FAILED"], "enumDescriptions": ["The state of the connection profile is unknown.", "The connection profile is in draft mode and fully editable.", "The connection profile is being created.", "The connection profile is ready.", "The connection profile is being updated.", "The connection profile is being deleted.", "The connection profile has been deleted.", "The last action on the connection profile failed."], "type": "string"}, "updateTime": {"description": "Output only. The timestamp when the resource was last updated. A timestamp in RFC3339 UTC \"Zulu\" format, accurate to nanoseconds. Example: \"2014-10-02T15:01:23.045123456Z\".", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "DatabaseType": {"description": "A message defining the database engine and provider.", "id": "DatabaseType", "properties": {"engine": {"description": "The database engine.", "enum": ["DATABASE_ENGINE_UNSPECIFIED", "MYSQL"], "enumDescriptions": ["The source database engine of the migration job is unknown.", "The source engine is MySQL."], "type": "string"}, "provider": {"description": "The database provider.", "enum": ["DATABASE_PROVIDER_UNSPECIFIED", "CLOUDSQL", "RDS"], "enumDescriptions": ["The database provider is unknown.", "CloudSQL runs the database.", "RDS runs the database."], "type": "string"}}, "type": "object"}, "Empty": {"description": "A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }", "id": "Empty", "properties": {}, "type": "object"}, "Expr": {"description": "Represents a textual expression in the Common Expression Language (CEL) syntax. CEL is a C-like expression language. The syntax and semantics of CEL are documented at https://github.com/google/cel-spec. Example (Comparison): title: \"Summary size limit\" description: \"Determines if a summary is less than 100 chars\" expression: \"document.summary.size() < 100\" Example (Equality): title: \"Requestor is owner\" description: \"Determines if requestor is the document owner\" expression: \"document.owner == request.auth.claims.email\" Example (Logic): title: \"Public documents\" description: \"Determine whether the document should be publicly visible\" expression: \"document.type != 'private' && document.type != 'internal'\" Example (Data Manipulation): title: \"Notification string\" description: \"Create a notification string with a timestamp.\" expression: \"'New message received at ' + string(document.create_time)\" The exact variables and functions that may be referenced within an expression are determined by the service that evaluates it. See the service documentation for additional information.", "id": "Expr", "properties": {"description": {"description": "Optional. Description of the expression. This is a longer text which describes the expression, e.g. when hovered over it in a UI.", "type": "string"}, "expression": {"description": "Textual representation of an expression in Common Expression Language syntax.", "type": "string"}, "location": {"description": "Optional. String indicating the location of the expression for error reporting, e.g. a file name and a position in the file.", "type": "string"}, "title": {"description": "Optional. Title for the expression, i.e. a short string describing its purpose. This can be used e.g. in UIs which allow to enter the expression.", "type": "string"}}, "type": "object"}, "GenerateSshScriptRequest": {"description": "Request message for 'GenerateSshScript' request.", "id": "GenerateSshScriptRequest", "properties": {"vm": {"description": "Required. Bastion VM Instance name to use or to create.", "type": "string"}, "vmCreationConfig": {"$ref": "VmCreationConfig", "description": "The VM creation configuration"}, "vmPort": {"description": "The port that will be open on the bastion host", "format": "int32", "type": "integer"}, "vmSelectionConfig": {"$ref": "VmSelectionConfig", "description": "The VM selection configuration"}}, "type": "object"}, "GoogleCloudClouddmsV1beta1OperationMetadata": {"description": "Represents the metadata of the long-running operation.", "id": "GoogleCloudClouddmsV1beta1OperationMetadata", "properties": {"apiVersion": {"description": "Output only. API version used to start the operation.", "readOnly": true, "type": "string"}, "createTime": {"description": "Output only. The time the operation was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "endTime": {"description": "Output only. The time the operation finished running.", "format": "google-datetime", "readOnly": true, "type": "string"}, "requestedCancellation": {"description": "Output only. Identifies whether the user has requested cancellation of the operation. Operations that have successfully been cancelled have Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.", "readOnly": true, "type": "boolean"}, "statusMessage": {"description": "Output only. Human-readable status of the operation, if any.", "readOnly": true, "type": "string"}, "target": {"description": "Output only. Server-defined resource path for the target of the operation.", "readOnly": true, "type": "string"}, "verb": {"description": "Output only. Name of the verb executed by the operation.", "readOnly": true, "type": "string"}}, "type": "object"}, "ListConnectionProfilesResponse": {"description": "Response message for 'ListConnectionProfiles' request.", "id": "ListConnectionProfilesResponse", "properties": {"connectionProfiles": {"description": "The response list of connection profiles.", "items": {"$ref": "ConnectionProfile"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListLocationsResponse": {"description": "The response message for Locations.ListLocations.", "id": "ListLocationsResponse", "properties": {"locations": {"description": "A list of locations that matches the specified filter in the request.", "items": {"$ref": "Location"}, "type": "array"}, "nextPageToken": {"description": "The standard List next-page token.", "type": "string"}}, "type": "object"}, "ListMigrationJobsResponse": {"description": "Response message for 'ListMigrationJobs' request.", "id": "ListMigrationJobsResponse", "properties": {"migrationJobs": {"description": "The list of migration jobs objects.", "items": {"$ref": "<PERSON><PERSON><PERSON><PERSON>"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListOperationsResponse": {"description": "The response message for Operations.ListOperations.", "id": "ListOperationsResponse", "properties": {"nextPageToken": {"description": "The standard List next-page token.", "type": "string"}, "operations": {"description": "A list of operations that matches the specified filter in the request.", "items": {"$ref": "Operation"}, "type": "array"}}, "type": "object"}, "Location": {"description": "A resource that represents a Google Cloud location.", "id": "Location", "properties": {"displayName": {"description": "The friendly name for this location, typically a nearby city name. For example, \"Tokyo\".", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Cross-service attributes for the location. For example {\"cloud.googleapis.com/region\": \"us-east1\"}", "type": "object"}, "locationId": {"description": "The canonical id for this location. For example: `\"us-east1\"`.", "type": "string"}, "metadata": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "Service-specific metadata. For example the available capacity at the given location.", "type": "object"}, "name": {"description": "Resource name for the location, which may vary between implementations. For example: `\"projects/example-project/locations/us-east1\"`", "type": "string"}}, "type": "object"}, "MigrationJob": {"description": "Represents a Database Migration Service migration job object.", "id": "<PERSON><PERSON><PERSON><PERSON>", "properties": {"createTime": {"description": "Output only. The timestamp when the migration job resource was created. A timestamp in RFC3339 UTC \"Zulu\" format, accurate to nanoseconds. Example: \"2014-10-02T15:01:23.045123456Z\".", "format": "google-datetime", "readOnly": true, "type": "string"}, "destination": {"description": "Required. The resource name (URI) of the destination connection profile.", "type": "string"}, "destinationDatabase": {"$ref": "DatabaseType", "description": "The database engine type and provider of the destination."}, "displayName": {"description": "The migration job display name.", "type": "string"}, "dumpPath": {"description": "The path to the dump file in Google Cloud Storage, in the format: (gs://[BUCKET_NAME]/[OBJECT_NAME]).", "type": "string"}, "duration": {"description": "Output only. The duration of the migration job (in seconds). A duration in seconds with up to nine fractional digits, terminated by 's'. Example: \"3.5s\".", "format": "google-duration", "readOnly": true, "type": "string"}, "endTime": {"description": "Output only. If the migration job is completed, the time when it was completed.", "format": "google-datetime", "readOnly": true, "type": "string"}, "error": {"$ref": "Status", "description": "Output only. The error details in case of state FAILED.", "readOnly": true}, "labels": {"additionalProperties": {"type": "string"}, "description": "The resource labels for migration job to use to annotate any related underlying resources such as Compute Engine VMs. An object containing a list of \"key\": \"value\" pairs. Example: `{ \"name\": \"wrench\", \"mass\": \"1.3kg\", \"count\": \"3\" }`.", "type": "object"}, "name": {"description": "The name (URI) of this migration job resource, in the form of: projects/{project}/locations/{location}/migrationJobs/{migrationJob}.", "type": "string"}, "phase": {"description": "Output only. The current migration job phase.", "enum": ["PHASE_UNSPECIFIED", "FULL_DUMP", "CDC", "PROMOTE_IN_PROGRESS", "WAITING_FOR_SOURCE_WRITES_TO_STOP", "PREPARING_THE_DUMP"], "enumDescriptions": ["The phase of the migration job is unknown.", "The migration job is in the full dump phase.", "The migration job is CDC phase.", "The migration job is running the promote phase.", "Only RDS flow - waiting for source writes to stop", "Only RDS flow - the sources writes stopped, waiting for dump to begin"], "readOnly": true, "type": "string"}, "reverseSshConnectivity": {"$ref": "ReverseSshConnectivity", "description": "The details needed to communicate to the source over Reverse SSH tunnel connectivity."}, "source": {"description": "Required. The resource name (URI) of the source connection profile.", "type": "string"}, "sourceDatabase": {"$ref": "DatabaseType", "description": "The database engine type and provider of the source."}, "state": {"description": "The current migration job state.", "enum": ["STATE_UNSPECIFIED", "MAINTENANCE", "DRAFT", "CREATING", "NOT_STARTED", "RUNNING", "FAILED", "COMPLETED", "DELETING", "STOPPING", "STOPPED", "DELETED", "UPDATING", "STARTING", "RESTARTING", "RESUMING"], "enumDescriptions": ["The state of the migration job is unknown.", "The migration job is down for maintenance.", "The migration job is in draft mode and no resources are created.", "The migration job is being created.", "The migration job is created and not started.", "The migration job is running.", "The migration job failed.", "The migration job has been completed.", "The migration job is being deleted.", "The migration job is being stopped.", "The migration job is currently stopped.", "The migration job has been deleted.", "The migration job is being updated.", "The migration job is starting.", "The migration job is restarting.", "The migration job is resuming."], "type": "string"}, "staticIpConnectivity": {"$ref": "StaticIpConnectivity", "description": "static ip connectivity data (default, no additional details needed)."}, "type": {"description": "Required. The migration job type.", "enum": ["TYPE_UNSPECIFIED", "ONE_TIME", "CONTINUOUS"], "enumDescriptions": ["The type of the migration job is unknown.", "The migration job is a one time migration.", "The migration job is a continuous migration."], "type": "string"}, "updateTime": {"description": "Output only. The timestamp when the migration job resource was last updated. A timestamp in RFC3339 UTC \"Zulu\" format, accurate to nanoseconds. Example: \"2014-10-02T15:01:23.045123456Z\".", "format": "google-datetime", "readOnly": true, "type": "string"}, "vpcPeeringConnectivity": {"$ref": "VpcPeeringConnectivity", "description": "The details of the VPC network that the source database is located in."}}, "type": "object"}, "MigrationJobVerificationError": {"description": "Error message of a verification Migration job.", "id": "MigrationJobVerificationError", "properties": {"errorCode": {"description": "Output only. An instance of ErrorCode specifying the error that occurred.", "enum": ["ERROR_CODE_UNSPECIFIED", "CONNECTION_FAILURE", "AUTHENTICATION_FAILURE", "INVALID_CONNECTION_PROFILE_CONFIG", "VERSION_INCOMPATIBILITY", "CONNECTION_PROFILE_TYPES_INCOMPATIBILITY", "UNSUPPORTED_GTID_MODE", "UNSUPPORTED_DEFINER", "CANT_RESTART_RUNNING_MIGRATION", "TABLES_WITH_LIMITED_SUPPORT", "UNSUPPORTED_DATABASE_LOCALE", "UNSUPPORTED_DATABASE_FDW_CONFIG", "ERROR_RDBMS", "SOURCE_SIZE_EXCEEDS_THRESHOLD", "EXISTING_CONFLICTING_DATABASES", "PARALLEL_IMPORT_INSUFFICIENT_PRIVILEGE", "EXISTING_DATA", "SOURCE_MAX_SUBSCRIPTIONS"], "enumDescriptions": ["An unknown error occurred", "We failed to connect to one of the connection profile.", "We failed to authenticate to one of the connection profile.", "One of the involved connection profiles has an invalid configuration.", "The versions of the source and the destination are incompatible.", "The types of the source and the destination are incompatible.", "The gtid_mode is not supported, applicable for MySQL.", "The definer is not supported.", "Migration is already running at the time of restart request.", "The source has tables with limited support. E.g. PostgreSQL tables without primary keys.", "The source uses an unsupported locale.", "The source uses an unsupported Foreign Data Wrapper configuration.", "There was an underlying RDBMS error.", "The source DB size in Bytes exceeds a certain threshold. The migration might require an increase of quota, or might not be supported.", "The destination DB contains existing databases that are conflicting with those in the source DB.", "Insufficient privilege to enable the parallelism configuration.", "The destination instance contains existing data or user defined entities (for example databases, tables, or functions). You can only migrate to empty instances. Clear your destination instance and retry the migration job.", "The migration job is configured to use max number of subscriptions to migrate data from the source to the destination."], "readOnly": true, "type": "string"}, "errorDetailMessage": {"description": "Output only. A specific detailed error message, if supplied by the engine.", "readOnly": true, "type": "string"}, "errorMessage": {"description": "Output only. A formatted message with further details about the error and a CTA.", "readOnly": true, "type": "string"}}, "type": "object"}, "MySqlConnectionProfile": {"description": "Specifies connection parameters required specifically for MySQL databases.", "id": "MySqlConnectionProfile", "properties": {"cloudSqlId": {"description": "If the source is a Cloud SQL database, use this field to provide the Cloud SQL instance ID of the source.", "type": "string"}, "host": {"description": "Required. The IP or hostname of the source MySQL database.", "type": "string"}, "password": {"description": "Required. Input only. The password for the user that Database Migration Service will be using to connect to the database. This field is not returned on request, and the value is encrypted when stored in Database Migration Service.", "type": "string"}, "passwordSet": {"description": "Output only. Indicates If this connection profile password is stored.", "readOnly": true, "type": "boolean"}, "port": {"description": "Required. The network port of the source MySQL database.", "format": "int32", "type": "integer"}, "ssl": {"$ref": "SslConfig", "description": "SSL configuration for the destination to connect to the source database."}, "username": {"description": "Required. The username that Database Migration Service will use to connect to the database. The value is encrypted when stored in Database Migration Service.", "type": "string"}}, "type": "object"}, "Operation": {"description": "This resource represents a long-running operation that is the result of a network API call.", "id": "Operation", "properties": {"done": {"description": "If the value is `false`, it means the operation is still in progress. If `true`, the operation is completed, and either `error` or `response` is available.", "type": "boolean"}, "error": {"$ref": "Status", "description": "The error result of the operation in case of failure or cancellation."}, "metadata": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata. Any method that returns a long-running operation should document the metadata type, if any.", "type": "object"}, "name": {"description": "The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the `name` should be a resource name ending with `operations/{unique_id}`.", "type": "string"}, "response": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "The normal, successful response of the operation. If the original method returns no data on success, such as `Delete`, the response is `google.protobuf.Empty`. If the original method is standard `Get`/`Create`/`Update`, the response should be the resource. For other methods, the response should have the type `XxxResponse`, where `Xxx` is the original method name. For example, if the original method name is `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.", "type": "object"}}, "type": "object"}, "Policy": {"description": "An Identity and Access Management (IAM) policy, which specifies access controls for Google Cloud resources. A `Policy` is a collection of `bindings`. A `binding` binds one or more `members`, or principals, to a single `role`. Principals can be user accounts, service accounts, Google groups, and domains (such as G Suite). A `role` is a named list of permissions; each `role` can be an IAM predefined role or a user-created custom role. For some types of Google Cloud resources, a `binding` can also specify a `condition`, which is a logical expression that allows access to a resource only if the expression evaluates to `true`. A condition can add constraints based on attributes of the request, the resource, or both. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies). **JSON example:** ``` { \"bindings\": [ { \"role\": \"roles/resourcemanager.organizationAdmin\", \"members\": [ \"user:<EMAIL>\", \"group:<EMAIL>\", \"domain:google.com\", \"serviceAccount:<EMAIL>\" ] }, { \"role\": \"roles/resourcemanager.organizationViewer\", \"members\": [ \"user:<EMAIL>\" ], \"condition\": { \"title\": \"expirable access\", \"description\": \"Does not grant access after Sep 2020\", \"expression\": \"request.time < timestamp('2020-10-01T00:00:00.000Z')\", } } ], \"etag\": \"BwWWja0YfJA=\", \"version\": 3 } ``` **YAML example:** ``` bindings: - members: - user:<EMAIL> - group:<EMAIL> - domain:google.com - serviceAccount:<EMAIL> role: roles/resourcemanager.organizationAdmin - members: - user:<EMAIL> role: roles/resourcemanager.organizationViewer condition: title: expirable access description: Does not grant access after Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z') etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features, see the [IAM documentation](https://cloud.google.com/iam/docs/).", "id": "Policy", "properties": {"auditConfigs": {"description": "Specifies cloud audit logging configuration for this policy.", "items": {"$ref": "AuditConfig"}, "type": "array"}, "bindings": {"description": "Associates a list of `members`, or principals, with a `role`. Optionally, may specify a `condition` that determines how and when the `bindings` are applied. Each of the `bindings` must contain at least one principal. The `bindings` in a `Policy` can refer to up to 1,500 principals; up to 250 of these principals can be Google groups. Each occurrence of a principal counts towards these limits. For example, if the `bindings` grant 50 different roles to `user:<EMAIL>`, and not to any other principal, then you can add another 1,450 principals to the `bindings` in the `Policy`.", "items": {"$ref": "Binding"}, "type": "array"}, "etag": {"description": "`etag` is used for optimistic concurrency control as a way to help prevent simultaneous updates of a policy from overwriting each other. It is strongly suggested that systems make use of the `etag` in the read-modify-write cycle to perform policy updates in order to avoid race conditions: An `etag` is returned in the response to `getIamPolicy`, and systems are expected to put that etag in the request to `setIamPolicy` to ensure that their change will be applied to the same version of the policy. **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost.", "format": "byte", "type": "string"}, "version": {"description": "Specifies the format of the policy. Valid values are `0`, `1`, and `3`. Requests that specify an invalid value are rejected. Any operation that affects conditional role bindings must specify version `3`. This requirement applies to the following operations: * Getting a policy that includes a conditional role binding * Adding a conditional role binding to a policy * Changing a conditional role binding in a policy * Removing any role binding, with or without a condition, from a policy that includes conditions **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost. If a policy does not include any conditions, operations on that policy may specify any valid version or leave the field unset. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "type": "integer"}}, "type": "object"}, "PromoteMigrationJobRequest": {"description": "Request message for 'PromoteMigrationJob' request.", "id": "PromoteMigrationJobRequest", "properties": {}, "type": "object"}, "RestartMigrationJobRequest": {"description": "Request message for 'RestartMigrationJob' request.", "id": "RestartMigrationJobRequest", "properties": {}, "type": "object"}, "ResumeMigrationJobRequest": {"description": "Request message for 'ResumeMigrationJob' request.", "id": "ResumeMigrationJobRequest", "properties": {}, "type": "object"}, "ReverseSshConnectivity": {"description": "The details needed to configure a reverse SSH tunnel between the source and destination databases. These details will be used when calling the generateSshScript method (see https://cloud.google.com/database-migration/docs/reference/rest/v1beta1/projects.locations.migrationJobs/generateSshScript) to produce the script that will help set up the reverse SSH tunnel, and to set up the VPC peering between the Cloud SQL private network and the VPC.", "id": "ReverseSshConnectivity", "properties": {"vm": {"description": "The name of the virtual machine (Compute Engine) used as the bastion server for the SSH tunnel.", "type": "string"}, "vmIp": {"description": "Required. The IP of the virtual machine (Compute Engine) used as the bastion server for the SSH tunnel.", "type": "string"}, "vmPort": {"description": "Required. The forwarding port of the virtual machine (Compute Engine) used as the bastion server for the SSH tunnel.", "format": "int32", "type": "integer"}, "vpc": {"description": "The name of the VPC to peer with the Cloud SQL private network.", "type": "string"}}, "type": "object"}, "SetIamPolicyRequest": {"description": "Request message for `SetIamPolicy` method.", "id": "SetIamPolicyRequest", "properties": {"policy": {"$ref": "Policy", "description": "REQUIRED: The complete policy to be applied to the `resource`. The size of the policy is limited to a few 10s of KB. An empty policy is a valid policy but certain Google Cloud services (such as Projects) might reject them."}, "updateMask": {"description": "OPTIONAL: A FieldMask specifying which fields of the policy to modify. Only the fields in the mask will be modified. If no mask is provided, the following default mask is used: `paths: \"bindings, etag\"`", "format": "google-fieldmask", "type": "string"}}, "type": "object"}, "SqlAclEntry": {"description": "An entry for an Access Control list.", "id": "SqlAclEntry", "properties": {"expireTime": {"description": "The time when this access control entry expires in [RFC 3339](https://tools.ietf.org/html/rfc3339) format, for example: `2012-11-15T16:19:00.094Z`.", "format": "google-datetime", "type": "string"}, "label": {"description": "A label to identify this entry.", "type": "string"}, "ttl": {"description": "Input only. The time-to-leave of this access control entry.", "format": "google-duration", "type": "string"}, "value": {"description": "The allowlisted value for the access control list.", "type": "string"}}, "type": "object"}, "SqlIpConfig": {"description": "IP Management configuration.", "id": "SqlIpConfig", "properties": {"authorizedNetworks": {"description": "The list of external networks that are allowed to connect to the instance using the IP. See https://en.wikipedia.org/wiki/CIDR_notation#CIDR_notation, also known as 'slash' notation (e.g. `*************/24`).", "items": {"$ref": "SqlAclEntry"}, "type": "array"}, "enableIpv4": {"description": "Whether the instance is assigned a public IP address or not.", "type": "boolean"}, "privateNetwork": {"description": "The resource link for the VPC network from which the Cloud SQL instance is accessible for private IP. For example, `/projects/myProject/global/networks/default`. This setting can be updated, but it cannot be removed after it is set.", "type": "string"}, "requireSsl": {"description": "Whether SSL connections over IP should be enforced or not.", "type": "boolean"}}, "type": "object"}, "SshScript": {"description": "Response message for 'GenerateSshScript' request.", "id": "SshScript", "properties": {"script": {"description": "The ssh configuration script.", "type": "string"}}, "type": "object"}, "SslConfig": {"description": "SSL configuration information.", "id": "SslConfig", "properties": {"caCertificate": {"description": "Required. Input only. The x509 PEM-encoded certificate of the CA that signed the source database server's certificate. The replica will use this certificate to verify it's connecting to the right host.", "type": "string"}, "clientCertificate": {"description": "Input only. The x509 PEM-encoded certificate that will be used by the replica to authenticate against the source database server.If this field is used then the 'client_key' field is mandatory.", "type": "string"}, "clientKey": {"description": "Input only. The unencrypted PKCS#1 or PKCS#8 PEM-encoded private key associated with the Client Certificate. If this field is used then the 'client_certificate' field is mandatory.", "type": "string"}, "type": {"description": "Output only. The ssl config type according to 'client_key', 'client_certificate' and 'ca_certificate'.", "enum": ["SSL_TYPE_UNSPECIFIED", "SERVER_ONLY", "SERVER_CLIENT"], "enumDescriptions": ["Unspecified.", "Only 'ca_certificate' specified.", "Both server ('ca_certificate'), and client ('client_key', 'client_certificate') specified."], "readOnly": true, "type": "string"}}, "type": "object"}, "StartMigrationJobRequest": {"description": "Request message for 'StartMigrationJob' request.", "id": "StartMigrationJobRequest", "properties": {}, "type": "object"}, "StaticIpConnectivity": {"description": "The source database will allow incoming connections from the destination database's public IP. You can retrieve the Cloud SQL instance's public IP from the Cloud SQL console or using Cloud SQL APIs. No additional configuration is required.", "id": "StaticIpConnectivity", "properties": {}, "type": "object"}, "Status": {"description": "The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).", "id": "Status", "properties": {"code": {"description": "The status code, which should be an enum value of google.rpc.Code.", "format": "int32", "type": "integer"}, "details": {"description": "A list of messages that carry the error details. There is a common set of message types for APIs to use.", "items": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "type": "object"}, "type": "array"}, "message": {"description": "A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.", "type": "string"}}, "type": "object"}, "StopMigrationJobRequest": {"description": "Request message for 'StopMigrationJob' request.", "id": "StopMigrationJobRequest", "properties": {}, "type": "object"}, "TestIamPermissionsRequest": {"description": "Request message for `TestIamPermissions` method.", "id": "TestIamPermissionsRequest", "properties": {"permissions": {"description": "The set of permissions to check for the `resource`. Permissions with wildcards (such as `*` or `storage.*`) are not allowed. For more information see [IAM Overview](https://cloud.google.com/iam/docs/overview#permissions).", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "TestIamPermissionsResponse": {"description": "Response message for `TestIamPermissions` method.", "id": "TestIamPermissionsResponse", "properties": {"permissions": {"description": "A subset of `TestPermissionsRequest.permissions` that the caller is allowed.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "VerifyMigrationJobRequest": {"description": "Request message for 'VerifyMigrationJob' request.", "id": "VerifyMigrationJobRequest", "properties": {}, "type": "object"}, "VmCreationConfig": {"description": "VM creation configuration message", "id": "VmCreationConfig", "properties": {"subnet": {"description": "The subnet name the vm needs to be created in.", "type": "string"}, "vmMachineType": {"description": "Required. VM instance machine type to create.", "type": "string"}, "vmZone": {"description": "The Google Cloud Platform zone to create the VM in.", "type": "string"}}, "type": "object"}, "VmSelectionConfig": {"description": "VM selection configuration message", "id": "VmSelectionConfig", "properties": {"vmZone": {"description": "Required. The Google Cloud Platform zone the VM is located.", "type": "string"}}, "type": "object"}, "VpcPeeringConnectivity": {"description": "The details of the VPC where the source database is located in Google Cloud. We will use this information to set up the VPC peering connection between Cloud SQL and this VPC.", "id": "VpcPeeringConnectivity", "properties": {"vpc": {"description": "The name of the VPC network to peer with the Cloud SQL private network.", "type": "string"}}, "type": "object"}}, "servicePath": "", "title": "Database Migration API", "version": "v1beta1", "version_module": true}