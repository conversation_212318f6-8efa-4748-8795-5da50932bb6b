<div class="flDebugPanelTitle">
  <a class="flDebugClose flDebugBack" href="">Back</a>
  <h3>SQL Details</h3>
</div>
<div class="flDebugPanelContent">
  <div class="flDebugScroll">
    <dl>
      <dt>Executed SQL</dt>
      <dd>{{ sql }}</dd>
      <dt>Original query duration</dt>
      <dd>{{ '%.4f'|format(duration * 1000) }} ms</dd>
    </dl>
    {% if result %}
    <table class="flDebugSelect">
      <thead>
        <tr>
          {% for h in headers %}
            <th>{{ h|upper }}</th>
          {% endfor %}
        </tr>
      </thead>
      <tbody>
        {% for row in result %}
            <tr class="{{ loop.cycle('flDebugOdd', 'flDebugEven') }}">
            {% for column in row %}
              <td>{{ column }}</td>
            {% endfor %}
          </tr>
        {% endfor %}
      </tbody>
    </table>
    {% else %}
    <p>Empty set</p>
    {% endif %}
  </div>
</div>
