from typing import Dict

from .. import Provider as LoremProvider


class Provider(LoremProvider):
    """Implement lorem provider for ``zh_CN`` locale."""

    word_connector = ""
    word_list = (
        "一个",
        "我们",
        "时间",
        "中国",
        "可以",
        "公司",
        "没有",
        "信息",
        "下载",
        "软件",
        "注册",
        "自己",
        "产品",
        "工作",
        "论坛",
        "企业",
        "这个",
        "他们",
        "管理",
        "已经",
        "问题",
        "内容",
        "使用",
        "进行",
        "市场",
        "服务",
        "如果",
        "系统",
        "技术",
        "发展",
        "现在",
        "作者",
        "就是",
        "网络",
        "提供",
        "相关",
        "我的",
        "文章",
        "方式",
        "电话",
        "发表",
        "所有",
        "时候",
        "因为",
        "北京",
        "有限",
        "公司",
        "什么",
        "还是",
        "开始",
        "本站",
        "发布",
        "自己",
        "支持",
        "在线",
        "国家",
        "生活",
        "联系",
        "积分",
        "主题",
        "所以",
        "不能",
        "的人",
        "上海",
        "中心",
        "世界",
        "游戏",
        "需要",
        "价格",
        "用户",
        "通过",
        "要求",
        "不是",
        "免费",
        "个人",
        "但是",
        "地址",
        "网站",
        "情况",
        "最后",
        "设计",
        "同时",
        "这些",
        "活动",
        "手机",
        "推荐",
        "一些",
        "主要",
        "大家",
        "发现",
        "目前",
        "文件",
        "你的",
        "不过",
        "评论",
        "生产",
        "美国",
        "图片",
        "经济",
        "功能",
        "国际",
        "的是",
        "选择",
        "其他",
        "这样",
        "会员",
        "环境",
        "来自",
        "日期",
        "成为",
        "他的",
        "最新",
        "专业",
        "一下",
        "人员",
        "任何",
        "教育",
        "资料",
        "状态",
        "都是",
        "点击",
        "为了",
        "不会",
        "出现",
        "知道",
        "社会",
        "名称",
        "而且",
        "介绍",
        "音乐",
        "等级",
        "可能",
        "这种",
        "建设",
        "朋友",
        "虽然",
        "电子",
        "资源",
        "看到",
        "精华",
        "电影",
        "如何",
        "新闻",
        "阅读",
        "安全",
        "全国",
        "只有",
        "回复",
        "大学",
        "学生",
        "学习",
        "关于",
        "项目",
        "不同",
        "以及",
        "有关",
        "那么",
        "开发",
        "还有",
        "只是",
        "非常",
        "研究",
        "广告",
        "首页",
        "方法",
        "希望",
        "地方",
        "也是",
        "单位",
        "怎么",
        "应该",
        "今天",
        "以上",
        "更新",
        "帖子",
        "显示",
        "能力",
        "电脑",
        "记者",
        "查看",
        "位置",
        "不要",
        "由于",
        "无法",
        "详细",
        "投资",
        "是一",
        "一般",
        "进入",
        "发生",
        "这里",
        "感觉",
        "更多",
        "你们",
        "的话",
        "起来",
        "标准",
        "一样",
        "认为",
        "女人",
        "那个",
        "设备",
        "搜索",
        "之后",
        "然后",
        "学校",
        "销售",
        "组织",
        "说明",
        "提高",
        "为什",
        "作品",
        "或者",
        "喜欢",
        "东西",
        "方面",
        "简介",
        "必须",
        "经营",
        "科技",
        "作为",
        "其中",
        "运行",
        "工程",
        "解决",
        "操作",
        "经验",
        "地区",
        "重要",
        "直接",
        "登录",
        "合作",
        "结果",
        "影响",
        "这是",
        "行业",
        "对于",
        "表示",
        "程序",
        "包括",
        "留言",
        "规定",
        "处理",
        "男人",
        "各种",
        "部门",
        "数据",
        "具有",
        "商品",
        "系列",
        "大小",
        "因此",
        "关系",
        "可是",
        "比较",
        "文化",
        "一直",
        "法律",
        "这么",
        "您的",
        "城市",
        "分析",
        "基本",
        "最大",
        "类别",
        "两个",
        "日本",
        "得到",
        "一次",
        "继续",
        "成功",
        "她的",
        "责任",
        "深圳",
        "业务",
        "欢迎",
        "加入",
        "能够",
        "觉得",
        "部分",
        "中文",
        "根据",
        "人民",
        "政府",
        "控制",
        "其实",
        "之间",
        "一种",
        "威望",
        "实现",
        "语言",
        "出来",
        "谢谢",
        "社区",
        "品牌",
        "是否",
        "工具",
        "完全",
        "决定",
        "很多",
        "网上",
        "事情",
        "今年",
        "国内",
        "以后",
        "制作",
        "浏览",
        "过程",
        "完成",
        "类型",
        "来源",
        "质量",
        "有些",
        "一起",
        "当然",
        "汽车",
        "一点",
        "帮助",
        "增加",
        "历史",
        "以下",
        "不断",
        "应用",
        "那些",
        "密码",
        "计划",
        "如此",
        "次数",
        "到了",
        "拥有",
        "孩子",
        "原因",
        "参加",
        "只要",
        "报告",
        "当前",
        "客户",
        "正在",
        "注意",
        "标题",
        "空间",
        "一定",
        "一切",
        "特别",
        "全部",
        "准备",
    )

    parts_of_speech: Dict[str, tuple] = {}
