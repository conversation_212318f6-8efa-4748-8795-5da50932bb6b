import os
import csv
from collections import defaultdict

# Extensions de fichiers à compter avec leurs langages
EXT_TO_LANG = {
    ".py": "Python",
    ".js": "JavaScript",
    ".ts": "TypeScript",
    ".html": "HTML",
    ".css": "CSS",
    ".scss": "SCSS",
    ".vue": "Vue.js",
    ".json": "JSON",
    ".md": "Markdown",
    ".yml": "YAML",
    ".yaml": "YAML",
    ".sql": "SQL"
}

# Dossiers à exclure
EXCLUDE_DIRS = {
    "venv", "vv_pos_18", "__pycache__", "node_modules", "dist", "build",
    ".git", ".idea", ".vscode", "migrations", "tests", "instance", 
}

def count_lines_in_file(filepath):
    """Compte les lignes dans un fichier en UTF-8 (ignore erreurs)"""
    try:
        with open(filepath, "r", encoding="utf-8", errors="ignore") as f:
            return sum(1 for _ in f)
    except Exception:
        return 0

def analyze_project(root_dir):
    """Analyse un projet et retourne les stats par langage"""
    stats = defaultdict(lambda: {"files": 0, "lines": 0})

    for dirpath, dirnames, filenames in os.walk(root_dir):
        # On ignore les dossiers exclus
        dirnames[:] = [d for d in dirnames if d not in EXCLUDE_DIRS]

        for filename in filenames:
            _, ext = os.path.splitext(filename)
            ext = ext.lower()

            if ext in EXT_TO_LANG:
                filepath = os.path.join(dirpath, filename)
                lines = count_lines_in_file(filepath)
                lang = EXT_TO_LANG[ext]

                stats[lang]["files"] += 1
                stats[lang]["lines"] += lines

    return stats

def print_stats(stats):
    """Affiche joliment les stats"""
    total_files = sum(s["files"] for s in stats.values())
    total_lines = sum(s["lines"] for s in stats.values())

    print("\n📊 Résultat du comptage :")
    print(f"  📁 Fichiers analysés : {total_files}")
    print(f"  📄 Total lignes de code : {total_lines:,}".replace(",", " "))
    print(f"  📏 Moyenne de lignes/fichier : {total_lines // total_files if total_files else 0}\n")

    print(f"{'Langage':<15} {'Fichiers':>10} {'Lignes':>15} {'Moyenne/fichier':>20}")
    print("-" * 60)
    for lang, data in sorted(stats.items(), key=lambda x: x[1]["lines"], reverse=True):
        avg = data["lines"] // data["files"] if data["files"] else 0
        print(f"{lang:<15} {data['files']:>10} {data['lines']:>15,} {avg:>20}".replace(",", " "))

def export_csv(stats, filename="loc_stats.csv"):
    """Exporte les stats en CSV"""
    with open(filename, "w", newline="", encoding="utf-8") as csvfile:
        writer = csv.writer(csvfile)
        writer.writerow(["Langage", "Fichiers", "Lignes", "Moyenne par fichier"])
        for lang, data in stats.items():
            avg = data["lines"] // data["files"] if data["files"] else 0
            writer.writerow([lang, data["files"], data["lines"], avg])
    print(f"\n✅ Statistiques exportées dans {filename}")

if __name__ == "__main__":
    project_path = "."  # Dossier courant
    stats = analyze_project(project_path)
    print_stats(stats)
    export_csv(stats)
