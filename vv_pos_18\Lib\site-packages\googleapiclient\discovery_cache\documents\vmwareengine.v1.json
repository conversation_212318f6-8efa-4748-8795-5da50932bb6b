{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/cloud-platform": {"description": "See, edit, configure, and delete your Google Cloud data and see the email address for your Google Account."}}}}, "basePath": "", "baseUrl": "https://vmwareengine.googleapis.com/", "batchPath": "batch", "canonicalName": "VMware Engine", "description": "The Google VMware Engine API lets you programmatically manage VMware environments.", "discoveryVersion": "v1", "documentationLink": "https://cloud.google.com/solutions/vmware-as-a-service", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "vmwareengine:v1", "kind": "discovery#restDescription", "mtlsRootUrl": "https://vmwareengine.mtls.googleapis.com/", "name": "vmwareengine", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"projects": {"resources": {"locations": {"methods": {"get": {"description": "Gets information about a location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}", "httpMethod": "GET", "id": "vmwareengine.projects.locations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Resource name for the location.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Location"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getDnsBindPermission": {"description": "Gets all the principals having bind permission on the intranet VPC associated with the consumer project granted by the Grant API. DnsBindPermission is a global resource and location can only be global.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/dnsBindPermission", "httpMethod": "GET", "id": "vmwareengine.projects.locations.getDnsBindPermission", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the resource which stores the users/service accounts having the permission to bind to the corresponding intranet VPC of the consumer project. DnsBindPermission is a global resource. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/global/dnsBindPermission`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/dnsBindPermission$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "DnsBindPermission"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists information about the supported locations for this service.", "flatPath": "v1/projects/{projectsId}/locations", "httpMethod": "GET", "id": "vmwareengine.projects.locations.list", "parameterOrder": ["name"], "parameters": {"extraLocationTypes": {"description": "Optional. A list of extra location types that should be used as conditions for controlling the visibility of the locations.", "location": "query", "repeated": true, "type": "string"}, "filter": {"description": "A filter to narrow down results to a preferred subset. The filtering language accepts strings like `\"displayName=tokyo\"`, and is documented in more detail in [AIP-160](https://google.aip.dev/160).", "location": "query", "type": "string"}, "name": {"description": "The resource that owns the locations collection, if applicable.", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "The maximum number of results to return. If not set, the service selects a default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token received from the `next_page_token` field in the response. Send that page token to receive the subsequent page.", "location": "query", "type": "string"}}, "path": "v1/{+name}/locations", "response": {"$ref": "ListLocationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"announcements": {"methods": {"get": {"description": "Retrieves a `Announcement` by its resource name.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/announcements/{announcementsId}", "httpMethod": "GET", "id": "vmwareengine.projects.locations.announcements.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the announcement to retrieve. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/us-west1-a/announcements/announcement-uuid`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/announcements/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Announcement"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists `Announcements` for a given region and project", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/announcements", "httpMethod": "GET", "id": "vmwareengine.projects.locations.announcements.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "A filter expression that matches resources returned in the response. The expression must specify the field name, a comparison operator, and the value that you want to use for filtering. The value must be a string, a number, or a boolean. The comparison operator must be `=`, `!=`, `>`, or `<`. For example, if you are filtering a list of announcement runs, you can exclude the ones named `example-announcement` by specifying `name != \"example-announcement\"`. You can also filter nested fields. To filter on multiple expressions, provide each separate expression within parentheses. For example: ``` (name = \"example-announcement\") (createTime > \"2021-04-12T08:15:10.40Z\") ``` By default, each expression is an `AND` expression. However, you can include `AND` and `OR` expressions explicitly. For example: ``` (name = \"announcement-1\") AND (createTime > \"2021-04-12T08:15:10.40Z\") OR (name = \"announcement-2\") ```", "location": "query", "type": "string"}, "orderBy": {"description": "Sorts list results by a certain order. By default, returned results are ordered by `name` in ascending order. You can also sort results in descending order based on the `name` value using `orderBy=\"name desc\"`. Currently, only ordering by `name` is supported.", "location": "query", "type": "string"}, "pageSize": {"description": "The maximum number of announcements to return in one page. The service may return fewer than this value. The maximum value is coerced to 1000. The default value of this field is 500.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `ListAnnouncements` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListAnnouncements` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The resource name of the location to be queried for announcements. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/us-west1-a`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/announcements", "response": {"$ref": "ListAnnouncementsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "dnsBindPermission": {"methods": {"grant": {"description": "Grants the bind permission to the customer provided principal(user / service account) to bind their DNS zone with the intranet VPC associated with the project. DnsBindPermission is a global resource and location can only be global.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/dnsBindPermission:grant", "httpMethod": "POST", "id": "vmwareengine.projects.locations.dnsBindPermission.grant", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the resource which stores the users/service accounts having the permission to bind to the corresponding intranet VPC of the consumer project. DnsBindPermission is a global resource. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/global/dnsBindPermission`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/dnsBindPermission$", "required": true, "type": "string"}}, "path": "v1/{+name}:grant", "request": {"$ref": "GrantDnsBindPermissionRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "revoke": {"description": "Revokes the bind permission from the customer provided principal(user / service account) on the intranet VPC associated with the consumer project. DnsBindPermission is a global resource and location can only be global.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/dnsBindPermission:revoke", "httpMethod": "POST", "id": "vmwareengine.projects.locations.dnsBindPermission.revoke", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the resource which stores the users/service accounts having the permission to bind to the corresponding intranet VPC of the consumer project. DnsBindPermission is a global resource. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/global/dnsBindPermission`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/dnsBindPermission$", "required": true, "type": "string"}}, "path": "v1/{+name}:revoke", "request": {"$ref": "RevokeDnsBindPermissionRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "networkPeerings": {"methods": {"create": {"description": "Creates a new network peering between the peer network and VMware Engine network provided in a `NetworkPeering` resource. NetworkPeering is a global resource and location can only be global.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/networkPeerings", "httpMethod": "POST", "id": "vmwareengine.projects.locations.networkPeerings.create", "parameterOrder": ["parent"], "parameters": {"networkPeeringId": {"description": "Required. The user-provided identifier of the new `NetworkPeering`. This identifier must be unique among `NetworkPeering` resources within the parent and becomes the final token in the name URI. The identifier must meet the following requirements: * Only contains 1-63 alphanumeric characters and hyphens * Begins with an alphabetical character * Ends with a non-hyphen character * Not formatted as a UUID * Complies with [RFC 1034](https://datatracker.ietf.org/doc/html/rfc1034) (section 3.5)", "location": "query", "type": "string"}, "parent": {"description": "Required. The resource name of the location to create the new network peering in. This value is always `global`, because `NetworkPeering` is a global resource. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/global`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server guarantees that a request doesn't result in creation of duplicate commitments for at least 60 minutes. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (********-0000-0000-0000-********0000).", "location": "query", "type": "string"}}, "path": "v1/{+parent}/networkPeerings", "request": {"$ref": "NetworkPeering"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a `NetworkPeering` resource. When a network peering is deleted for a VMware Engine network, the peer network becomes inaccessible to that VMware Engine network. NetworkPeering is a global resource and location can only be global.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/networkPeerings/{networkPeeringsId}", "httpMethod": "DELETE", "id": "vmwareengine.projects.locations.networkPeerings.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the network peering to be deleted. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/global/networkPeerings/my-peering`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/networkPeerings/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server guarantees that a request doesn't result in creation of duplicate commitments for at least 60 minutes. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (********-0000-0000-0000-********0000).", "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Retrieves a `NetworkPeering` resource by its resource name. The resource contains details of the network peering, such as peered networks, import and export custom route configurations, and peering state. NetworkPeering is a global resource and location can only be global.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/networkPeerings/{networkPeeringsId}", "httpMethod": "GET", "id": "vmwareengine.projects.locations.networkPeerings.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the network peering to retrieve. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/global/networkPeerings/my-peering`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/networkPeerings/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "NetworkPeering"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists `NetworkPeering` resources in a given project. NetworkPeering is a global resource and location can only be global.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/networkPeerings", "httpMethod": "GET", "id": "vmwareengine.projects.locations.networkPeerings.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "A filter expression that matches resources returned in the response. The expression must specify the field name, a comparison operator, and the value that you want to use for filtering. The value must be a string, a number, or a boolean. The comparison operator must be `=`, `!=`, `>`, or `<`. For example, if you are filtering a list of network peerings, you can exclude the ones named `example-peering` by specifying `name != \"example-peering\"`. To filter on multiple expressions, provide each separate expression within parentheses. For example: ``` (name = \"example-peering\") (createTime > \"2021-04-12T08:15:10.40Z\") ``` By default, each expression is an `AND` expression. However, you can include `AND` and `OR` expressions explicitly. For example: ``` (name = \"example-peering-1\") AND (createTime > \"2021-04-12T08:15:10.40Z\") OR (name = \"example-peering-2\") ```", "location": "query", "type": "string"}, "orderBy": {"description": "Sorts list results by a certain order. By default, returned results are ordered by `name` in ascending order. You can also sort results in descending order based on the `name` value using `orderBy=\"name desc\"`. Currently, only ordering by `name` is supported.", "location": "query", "type": "string"}, "pageSize": {"description": "The maximum number of network peerings to return in one page. The maximum value is coerced to 1000. The default value of this field is 500.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `ListNetworkPeerings` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListNetworkPeerings` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The resource name of the location (global) to query for network peerings. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/global`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/networkPeerings", "response": {"$ref": "ListNetworkPeeringsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Modifies a `NetworkPeering` resource. Only the `description` field can be updated. Only fields specified in `updateMask` are applied. NetworkPeering is a global resource and location can only be global.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/networkPeerings/{networkPeeringsId}", "httpMethod": "PATCH", "id": "vmwareengine.projects.locations.networkPeerings.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only. Identifier. The resource name of the network peering. NetworkPeering is a global resource and location can only be global. Resource names are scheme-less URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/global/networkPeerings/my-peering`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/networkPeerings/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server guarantees that a request doesn't result in creation of duplicate commitments for at least 60 minutes. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (********-0000-0000-0000-********0000).", "location": "query", "type": "string"}, "updateMask": {"description": "Required. Field mask is used to specify the fields to be overwritten in the `NetworkPeering` resource by the update. The fields specified in the `update_mask` are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then all fields will be overwritten.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "NetworkPeering"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"peeringRoutes": {"methods": {"list": {"description": "Lists the network peering routes exchanged over a peering connection. NetworkPeering is a global resource and location can only be global.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/networkPeerings/{networkPeeringsId}/peeringRoutes", "httpMethod": "GET", "id": "vmwareengine.projects.locations.networkPeerings.peeringRoutes.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "A filter expression that matches resources returned in the response. Currently, only filtering on the `direction` field is supported. To return routes imported from the peer network, provide \"direction=INCOMING\". To return routes exported from the VMware Engine network, provide \"direction=OUTGOING\". Other filter expressions return an error.", "location": "query", "type": "string"}, "pageSize": {"description": "The maximum number of peering routes to return in one page. The service may return fewer than this value. The maximum value is coerced to 1000. The default value of this field is 500.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `ListPeeringRoutes` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListPeeringRoutes` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The resource name of the network peering to retrieve peering routes from. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/global/networkPeerings/my-peering`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/networkPeerings/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/peeringRoutes", "response": {"$ref": "ListPeeringRoutesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}, "networkPolicies": {"methods": {"create": {"description": "Creates a new network policy in a given VMware Engine network of a project and location (region). A new network policy cannot be created if another network policy already exists in the same scope.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/networkPolicies", "httpMethod": "POST", "id": "vmwareengine.projects.locations.networkPolicies.create", "parameterOrder": ["parent"], "parameters": {"networkPolicyId": {"description": "Required. The user-provided identifier of the network policy to be created. This identifier must be unique within parent `projects/{my-project}/locations/{us-central1}/networkPolicies` and becomes the final token in the name URI. The identifier must meet the following requirements: * Only contains 1-63 alphanumeric characters and hyphens * Begins with an alphabetical character * Ends with a non-hyphen character * Not formatted as a UUID * Complies with [RFC 1034](https://datatracker.ietf.org/doc/html/rfc1034) (section 3.5)", "location": "query", "type": "string"}, "parent": {"description": "Required. The resource name of the location (region) to create the new network policy in. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/us-central1`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server guarantees that a request doesn't result in creation of duplicate commitments for at least 60 minutes. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (********-0000-0000-0000-********0000).", "location": "query", "type": "string"}}, "path": "v1/{+parent}/networkPolicies", "request": {"$ref": "NetworkPolicy"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a `NetworkPolicy` resource. A network policy cannot be deleted when `NetworkService.state` is set to `RECONCILING` for either its external IP or internet access service.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/networkPolicies/{networkPoliciesId}", "httpMethod": "DELETE", "id": "vmwareengine.projects.locations.networkPolicies.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the network policy to delete. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/us-central1/networkPolicies/my-network-policy`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/networkPolicies/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server guarantees that a request doesn't result in creation of duplicate commitments for at least 60 minutes. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (********-0000-0000-0000-********0000).", "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "fetchExternalAddresses": {"description": "Lists external IP addresses assigned to VMware workload VMs within the scope of the given network policy.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/networkPolicies/{networkPoliciesId}:fetchExternalAddresses", "httpMethod": "GET", "id": "vmwareengine.projects.locations.networkPolicies.fetchExternalAddresses", "parameterOrder": ["networkPolicy"], "parameters": {"networkPolicy": {"description": "Required. The resource name of the network policy to query for assigned external IP addresses. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/us-central1/networkPolicies/my-policy`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/networkPolicies/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "The maximum number of external IP addresses to return in one page. The service may return fewer than this value. The maximum value is coerced to 1000. The default value of this field is 500.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `FetchNetworkPolicyExternalAddresses` call. Provide this to retrieve the subsequent page. When paginating, all parameters provided to `FetchNetworkPolicyExternalAddresses`, except for `page_size` and `page_token`, must match the call that provided the page token.", "location": "query", "type": "string"}}, "path": "v1/{+networkPolicy}:fetchExternalAddresses", "response": {"$ref": "FetchNetworkPolicyExternalAddressesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Retrieves a `NetworkPolicy` resource by its resource name.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/networkPolicies/{networkPoliciesId}", "httpMethod": "GET", "id": "vmwareengine.projects.locations.networkPolicies.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the network policy to retrieve. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/us-central1/networkPolicies/my-network-policy`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/networkPolicies/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "NetworkPolicy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists `NetworkPolicy` resources in a specified project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/networkPolicies", "httpMethod": "GET", "id": "vmwareengine.projects.locations.networkPolicies.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "A filter expression that matches resources returned in the response. The expression must specify the field name, a comparison operator, and the value that you want to use for filtering. The value must be a string, a number, or a boolean. The comparison operator must be `=`, `!=`, `>`, or `<`. For example, if you are filtering a list of network policies, you can exclude the ones named `example-policy` by specifying `name != \"example-policy\"`. To filter on multiple expressions, provide each separate expression within parentheses. For example: ``` (name = \"example-policy\") (createTime > \"2021-04-12T08:15:10.40Z\") ``` By default, each expression is an `AND` expression. However, you can include `AND` and `OR` expressions explicitly. For example: ``` (name = \"example-policy-1\") AND (createTime > \"2021-04-12T08:15:10.40Z\") OR (name = \"example-policy-2\") ```", "location": "query", "type": "string"}, "orderBy": {"description": "Sorts list results by a certain order. By default, returned results are ordered by `name` in ascending order. You can also sort results in descending order based on the `name` value using `orderBy=\"name desc\"`. Currently, only ordering by `name` is supported.", "location": "query", "type": "string"}, "pageSize": {"description": "The maximum number of network policies to return in one page. The service may return fewer than this value. The maximum value is coerced to 1000. The default value of this field is 500.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `ListNetworkPolicies` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListNetworkPolicies` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The resource name of the location (region) to query for network policies. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/us-central1`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/networkPolicies", "response": {"$ref": "ListNetworkPoliciesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Modifies a `NetworkPolicy` resource. Only the following fields can be updated: `internet_access`, `external_ip`, `edge_services_cidr`. Only fields specified in `updateMask` are applied. When updating a network policy, the external IP network service can only be disabled if there are no external IP addresses present in the scope of the policy. Also, a `NetworkService` cannot be updated when `NetworkService.state` is set to `RECONCILING`. During operation processing, the resource is temporarily in the `ACTIVE` state before the operation fully completes. For that period of time, you can't update the resource. Use the operation status to determine when the processing fully completes.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/networkPolicies/{networkPoliciesId}", "httpMethod": "PATCH", "id": "vmwareengine.projects.locations.networkPolicies.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only. Identifier. The resource name of this network policy. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/us-central1/networkPolicies/my-network-policy`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/networkPolicies/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server guarantees that a request doesn't result in creation of duplicate commitments for at least 60 minutes. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (********-0000-0000-0000-********0000).", "location": "query", "type": "string"}, "updateMask": {"description": "Required. Field mask is used to specify the fields to be overwritten in the `NetworkPolicy` resource by the update. The fields specified in the `update_mask` are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then all fields will be overwritten.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "NetworkPolicy"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"externalAccessRules": {"methods": {"create": {"description": "Creates a new external access rule in a given network policy.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/networkPolicies/{networkPoliciesId}/externalAccessRules", "httpMethod": "POST", "id": "vmwareengine.projects.locations.networkPolicies.externalAccessRules.create", "parameterOrder": ["parent"], "parameters": {"externalAccessRuleId": {"description": "Required. The user-provided identifier of the `ExternalAccessRule` to be created. This identifier must be unique among `ExternalAccessRule` resources within the parent and becomes the final token in the name URI. The identifier must meet the following requirements: * Only contains 1-63 alphanumeric characters and hyphens * Begins with an alphabetical character * Ends with a non-hyphen character * Not formatted as a UUID * Complies with [RFC 1034](https://datatracker.ietf.org/doc/html/rfc1034) (section 3.5)", "location": "query", "type": "string"}, "parent": {"description": "Required. The resource name of the network policy to create a new external access firewall rule in. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/us-central1/networkPolicies/my-policy`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/networkPolicies/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "A request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server guarantees that a request doesn't result in creation of duplicate commitments for at least 60 minutes. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if the original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (********-0000-0000-0000-********0000).", "location": "query", "type": "string"}}, "path": "v1/{+parent}/externalAccessRules", "request": {"$ref": "ExternalAccessRule"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a single external access rule.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/networkPolicies/{networkPoliciesId}/externalAccessRules/{externalAccessRulesId}", "httpMethod": "DELETE", "id": "vmwareengine.projects.locations.networkPolicies.externalAccessRules.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the external access firewall rule to delete. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/us-central1/networkPolicies/my-policy/externalAccessRules/my-rule`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/networkPolicies/[^/]+/externalAccessRules/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server guarantees that a request doesn't result in creation of duplicate commitments for at least 60 minutes. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if the original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (********-0000-0000-0000-********0000).", "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of a single external access rule.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/networkPolicies/{networkPoliciesId}/externalAccessRules/{externalAccessRulesId}", "httpMethod": "GET", "id": "vmwareengine.projects.locations.networkPolicies.externalAccessRules.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the external access firewall rule to retrieve. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/us-central1/networkPolicies/my-policy/externalAccessRules/my-rule`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/networkPolicies/[^/]+/externalAccessRules/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "ExternalAccessRule"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists `ExternalAccessRule` resources in the specified network policy.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/networkPolicies/{networkPoliciesId}/externalAccessRules", "httpMethod": "GET", "id": "vmwareengine.projects.locations.networkPolicies.externalAccessRules.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "A filter expression that matches resources returned in the response. The expression must specify the field name, a comparison operator, and the value that you want to use for filtering. The value must be a string, a number, or a boolean. The comparison operator must be `=`, `!=`, `>`, or `<`. For example, if you are filtering a list of external access rules, you can exclude the ones named `example-rule` by specifying `name != \"example-rule\"`. To filter on multiple expressions, provide each separate expression within parentheses. For example: ``` (name = \"example-rule\") (createTime > \"2021-04-12T08:15:10.40Z\") ``` By default, each expression is an `AND` expression. However, you can include `AND` and `OR` expressions explicitly. For example: ``` (name = \"example-rule-1\") AND (createTime > \"2021-04-12T08:15:10.40Z\") OR (name = \"example-rule-2\") ```", "location": "query", "type": "string"}, "orderBy": {"description": "Sorts list results by a certain order. By default, returned results are ordered by `name` in ascending order. You can also sort results in descending order based on the `name` value using `orderBy=\"name desc\"`. Currently, only ordering by `name` is supported.", "location": "query", "type": "string"}, "pageSize": {"description": "The maximum number of external access rules to return in one page. The service may return fewer than this value. The maximum value is coerced to 1000. The default value of this field is 500.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `ListExternalAccessRulesRequest` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListExternalAccessRulesRequest` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The resource name of the network policy to query for external access firewall rules. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/us-central1/networkPolicies/my-policy`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/networkPolicies/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/externalAccessRules", "response": {"$ref": "ListExternalAccessRulesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates the parameters of a single external access rule. Only fields specified in `update_mask` are applied.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/networkPolicies/{networkPoliciesId}/externalAccessRules/{externalAccessRulesId}", "httpMethod": "PATCH", "id": "vmwareengine.projects.locations.networkPolicies.externalAccessRules.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only. The resource name of this external access rule. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/us-central1/networkPolicies/my-policy/externalAccessRules/my-rule`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/networkPolicies/[^/]+/externalAccessRules/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server guarantees that a request doesn't result in creation of duplicate commitments for at least 60 minutes. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if the original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (********-0000-0000-0000-********0000).", "location": "query", "type": "string"}, "updateMask": {"description": "Required. Field mask is used to specify the fields to be overwritten in the `ExternalAccessRule` resource by the update. The fields specified in the `update_mask` are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then all fields will be overwritten.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "ExternalAccessRule"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}, "nodeTypes": {"methods": {"get": {"description": "Gets details of a single `NodeType`.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/nodeTypes/{nodeTypesId}", "httpMethod": "GET", "id": "vmwareengine.projects.locations.nodeTypes.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the node type to retrieve. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-proj/locations/us-central1-a/nodeTypes/standard-72`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/nodeTypes/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "NodeType"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists node types", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/nodeTypes", "httpMethod": "GET", "id": "vmwareengine.projects.locations.nodeTypes.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "A filter expression that matches resources returned in the response. The expression must specify the field name, a comparison operator, and the value that you want to use for filtering. The value must be a string, a number, or a boolean. The comparison operator must be `=`, `!=`, `>`, or `<`. For example, if you are filtering a list of node types, you can exclude the ones named `standard-72` by specifying `name != \"standard-72\"`. To filter on multiple expressions, provide each separate expression within parentheses. For example: ``` (name = \"standard-72\") (virtual_cpu_count > 2) ``` By default, each expression is an `AND` expression. However, you can include `AND` and `OR` expressions explicitly. For example: ``` (name = \"standard-96\") AND (virtual_cpu_count > 2) OR (name = \"standard-72\") ```", "location": "query", "type": "string"}, "pageSize": {"description": "The maximum number of node types to return in one page. The service may return fewer than this value. The maximum value is coerced to 1000. The default value of this field is 500.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `ListNodeTypes` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListNodeTypes` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The resource name of the location to be queried for node types. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/us-central1-a`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/nodeTypes", "response": {"$ref": "ListNodeTypesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "operations": {"methods": {"delete": {"description": "Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}", "httpMethod": "DELETE", "id": "vmwareengine.projects.locations.operations.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource to be deleted.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}", "httpMethod": "GET", "id": "vmwareengine.projects.locations.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/operations", "httpMethod": "GET", "id": "vmwareengine.projects.locations.operations.list", "parameterOrder": ["name"], "parameters": {"filter": {"description": "The standard list filter.", "location": "query", "type": "string"}, "name": {"description": "The name of the operation's parent resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "The standard list page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The standard list page token.", "location": "query", "type": "string"}}, "path": "v1/{+name}/operations", "response": {"$ref": "ListOperationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "privateClouds": {"methods": {"create": {"description": "Creates a new `PrivateCloud` resource in a given project and location. Private clouds of type `STANDARD` and `TIME_LIMITED` are zonal resources, `STRETCHED` private clouds are regional. Creating a private cloud also creates a [management cluster](https://cloud.google.com/vmware-engine/docs/concepts-vmware-components) for that private cloud.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/privateClouds", "httpMethod": "POST", "id": "vmwareengine.projects.locations.privateClouds.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The resource name of the location to create the new private cloud in. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/us-central1-a`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "privateCloudId": {"description": "Required. The user-provided identifier of the private cloud to be created. This identifier must be unique among each `PrivateCloud` within the parent and becomes the final token in the name URI. The identifier must meet the following requirements: * Only contains 1-63 alphanumeric characters and hyphens * Begins with an alphabetical character * Ends with a non-hyphen character * Not formatted as a UUID * Complies with [RFC 1034](https://datatracker.ietf.org/doc/html/rfc1034) (section 3.5)", "location": "query", "type": "string"}, "requestId": {"description": "Optional. The request ID must be a valid UUID with the exception that zero UUID is not supported (********-0000-0000-0000-********0000).", "location": "query", "type": "string"}, "validateOnly": {"description": "Optional. True if you want the request to be validated and not executed; false otherwise.", "location": "query", "type": "boolean"}}, "path": "v1/{+parent}/privateClouds", "request": {"$ref": "PrivateCloud"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Schedules a `PrivateCloud` resource for deletion. A `PrivateCloud` resource scheduled for deletion has `PrivateCloud.state` set to `DELETED` and `expireTime` set to the time when deletion is final and can no longer be reversed. The delete operation is marked as done as soon as the `PrivateCloud` is successfully scheduled for deletion (this also applies when `delayHours` is set to zero), and the operation is not kept in pending state until `PrivateCloud` is purged. `PrivateCloud` can be restored using `UndeletePrivateCloud` method before the `expireTime` elapses. When `expireTime` is reached, deletion is final and all private cloud resources are irreversibly removed and billing stops. During the final removal process, `PrivateCloud.state` is set to `PURGING`. `PrivateCloud` can be polled using standard `GET` method for the whole period of deletion and purging. It will not be returned only when it is completely purged.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}", "httpMethod": "DELETE", "id": "vmwareengine.projects.locations.privateClouds.delete", "parameterOrder": ["name"], "parameters": {"delayHours": {"description": "Optional. Time delay of the deletion specified in hours. The default value is `3`. Specifying a non-zero value for this field changes the value of `PrivateCloud.state` to `DELETED` and sets `expire_time` to the planned deletion time. Deletion can be cancelled before `expire_time` elapses using VmwareEngine.UndeletePrivateCloud. Specifying a value of `0` for this field instead begins the deletion process and ceases billing immediately. During the final deletion process, the value of `PrivateCloud.state` becomes `PURGING`.", "format": "int32", "location": "query", "type": "integer"}, "force": {"description": "Optional. If set to true, cascade delete is enabled and all children of this private cloud resource are also deleted. When this flag is set to false, the private cloud will not be deleted if there are any children other than the management cluster. The management cluster is always deleted.", "location": "query", "type": "boolean"}, "name": {"description": "Required. The resource name of the private cloud to delete. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/us-central1-a/privateClouds/my-cloud`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/privateClouds/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. The request ID must be a valid UUID with the exception that zero UUID is not supported (********-0000-0000-0000-********0000).", "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Retrieves a `PrivateCloud` resource by its resource name.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}", "httpMethod": "GET", "id": "vmwareengine.projects.locations.privateClouds.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the private cloud to retrieve. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/us-central1-a/privateClouds/my-cloud`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/privateClouds/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "PrivateCloud"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getDnsForwarding": {"description": "Gets details of the `DnsForwarding` config.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}/dnsForwarding", "httpMethod": "GET", "id": "vmwareengine.projects.locations.privateClouds.getDnsForwarding", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of a `DnsForwarding` to retrieve. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/us-central1-a/privateClouds/my-cloud/dnsForwarding`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/privateClouds/[^/]+/dnsForwarding$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "DnsForwarding"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getIamPolicy": {"description": "Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}:getIamPolicy", "httpMethod": "GET", "id": "vmwareengine.projects.locations.privateClouds.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"options.requestedPolicyVersion": {"description": "Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "location": "query", "type": "integer"}, "resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/privateClouds/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:getIamPolicy", "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists `PrivateCloud` resources in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/privateClouds", "httpMethod": "GET", "id": "vmwareengine.projects.locations.privateClouds.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "A filter expression that matches resources returned in the response. The expression must specify the field name, a comparison operator, and the value that you want to use for filtering. The value must be a string, a number, or a boolean. The comparison operator must be `=`, `!=`, `>`, or `<`. For example, if you are filtering a list of private clouds, you can exclude the ones named `example-pc` by specifying `name != \"example-pc\"`. You can also filter nested fields. For example, you could specify `networkConfig.managementCidr = \"***********/24\"` to include private clouds only if they have a matching address in their network configuration. To filter on multiple expressions, provide each separate expression within parentheses. For example: ``` (name = \"example-pc\") (createTime > \"2021-04-12T08:15:10.40Z\") ``` By default, each expression is an `AND` expression. However, you can include `AND` and `OR` expressions explicitly. For example: ``` (name = \"private-cloud-1\") AND (createTime > \"2021-04-12T08:15:10.40Z\") OR (name = \"private-cloud-2\") ```", "location": "query", "type": "string"}, "orderBy": {"description": "Sorts list results by a certain order. By default, returned results are ordered by `name` in ascending order. You can also sort results in descending order based on the `name` value using `orderBy=\"name desc\"`. Currently, only ordering by `name` is supported.", "location": "query", "type": "string"}, "pageSize": {"description": "The maximum number of private clouds to return in one page. The service may return fewer than this value. The maximum value is coerced to 1000. The default value of this field is 500.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `ListPrivateClouds` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListPrivateClouds` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The resource name of the private cloud to be queried for clusters. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/us-central1-a`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/privateClouds", "response": {"$ref": "ListPrivateCloudsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Modifies a `PrivateCloud` resource. Only the following fields can be updated: `description`. Only fields specified in `updateMask` are applied. During operation processing, the resource is temporarily in the `ACTIVE` state before the operation fully completes. For that period of time, you can't update the resource. Use the operation status to determine when the processing fully completes.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}", "httpMethod": "PATCH", "id": "vmwareengine.projects.locations.privateClouds.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only. Identifier. The resource name of this private cloud. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/us-central1-a/privateClouds/my-cloud`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/privateClouds/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. The request ID must be a valid UUID with the exception that zero UUID is not supported (********-0000-0000-0000-********0000).", "location": "query", "type": "string"}, "updateMask": {"description": "Required. Field mask is used to specify the fields to be overwritten in the `PrivateCloud` resource by the update. The fields specified in `updateMask` are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then all fields will be overwritten.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "PrivateCloud"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "resetNsxCredentials": {"description": "Resets credentials of the NSX appliance.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}:resetNsxCredentials", "httpMethod": "POST", "id": "vmwareengine.projects.locations.privateClouds.resetNsxCredentials", "parameterOrder": ["privateCloud"], "parameters": {"privateCloud": {"description": "Required. The resource name of the private cloud to reset credentials for. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/us-central1-a/privateClouds/my-cloud`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/privateClouds/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+privateCloud}:resetNsxCredentials", "request": {"$ref": "ResetNsxCredentialsRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "resetVcenterCredentials": {"description": "Resets credentials of the Vcenter appliance.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}:resetVcenterCredentials", "httpMethod": "POST", "id": "vmwareengine.projects.locations.privateClouds.resetVcenterCredentials", "parameterOrder": ["privateCloud"], "parameters": {"privateCloud": {"description": "Required. The resource name of the private cloud to reset credentials for. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/us-central1-a/privateClouds/my-cloud`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/privateClouds/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+privateCloud}:resetVcenterCredentials", "request": {"$ref": "ResetVcenterCredentialsRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}:setIamPolicy", "httpMethod": "POST", "id": "vmwareengine.projects.locations.privateClouds.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/privateClouds/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:setIamPolicy", "request": {"$ref": "SetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "showNsxCredentials": {"description": "Gets details of credentials for NSX appliance.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}:showNsxCredentials", "httpMethod": "GET", "id": "vmwareengine.projects.locations.privateClouds.showNsxCredentials", "parameterOrder": ["privateCloud"], "parameters": {"privateCloud": {"description": "Required. The resource name of the private cloud to be queried for credentials. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/us-central1-a/privateClouds/my-cloud`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/privateClouds/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+privateCloud}:showNsxCredentials", "response": {"$ref": "Credentials"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "showVcenterCredentials": {"description": "Gets details of credentials for Vcenter appliance.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}:showVcenterCredentials", "httpMethod": "GET", "id": "vmwareengine.projects.locations.privateClouds.showVcenterCredentials", "parameterOrder": ["privateCloud"], "parameters": {"privateCloud": {"description": "Required. The resource name of the private cloud to be queried for credentials. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/us-central1-a/privateClouds/my-cloud`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/privateClouds/[^/]+$", "required": true, "type": "string"}, "username": {"description": "Optional. The username of the user to be queried for credentials. The default value of this <NAME_EMAIL>. The provided value must be one of the following: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>.", "location": "query", "type": "string"}}, "path": "v1/{+privateCloud}:showVcenterCredentials", "response": {"$ref": "Credentials"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"description": "Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may \"fail open\" without warning.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}:testIamPermissions", "httpMethod": "POST", "id": "vmwareengine.projects.locations.privateClouds.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/privateClouds/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:testIamPermissions", "request": {"$ref": "TestIamPermissionsRequest"}, "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "undelete": {"description": "Restores a private cloud that was previously scheduled for deletion by `DeletePrivateCloud`. A `PrivateCloud` resource scheduled for deletion has `PrivateCloud.state` set to `DELETED` and `PrivateCloud.expireTime` set to the time when deletion can no longer be reversed.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}:undelete", "httpMethod": "POST", "id": "vmwareengine.projects.locations.privateClouds.undelete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the private cloud scheduled for deletion. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/us-central1-a/privateClouds/my-cloud`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/privateClouds/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:undelete", "request": {"$ref": "UndeletePrivateCloudRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "updateDnsForwarding": {"description": "Updates the parameters of the `DnsForwarding` config, like associated domains. Only fields specified in `update_mask` are applied.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}/dnsForwarding", "httpMethod": "PATCH", "id": "vmwareengine.projects.locations.privateClouds.updateDnsForwarding", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only. Identifier. The resource name of this DNS profile. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/us-central1-a/privateClouds/my-cloud/dnsForwarding`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/privateClouds/[^/]+/dnsForwarding$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server guarantees that a request doesn't result in creation of duplicate commitments for at least 60 minutes. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (********-0000-0000-0000-********0000).", "location": "query", "type": "string"}, "updateMask": {"description": "Required. Field mask is used to specify the fields to be overwritten in the `DnsForwarding` resource by the update. The fields specified in the `update_mask` are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then all fields will be overwritten.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "DnsForwarding"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"clusters": {"methods": {"create": {"description": "Creates a new cluster in a given private cloud. Creating a new cluster provides additional nodes for use in the parent private cloud and requires sufficient [node quota](https://cloud.google.com/vmware-engine/quotas).", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}/clusters", "httpMethod": "POST", "id": "vmwareengine.projects.locations.privateClouds.clusters.create", "parameterOrder": ["parent"], "parameters": {"clusterId": {"description": "Required. The user-provided identifier of the new `Cluster`. This identifier must be unique among clusters within the parent and becomes the final token in the name URI. The identifier must meet the following requirements: * Only contains 1-63 alphanumeric characters and hyphens * Begins with an alphabetical character * Ends with a non-hyphen character * Not formatted as a UUID * Complies with [RFC 1034](https://datatracker.ietf.org/doc/html/rfc1034) (section 3.5)", "location": "query", "type": "string"}, "parent": {"description": "Required. The resource name of the private cloud to create a new cluster in. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/us-central1-a/privateClouds/my-cloud`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/privateClouds/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. The request ID must be a valid UUID with the exception that zero UUID is not supported (********-0000-0000-0000-********0000).", "location": "query", "type": "string"}, "validateOnly": {"description": "Optional. True if you want the request to be validated and not executed; false otherwise.", "location": "query", "type": "boolean"}}, "path": "v1/{+parent}/clusters", "request": {"$ref": "Cluster"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a `Cluster` resource. To avoid unintended data loss, migrate or gracefully shut down any workloads running on the cluster before deletion. You cannot delete the management cluster of a private cloud using this method.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}/clusters/{clustersId}", "httpMethod": "DELETE", "id": "vmwareengine.projects.locations.privateClouds.clusters.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the cluster to delete. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/us-central1-a/privateClouds/my-cloud/clusters/my-cluster`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/privateClouds/[^/]+/clusters/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. The request ID must be a valid UUID with the exception that zero UUID is not supported (********-0000-0000-0000-********0000).", "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Retrieves a `Cluster` resource by its resource name.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}/clusters/{clustersId}", "httpMethod": "GET", "id": "vmwareengine.projects.locations.privateClouds.clusters.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The cluster resource name to retrieve. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/us-central1-a/privateClouds/my-cloud/clusters/my-cluster`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/privateClouds/[^/]+/clusters/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Cluster"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getIamPolicy": {"description": "Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}/clusters/{clustersId}:getIamPolicy", "httpMethod": "GET", "id": "vmwareengine.projects.locations.privateClouds.clusters.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"options.requestedPolicyVersion": {"description": "Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "location": "query", "type": "integer"}, "resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/privateClouds/[^/]+/clusters/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:getIamPolicy", "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists `Cluster` resources in a given private cloud.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}/clusters", "httpMethod": "GET", "id": "vmwareengine.projects.locations.privateClouds.clusters.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": " To filter on multiple expressions, provide each separate expression within parentheses. For example: ``` (name = \"example-cluster\") (nodeCount = \"3\") ``` By default, each expression is an `AND` expression. However, you can include `AND` and `OR` expressions explicitly. For example: ``` (name = \"example-cluster-1\") AND (createTime > \"2021-04-12T08:15:10.40Z\") OR (name = \"example-cluster-2\") ```", "location": "query", "type": "string"}, "orderBy": {"description": "Sorts list results by a certain order. By default, returned results are ordered by `name` in ascending order. You can also sort results in descending order based on the `name` value using `orderBy=\"name desc\"`. Currently, only ordering by `name` is supported.", "location": "query", "type": "string"}, "pageSize": {"description": "The maximum number of clusters to return in one page. The service may return fewer than this value. The maximum value is coerced to 1000. The default value of this field is 500.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `ListClusters` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListClusters` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The resource name of the private cloud to query for clusters. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/us-central1-a/privateClouds/my-cloud`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/privateClouds/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/clusters", "response": {"$ref": "ListClustersResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Modifies a `Cluster` resource. Only fields specified in `updateMask` are applied. During operation processing, the resource is temporarily in the `ACTIVE` state before the operation fully completes. For that period of time, you can't update the resource. Use the operation status to determine when the processing fully completes.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}/clusters/{clustersId}", "httpMethod": "PATCH", "id": "vmwareengine.projects.locations.privateClouds.clusters.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only. Identifier. The resource name of this cluster. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/us-central1-a/privateClouds/my-cloud/clusters/my-cluster`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/privateClouds/[^/]+/clusters/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. The request ID must be a valid UUID with the exception that zero UUID is not supported (********-0000-0000-0000-********0000).", "location": "query", "type": "string"}, "updateMask": {"description": "Required. Field mask is used to specify the fields to be overwritten in the `Cluster` resource by the update. The fields specified in the `updateMask` are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then all fields will be overwritten.", "format": "google-fieldmask", "location": "query", "type": "string"}, "validateOnly": {"description": "Optional. True if you want the request to be validated and not executed; false otherwise.", "location": "query", "type": "boolean"}}, "path": "v1/{+name}", "request": {"$ref": "Cluster"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}/clusters/{clustersId}:setIamPolicy", "httpMethod": "POST", "id": "vmwareengine.projects.locations.privateClouds.clusters.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/privateClouds/[^/]+/clusters/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:setIamPolicy", "request": {"$ref": "SetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"description": "Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may \"fail open\" without warning.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}/clusters/{clustersId}:testIamPermissions", "httpMethod": "POST", "id": "vmwareengine.projects.locations.privateClouds.clusters.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/privateClouds/[^/]+/clusters/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:testIamPermissions", "request": {"$ref": "TestIamPermissionsRequest"}, "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"nodes": {"methods": {"get": {"description": "Gets details of a single node.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}/clusters/{clustersId}/nodes/{nodesId}", "httpMethod": "GET", "id": "vmwareengine.projects.locations.privateClouds.clusters.nodes.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the node to retrieve. For example: `projects/{project}/locations/{location}/privateClouds/{private_cloud}/clusters/{cluster}/nodes/{node}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/privateClouds/[^/]+/clusters/[^/]+/nodes/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Node"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists nodes in a given cluster.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}/clusters/{clustersId}/nodes", "httpMethod": "GET", "id": "vmwareengine.projects.locations.privateClouds.clusters.nodes.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The maximum number of nodes to return in one page. The service may return fewer than this value. The maximum value is coerced to 1000. The default value of this field is 500.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `ListNodes` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListNodes` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The resource name of the cluster to be queried for nodes. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/us-central1-a/privateClouds/my-cloud/clusters/my-cluster`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/privateClouds/[^/]+/clusters/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/nodes", "response": {"$ref": "ListNodesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}, "externalAddresses": {"methods": {"create": {"description": "Creates a new `ExternalAddress` resource in a given private cloud. The network policy that corresponds to the private cloud must have the external IP address network service enabled (`NetworkPolicy.external_ip`).", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}/externalAddresses", "httpMethod": "POST", "id": "vmwareengine.projects.locations.privateClouds.externalAddresses.create", "parameterOrder": ["parent"], "parameters": {"externalAddressId": {"description": "Required. The user-provided identifier of the `ExternalAddress` to be created. This identifier must be unique among `ExternalAddress` resources within the parent and becomes the final token in the name URI. The identifier must meet the following requirements: * Only contains 1-63 alphanumeric characters and hyphens * Begins with an alphabetical character * Ends with a non-hyphen character * Not formatted as a UUID * Complies with [RFC 1034](https://datatracker.ietf.org/doc/html/rfc1034) (section 3.5)", "location": "query", "type": "string"}, "parent": {"description": "Required. The resource name of the private cloud to create a new external IP address in. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/us-central1-a/privateClouds/my-cloud`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/privateClouds/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server guarantees that a request doesn't result in creation of duplicate commitments for at least 60 minutes. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if the original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (********-0000-0000-0000-********0000).", "location": "query", "type": "string"}}, "path": "v1/{+parent}/externalAddresses", "request": {"$ref": "ExternalAddress"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a single external IP address. When you delete an external IP address, connectivity between the external IP address and the corresponding internal IP address is lost.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}/externalAddresses/{externalAddressesId}", "httpMethod": "DELETE", "id": "vmwareengine.projects.locations.privateClouds.externalAddresses.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the external IP address to delete. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/us-central1-a/privateClouds/my-cloud/externalAddresses/my-ip`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/privateClouds/[^/]+/externalAddresses/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server guarantees that a request doesn't result in creation of duplicate commitments for at least 60 minutes. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if the original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (********-0000-0000-0000-********0000).", "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of a single external IP address.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}/externalAddresses/{externalAddressesId}", "httpMethod": "GET", "id": "vmwareengine.projects.locations.privateClouds.externalAddresses.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the external IP address to retrieve. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/us-central1-a/privateClouds/my-cloud/externalAddresses/my-ip`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/privateClouds/[^/]+/externalAddresses/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "ExternalAddress"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists external IP addresses assigned to VMware workload VMs in a given private cloud.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}/externalAddresses", "httpMethod": "GET", "id": "vmwareengine.projects.locations.privateClouds.externalAddresses.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "A filter expression that matches resources returned in the response. The expression must specify the field name, a comparison operator, and the value that you want to use for filtering. The value must be a string, a number, or a boolean. The comparison operator must be `=`, `!=`, `>`, or `<`. For example, if you are filtering a list of IP addresses, you can exclude the ones named `example-ip` by specifying `name != \"example-ip\"`. To filter on multiple expressions, provide each separate expression within parentheses. For example: ``` (name = \"example-ip\") (createTime > \"2021-04-12T08:15:10.40Z\") ``` By default, each expression is an `AND` expression. However, you can include `AND` and `OR` expressions explicitly. For example: ``` (name = \"example-ip-1\") AND (createTime > \"2021-04-12T08:15:10.40Z\") OR (name = \"example-ip-2\") ```", "location": "query", "type": "string"}, "orderBy": {"description": "Sorts list results by a certain order. By default, returned results are ordered by `name` in ascending order. You can also sort results in descending order based on the `name` value using `orderBy=\"name desc\"`. Currently, only ordering by `name` is supported.", "location": "query", "type": "string"}, "pageSize": {"description": "The maximum number of external IP addresses to return in one page. The service may return fewer than this value. The maximum value is coerced to 1000. The default value of this field is 500.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `ListExternalAddresses` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListExternalAddresses` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The resource name of the private cloud to be queried for external IP addresses. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/us-central1-a/privateClouds/my-cloud`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/privateClouds/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/externalAddresses", "response": {"$ref": "ListExternalAddressesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates the parameters of a single external IP address. Only fields specified in `update_mask` are applied. During operation processing, the resource is temporarily in the `ACTIVE` state before the operation fully completes. For that period of time, you can't update the resource. Use the operation status to determine when the processing fully completes.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}/externalAddresses/{externalAddressesId}", "httpMethod": "PATCH", "id": "vmwareengine.projects.locations.privateClouds.externalAddresses.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only. Identifier. The resource name of this external IP address. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/us-central1-a/privateClouds/my-cloud/externalAddresses/my-address`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/privateClouds/[^/]+/externalAddresses/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server guarantees that a request doesn't result in creation of duplicate commitments for at least 60 minutes. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if the original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (********-0000-0000-0000-********0000).", "location": "query", "type": "string"}, "updateMask": {"description": "Required. Field mask is used to specify the fields to be overwritten in the `ExternalAddress` resource by the update. The fields specified in the `update_mask` are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then all fields will be overwritten.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "ExternalAddress"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "hcxActivationKeys": {"methods": {"create": {"description": "Creates a new HCX activation key in a given private cloud.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}/hcxActivationKeys", "httpMethod": "POST", "id": "vmwareengine.projects.locations.privateClouds.hcxActivationKeys.create", "parameterOrder": ["parent"], "parameters": {"hcxActivationKeyId": {"description": "Required. The user-provided identifier of the `HcxActivationKey` to be created. This identifier must be unique among `HcxActivationKey` resources within the parent and becomes the final token in the name URI. The identifier must meet the following requirements: * Only contains 1-63 alphanumeric characters and hyphens * Begins with an alphabetical character * Ends with a non-hyphen character * Not formatted as a UUID * Complies with [RFC 1034](https://datatracker.ietf.org/doc/html/rfc1034) (section 3.5)", "location": "query", "type": "string"}, "parent": {"description": "Required. The resource name of the private cloud to create the key for. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/us-central1/privateClouds/my-cloud`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/privateClouds/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "A request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server guarantees that a request doesn't result in creation of duplicate commitments for at least 60 minutes. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (********-0000-0000-0000-********0000).", "location": "query", "type": "string"}}, "path": "v1/{+parent}/hcxActivationKeys", "request": {"$ref": "HcxActivationKey"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Retrieves a `HcxActivationKey` resource by its resource name.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}/hcxActivationKeys/{hcxActivationKeysId}", "httpMethod": "GET", "id": "vmwareengine.projects.locations.privateClouds.hcxActivationKeys.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the HCX activation key to retrieve. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/us-central1/privateClouds/my-cloud/hcxActivationKeys/my-key`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/privateClouds/[^/]+/hcxActivationKeys/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "HcxActivationKey"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getIamPolicy": {"description": "Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}/hcxActivationKeys/{hcxActivationKeysId}:getIamPolicy", "httpMethod": "GET", "id": "vmwareengine.projects.locations.privateClouds.hcxActivationKeys.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"options.requestedPolicyVersion": {"description": "Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "location": "query", "type": "integer"}, "resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/privateClouds/[^/]+/hcxActivationKeys/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:getIamPolicy", "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists `HcxActivationKey` resources in a given private cloud.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}/hcxActivationKeys", "httpMethod": "GET", "id": "vmwareengine.projects.locations.privateClouds.hcxActivationKeys.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The maximum number of HCX activation keys to return in one page. The service may return fewer than this value. The maximum value is coerced to 1000. The default value of this field is 500.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `ListHcxActivationKeys` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListHcxActivationKeys` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The resource name of the private cloud to be queried for HCX activation keys. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/us-central1/privateClouds/my-cloud`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/privateClouds/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/hcxActivationKeys", "response": {"$ref": "ListHcxActivationKeysResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}/hcxActivationKeys/{hcxActivationKeysId}:setIamPolicy", "httpMethod": "POST", "id": "vmwareengine.projects.locations.privateClouds.hcxActivationKeys.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/privateClouds/[^/]+/hcxActivationKeys/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:setIamPolicy", "request": {"$ref": "SetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"description": "Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may \"fail open\" without warning.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}/hcxActivationKeys/{hcxActivationKeysId}:testIamPermissions", "httpMethod": "POST", "id": "vmwareengine.projects.locations.privateClouds.hcxActivationKeys.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/privateClouds/[^/]+/hcxActivationKeys/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:testIamPermissions", "request": {"$ref": "TestIamPermissionsRequest"}, "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "loggingServers": {"methods": {"create": {"description": "Create a new logging server for a given private cloud.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}/loggingServers", "httpMethod": "POST", "id": "vmwareengine.projects.locations.privateClouds.loggingServers.create", "parameterOrder": ["parent"], "parameters": {"loggingServerId": {"description": "Required. The user-provided identifier of the `LoggingServer` to be created. This identifier must be unique among `LoggingServer` resources within the parent and becomes the final token in the name URI. The identifier must meet the following requirements: * Only contains 1-63 alphanumeric characters and hyphens * Begins with an alphabetical character * Ends with a non-hyphen character * Not formatted as a UUID * Complies with [RFC 1034](https://datatracker.ietf.org/doc/html/rfc1034) (section 3.5)", "location": "query", "type": "string"}, "parent": {"description": "Required. The resource name of the private cloud to create a new Logging Server in. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/us-central1-a/privateClouds/my-cloud`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/privateClouds/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server guarantees that a request doesn't result in creation of duplicate commitments for at least 60 minutes. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (********-0000-0000-0000-********0000).", "location": "query", "type": "string"}}, "path": "v1/{+parent}/loggingServers", "request": {"$ref": "LoggingServer"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a single logging server.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}/loggingServers/{loggingServersId}", "httpMethod": "DELETE", "id": "vmwareengine.projects.locations.privateClouds.loggingServers.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the logging server to delete. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/us-central1-a/privateClouds/my-cloud/loggingServers/my-logging-server`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/privateClouds/[^/]+/loggingServers/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server guarantees that a request doesn't result in creation of duplicate commitments for at least 60 minutes. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (********-0000-0000-0000-********0000).", "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of a logging server.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}/loggingServers/{loggingServersId}", "httpMethod": "GET", "id": "vmwareengine.projects.locations.privateClouds.loggingServers.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the Logging Server to retrieve. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/us-central1-a/privateClouds/my-cloud/loggingServers/my-logging-server`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/privateClouds/[^/]+/loggingServers/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "LoggingServer"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists logging servers configured for a given private cloud.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}/loggingServers", "httpMethod": "GET", "id": "vmwareengine.projects.locations.privateClouds.loggingServers.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "A filter expression that matches resources returned in the response. The expression must specify the field name, a comparison operator, and the value that you want to use for filtering. The value must be a string, a number, or a boolean. The comparison operator must be `=`, `!=`, `>`, or `<`. For example, if you are filtering a list of logging servers, you can exclude the ones named `example-server` by specifying `name != \"example-server\"`. To filter on multiple expressions, provide each separate expression within parentheses. For example: ``` (name = \"example-server\") (createTime > \"2021-04-12T08:15:10.40Z\") ``` By default, each expression is an `AND` expression. However, you can include `AND` and `OR` expressions explicitly. For example: ``` (name = \"example-server-1\") AND (createTime > \"2021-04-12T08:15:10.40Z\") OR (name = \"example-server-2\") ```", "location": "query", "type": "string"}, "orderBy": {"description": "Sorts list results by a certain order. By default, returned results are ordered by `name` in ascending order. You can also sort results in descending order based on the `name` value using `orderBy=\"name desc\"`. Currently, only ordering by `name` is supported.", "location": "query", "type": "string"}, "pageSize": {"description": "The maximum number of logging servers to return in one page. The service may return fewer than this value. The maximum value is coerced to 1000. The default value of this field is 500.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `ListLoggingServersRequest` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListLoggingServersRequest` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The resource name of the private cloud to be queried for logging servers. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/us-central1-a/privateClouds/my-cloud`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/privateClouds/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/loggingServers", "response": {"$ref": "ListLoggingServersResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates the parameters of a single logging server. Only fields specified in `update_mask` are applied.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}/loggingServers/{loggingServersId}", "httpMethod": "PATCH", "id": "vmwareengine.projects.locations.privateClouds.loggingServers.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only. The resource name of this logging server. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/us-central1-a/privateClouds/my-cloud/loggingServers/my-logging-server`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/privateClouds/[^/]+/loggingServers/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server guarantees that a request doesn't result in creation of duplicate commitments for at least 60 minutes. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (********-0000-0000-0000-********0000).", "location": "query", "type": "string"}, "updateMask": {"description": "Required. Field mask is used to specify the fields to be overwritten in the `LoggingServer` resource by the update. The fields specified in the `update_mask` are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then all fields will be overwritten.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "LoggingServer"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "managementDnsZoneBindings": {"methods": {"create": {"description": "Creates a new `ManagementDnsZoneBinding` resource in a private cloud. This RPC creates the DNS binding and the resource that represents the DNS binding of the consumer VPC network to the management DNS zone. A management DNS zone is the Cloud DNS cross-project binding zone that VMware Engine creates for each private cloud. It contains FQDNs and corresponding IP addresses for the private cloud's ESXi hosts and management VM appliances like vCenter and NSX Manager.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}/managementDnsZoneBindings", "httpMethod": "POST", "id": "vmwareengine.projects.locations.privateClouds.managementDnsZoneBindings.create", "parameterOrder": ["parent"], "parameters": {"managementDnsZoneBindingId": {"description": "Required. The user-provided identifier of the `ManagementDnsZoneBinding` resource to be created. This identifier must be unique among `ManagementDnsZoneBinding` resources within the parent and becomes the final token in the name URI. The identifier must meet the following requirements: * Only contains 1-63 alphanumeric characters and hyphens * Begins with an alphabetical character * Ends with a non-hyphen character * Not formatted as a UUID * Complies with [RFC 1034](https://datatracker.ietf.org/doc/html/rfc1034) (section 3.5)", "location": "query", "type": "string"}, "parent": {"description": "Required. The resource name of the private cloud to create a new management DNS zone binding for. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/us-central1-a/privateClouds/my-cloud`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/privateClouds/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server guarantees that a request doesn't result in creation of duplicate commitments for at least 60 minutes. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if the original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (********-0000-0000-0000-********0000).", "location": "query", "type": "string"}}, "path": "v1/{+parent}/managementDnsZoneBindings", "request": {"$ref": "ManagementDnsZoneBinding"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a `ManagementDnsZoneBinding` resource. When a management DNS zone binding is deleted, the corresponding consumer VPC network is no longer bound to the management DNS zone.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}/managementDnsZoneBindings/{managementDnsZoneBindingsId}", "httpMethod": "DELETE", "id": "vmwareengine.projects.locations.privateClouds.managementDnsZoneBindings.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the management DNS zone binding to delete. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/us-central1-a/privateClouds/my-cloud/managementDnsZoneBindings/my-management-dns-zone-binding`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/privateClouds/[^/]+/managementDnsZoneBindings/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server guarantees that a request doesn't result in creation of duplicate commitments for at least 60 minutes. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if the original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (********-0000-0000-0000-********0000).", "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Retrieves a 'ManagementDnsZoneBinding' resource by its resource name.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}/managementDnsZoneBindings/{managementDnsZoneBindingsId}", "httpMethod": "GET", "id": "vmwareengine.projects.locations.privateClouds.managementDnsZoneBindings.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the management DNS zone binding to retrieve. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/us-central1-a/privateClouds/my-cloud/managementDnsZoneBindings/my-management-dns-zone-binding`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/privateClouds/[^/]+/managementDnsZoneBindings/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "ManagementDnsZoneBinding"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists Consumer VPCs bound to Management DNS Zone of a given private cloud.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}/managementDnsZoneBindings", "httpMethod": "GET", "id": "vmwareengine.projects.locations.privateClouds.managementDnsZoneBindings.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "A filter expression that matches resources returned in the response. The expression must specify the field name, a comparison operator, and the value that you want to use for filtering. The value must be a string, a number, or a boolean. The comparison operator must be `=`, `!=`, `>`, or `<`. For example, if you are filtering a list of Management DNS Zone Bindings, you can exclude the ones named `example-management-dns-zone-binding` by specifying `name != \"example-management-dns-zone-binding\"`. To filter on multiple expressions, provide each separate expression within parentheses. For example: ``` (name = \"example-management-dns-zone-binding\") (createTime > \"2021-04-12T08:15:10.40Z\") ``` By default, each expression is an `AND` expression. However, you can include `AND` and `OR` expressions explicitly. For example: ``` (name = \"example-management-dns-zone-binding-1\") AND (createTime > \"2021-04-12T08:15:10.40Z\") OR (name = \"example-management-dns-zone-binding-2\") ```", "location": "query", "type": "string"}, "orderBy": {"description": "Sorts list results by a certain order. By default, returned results are ordered by `name` in ascending order. You can also sort results in descending order based on the `name` value using `orderBy=\"name desc\"`. Currently, only ordering by `name` is supported.", "location": "query", "type": "string"}, "pageSize": {"description": "The maximum number of management DNS zone bindings to return in one page. The service may return fewer than this value. The maximum value is coerced to 1000. The default value of this field is 500.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `ListManagementDnsZoneBindings` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListManagementDnsZoneBindings` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The resource name of the private cloud to be queried for management DNS zone bindings. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/us-central1-a/privateClouds/my-cloud`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/privateClouds/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/managementDnsZoneBindings", "response": {"$ref": "ListManagementDnsZoneBindingsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates a `ManagementDnsZoneBinding` resource. Only fields specified in `update_mask` are applied.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}/managementDnsZoneBindings/{managementDnsZoneBindingsId}", "httpMethod": "PATCH", "id": "vmwareengine.projects.locations.privateClouds.managementDnsZoneBindings.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only. The resource name of this binding. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/us-central1-a/privateClouds/my-cloud/managementDnsZoneBindings/my-management-dns-zone-binding`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/privateClouds/[^/]+/managementDnsZoneBindings/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server guarantees that a request doesn't result in creation of duplicate commitments for at least 60 minutes. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if the original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (********-0000-0000-0000-********0000).", "location": "query", "type": "string"}, "updateMask": {"description": "Required. Field mask is used to specify the fields to be overwritten in the `ManagementDnsZoneBinding` resource by the update. The fields specified in the `update_mask` are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then all fields will be overwritten.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "ManagementDnsZoneBinding"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "repair": {"description": "Retries to create a `ManagementDnsZoneBinding` resource that is in failed state.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}/managementDnsZoneBindings/{managementDnsZoneBindingsId}:repair", "httpMethod": "POST", "id": "vmwareengine.projects.locations.privateClouds.managementDnsZoneBindings.repair", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the management DNS zone binding to repair. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/us-central1-a/privateClouds/my-cloud/managementDnsZoneBindings/my-management-dns-zone-binding`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/privateClouds/[^/]+/managementDnsZoneBindings/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:repair", "request": {"$ref": "RepairManagementDnsZoneBindingRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "subnets": {"methods": {"get": {"description": "Gets details of a single subnet.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}/subnets/{subnetsId}", "httpMethod": "GET", "id": "vmwareengine.projects.locations.privateClouds.subnets.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the subnet to retrieve. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/us-central1-a/privateClouds/my-cloud/subnets/my-subnet`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/privateClouds/[^/]+/subnets/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Subnet"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists subnets in a given private cloud.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}/subnets", "httpMethod": "GET", "id": "vmwareengine.projects.locations.privateClouds.subnets.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The maximum number of subnets to return in one page. The service may return fewer than this value. The maximum value is coerced to 1000. The default value of this field is 500.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `ListSubnetsRequest` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListSubnetsRequest` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The resource name of the private cloud to be queried for subnets. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/us-central1-a/privateClouds/my-cloud`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/privateClouds/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/subnets", "response": {"$ref": "ListSubnetsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates the parameters of a single subnet. Only fields specified in `update_mask` are applied. *Note*: This API is synchronous and always returns a successful `google.longrunning.Operation` (LRO). The returned LRO will only have `done` and `response` fields.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}/subnets/{subnetsId}", "httpMethod": "PATCH", "id": "vmwareengine.projects.locations.privateClouds.subnets.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only. Identifier. The resource name of this subnet. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/us-central1-a/privateClouds/my-cloud/subnets/my-subnet`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/privateClouds/[^/]+/subnets/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Required. Field mask is used to specify the fields to be overwritten in the `Subnet` resource by the update. The fields specified in the `update_mask` are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then all fields will be overwritten.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "Subnet"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "upgrades": {"methods": {"get": {"description": "Retrieves a private cloud `Upgrade` resource by its resource name.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}/upgrades/{upgradesId}", "httpMethod": "GET", "id": "vmwareengine.projects.locations.privateClouds.upgrades.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the `Upgrade` resource to be retrieved. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/us-west1-a/privateClouds/my-cloud/upgrades/my-upgrade`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/privateClouds/[^/]+/upgrades/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Upgrade"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists past, ongoing and upcoming `Upgrades` for the given private cloud.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}/upgrades", "httpMethod": "GET", "id": "vmwareengine.projects.locations.privateClouds.upgrades.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "A filter expression that matches resources returned in the response. The expression must specify the field name, a comparison operator, and the value that you want to use for filtering. The value must be a string, a number, or a boolean. The comparison operator must be `=`, `!=`, `>`, or `<`. For example, if you are filtering a list of upgrades, you can exclude the ones named `example-upgrade1` by specifying `name != \"example-upgrade1\"`. You can also filter nested fields. To filter on multiple expressions, provide each separate expression within parentheses. For example: ``` (name = \"example-upgrade\") (createTime > \"2021-04-12T08:15:10.40Z\") ``` By default, each expression is an `AND` expression. However, you can include `AND` and `OR` expressions explicitly. For example: ``` (name = \"upgrade-1\") AND (createTime > \"2021-04-12T08:15:10.40Z\") OR (name = \"upgrade-2\") ```", "location": "query", "type": "string"}, "orderBy": {"description": "Sorts list results by a certain order. By default, returned results are ordered by `name` in ascending order. You can also sort results in descending order based on the `name` value using `orderBy=\"name desc\"`. Currently, only ordering by `name` is supported.", "location": "query", "type": "string"}, "pageSize": {"description": "The maximum number of `Upgrades` to return in one page. The service may return fewer resources than this value. The maximum value is coerced to 1000. The default value of this field is 500.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `ListUpgrades` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListUpgrades` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. Query a list of `Upgrades` for the given private cloud resource name. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/us-west1-a/privateClouds/my-cloud`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/privateClouds/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/upgrades", "response": {"$ref": "ListUpgradesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Update the private cloud `Upgrade` resource. Only `schedule` field can updated. The schedule can only be updated when the upgrade has not started and schedule edit window is open. Only fields specified in `update_mask` are considered.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}/upgrades/{upgradesId}", "httpMethod": "PATCH", "id": "vmwareengine.projects.locations.privateClouds.upgrades.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only. Identifier. The resource name of the private cloud `Upgrade`. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/us-west1-a/privateClouds/my-cloud/upgrades/my-upgrade`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/privateClouds/[^/]+/upgrades/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server guarantees that a request doesn't result in creation of duplicate commitments for at least 60 minutes. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (********-0000-0000-0000-********0000).", "location": "query", "type": "string"}, "updateMask": {"description": "Required. Field mask is used to specify the fields to be overwritten in the `Upgrade` resource by the update. The fields specified in the `update_mask` are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then all fields will be overwritten.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "Upgrade"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}, "privateConnections": {"methods": {"create": {"description": "Creates a new private connection that can be used for accessing private Clouds.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/privateConnections", "httpMethod": "POST", "id": "vmwareengine.projects.locations.privateConnections.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The resource name of the location to create the new private connection in. Private connection is a regional resource. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/us-central1`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "privateConnectionId": {"description": "Required. The user-provided identifier of the new private connection. This identifier must be unique among private connection resources within the parent and becomes the final token in the name URI. The identifier must meet the following requirements: * Only contains 1-63 alphanumeric characters and hyphens * Begins with an alphabetical character * Ends with a non-hyphen character * Not formatted as a UUID * Complies with [RFC 1034](https://datatracker.ietf.org/doc/html/rfc1034) (section 3.5)", "location": "query", "type": "string"}, "requestId": {"description": "Optional. A request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server guarantees that a request doesn't result in creation of duplicate commitments for at least 60 minutes. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (********-0000-0000-0000-********0000).", "location": "query", "type": "string"}}, "path": "v1/{+parent}/privateConnections", "request": {"$ref": "PrivateConnection"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a `PrivateConnection` resource. When a private connection is deleted for a VMware Engine network, the connected network becomes inaccessible to that VMware Engine network.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/privateConnections/{privateConnectionsId}", "httpMethod": "DELETE", "id": "vmwareengine.projects.locations.privateConnections.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the private connection to be deleted. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/us-central1/privateConnections/my-connection`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/privateConnections/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server guarantees that a request doesn't result in creation of duplicate commitments for at least 60 minutes. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (********-0000-0000-0000-********0000).", "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Retrieves a `PrivateConnection` resource by its resource name. The resource contains details of the private connection, such as connected network, routing mode and state.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/privateConnections/{privateConnectionsId}", "httpMethod": "GET", "id": "vmwareengine.projects.locations.privateConnections.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the private connection to retrieve. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/us-central1/privateConnections/my-connection`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/privateConnections/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "PrivateConnection"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists `PrivateConnection` resources in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/privateConnections", "httpMethod": "GET", "id": "vmwareengine.projects.locations.privateConnections.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "A filter expression that matches resources returned in the response. The expression must specify the field name, a comparison operator, and the value that you want to use for filtering. The value must be a string, a number, or a boolean. The comparison operator must be `=`, `!=`, `>`, or `<`. For example, if you are filtering a list of private connections, you can exclude the ones named `example-connection` by specifying `name != \"example-connection\"`. To filter on multiple expressions, provide each separate expression within parentheses. For example: ``` (name = \"example-connection\") (createTime > \"2022-09-22T08:15:10.40Z\") ``` By default, each expression is an `AND` expression. However, you can include `AND` and `OR` expressions explicitly. For example: ``` (name = \"example-connection-1\") AND (createTime > \"2021-04-12T08:15:10.40Z\") OR (name = \"example-connection-2\") ```", "location": "query", "type": "string"}, "orderBy": {"description": "Sorts list results by a certain order. By default, returned results are ordered by `name` in ascending order. You can also sort results in descending order based on the `name` value using `orderBy=\"name desc\"`. Currently, only ordering by `name` is supported.", "location": "query", "type": "string"}, "pageSize": {"description": "The maximum number of private connections to return in one page. The maximum value is coerced to 1000. The default value of this field is 500.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `ListPrivateConnections` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListPrivateConnections` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The resource name of the location to query for private connections. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/us-central1`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/privateConnections", "response": {"$ref": "ListPrivateConnectionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Modifies a `PrivateConnection` resource. Only `description` and `routing_mode` fields can be updated. Only fields specified in `updateMask` are applied.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/privateConnections/{privateConnectionsId}", "httpMethod": "PATCH", "id": "vmwareengine.projects.locations.privateConnections.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only. The resource name of the private connection. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/us-central1/privateConnections/my-connection`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/privateConnections/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server guarantees that a request doesn't result in creation of duplicate commitments for at least 60 minutes. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (********-0000-0000-0000-********0000).", "location": "query", "type": "string"}, "updateMask": {"description": "Required. Field mask is used to specify the fields to be overwritten in the `PrivateConnection` resource by the update. The fields specified in the `update_mask` are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then all fields will be overwritten.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "PrivateConnection"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"peeringRoutes": {"methods": {"list": {"description": "Lists the private connection routes exchanged over a peering connection.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/privateConnections/{privateConnectionsId}/peeringRoutes", "httpMethod": "GET", "id": "vmwareengine.projects.locations.privateConnections.peeringRoutes.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The maximum number of peering routes to return in one page. The service may return fewer than this value. The maximum value is coerced to 1000. The default value of this field is 500.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `ListPrivateConnectionPeeringRoutes` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListPrivateConnectionPeeringRoutes` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The resource name of the private connection to retrieve peering routes from. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/us-west1/privateConnections/my-connection`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/privateConnections/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/peeringRoutes", "response": {"$ref": "ListPrivateConnectionPeeringRoutesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}, "vmwareEngineNetworks": {"methods": {"create": {"description": "Creates a new VMware Engine network that can be used by a private cloud.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/vmwareEngineNetworks", "httpMethod": "POST", "id": "vmwareengine.projects.locations.vmwareEngineNetworks.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The resource name of the location to create the new VMware Engine network in. A VMware Engine network of type `LEGACY` is a regional resource, and a VMware Engine network of type `STANDARD` is a global resource. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/global`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server guarantees that a request doesn't result in creation of duplicate commitments for at least 60 minutes. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (********-0000-0000-0000-********0000).", "location": "query", "type": "string"}, "vmwareEngineNetworkId": {"description": "Required. The user-provided identifier of the new VMware Engine network. This identifier must be unique among VMware Engine network resources within the parent and becomes the final token in the name URI. The identifier must meet the following requirements: * For networks of type LEGACY, adheres to the format: `{region-id}-default`. Replace `{region-id}` with the region where you want to create the VMware Engine network. For example, \"us-central1-default\". * Only contains 1-63 alphanumeric characters and hyphens * Begins with an alphabetical character * Ends with a non-hyphen character * Not formatted as a UUID * Complies with [RFC 1034](https://datatracker.ietf.org/doc/html/rfc1034) (section 3.5)", "location": "query", "type": "string"}}, "path": "v1/{+parent}/vmwareEngineNetworks", "request": {"$ref": "VmwareEngineNetwork"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a `VmwareEngineNetwork` resource. You can only delete a VMware Engine network after all resources that refer to it are deleted. For example, a private cloud, a network peering, and a network policy can all refer to the same VMware Engine network.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/vmwareEngineNetworks/{vmwareEngineNetworksId}", "httpMethod": "DELETE", "id": "vmwareengine.projects.locations.vmwareEngineNetworks.delete", "parameterOrder": ["name"], "parameters": {"etag": {"description": "Optional. Checksum used to ensure that the user-provided value is up to date before the server processes the request. The server compares provided checksum with the current checksum of the resource. If the user-provided value is out of date, this request returns an `ABORTED` error.", "location": "query", "type": "string"}, "name": {"description": "Required. The resource name of the VMware Engine network to be deleted. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/global/vmwareEngineNetworks/my-network`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/vmwareEngineNetworks/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server guarantees that a request doesn't result in creation of duplicate commitments for at least 60 minutes. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (********-0000-0000-0000-********0000).", "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Retrieves a `VmwareEngineNetwork` resource by its resource name. The resource contains details of the VMware Engine network, such as its VMware Engine network type, peered networks in a service project, and state (for example, `CREATING`, `ACTIVE`, `DELETING`).", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/vmwareEngineNetworks/{vmwareEngineNetworksId}", "httpMethod": "GET", "id": "vmwareengine.projects.locations.vmwareEngineNetworks.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the VMware Engine network to retrieve. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/global/vmwareEngineNetworks/my-network`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/vmwareEngineNetworks/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "VmwareEngineNetwork"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists `VmwareEngineNetwork` resources in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/vmwareEngineNetworks", "httpMethod": "GET", "id": "vmwareengine.projects.locations.vmwareEngineNetworks.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "A filter expression that matches resources returned in the response. The expression must specify the field name, a comparison operator, and the value that you want to use for filtering. The value must be a string, a number, or a boolean. The comparison operator must be `=`, `!=`, `>`, or `<`. For example, if you are filtering a list of network peerings, you can exclude the ones named `example-network` by specifying `name != \"example-network\"`. To filter on multiple expressions, provide each separate expression within parentheses. For example: ``` (name = \"example-network\") (createTime > \"2021-04-12T08:15:10.40Z\") ``` By default, each expression is an `AND` expression. However, you can include `AND` and `OR` expressions explicitly. For example: ``` (name = \"example-network-1\") AND (createTime > \"2021-04-12T08:15:10.40Z\") OR (name = \"example-network-2\") ```", "location": "query", "type": "string"}, "orderBy": {"description": "Sorts list results by a certain order. By default, returned results are ordered by `name` in ascending order. You can also sort results in descending order based on the `name` value using `orderBy=\"name desc\"`. Currently, only ordering by `name` is supported.", "location": "query", "type": "string"}, "pageSize": {"description": "The maximum number of results to return in one page. The maximum value is coerced to 1000. The default value of this field is 500.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `ListVmwareEngineNetworks` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListVmwareEngineNetworks` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The resource name of the location to query for VMware Engine networks. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/global`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/vmwareEngineNetworks", "response": {"$ref": "ListVmwareEngineNetworksResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Modifies a VMware Engine network resource. Only the following fields can be updated: `description`. Only fields specified in `updateMask` are applied.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/vmwareEngineNetworks/{vmwareEngineNetworksId}", "httpMethod": "PATCH", "id": "vmwareengine.projects.locations.vmwareEngineNetworks.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only. Identifier. The resource name of the VMware Engine network. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/global/vmwareEngineNetworks/my-network`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/vmwareEngineNetworks/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server guarantees that a request doesn't result in creation of duplicate commitments for at least 60 minutes. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (********-0000-0000-0000-********0000).", "location": "query", "type": "string"}, "updateMask": {"description": "Required. Field mask is used to specify the fields to be overwritten in the VMware Engine network resource by the update. The fields specified in the `update_mask` are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then all fields will be overwritten. Only the following fields can be updated: `description`.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "VmwareEngineNetwork"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}}}}, "revision": "20250603", "rootUrl": "https://vmwareengine.googleapis.com/", "schemas": {"Announcement": {"description": "Announcement for the resources of Vmware Engine.", "id": "Announcement", "properties": {"activityType": {"description": "Optional. Activity type of the announcement There can be only one active announcement for a given activity type and target resource.", "type": "string"}, "cluster": {"description": "A Cluster resource name.", "type": "string"}, "code": {"description": "Required. Code of the announcement. Indicates the presence of a VMware Engine related announcement and corresponds to a related message in the `description` field.", "type": "string"}, "createTime": {"description": "Output only. Creation time of this resource. It also serves as start time of notification.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "Output only. Description of the announcement.", "readOnly": true, "type": "string"}, "metadata": {"additionalProperties": {"type": "string"}, "description": "Output only. Additional structured details about this announcement.", "readOnly": true, "type": "object"}, "name": {"description": "Output only. The resource name of the announcement. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/us-west1-a/announcements/my-announcement-id`", "readOnly": true, "type": "string"}, "privateCloud": {"description": "A Private Cloud resource name.", "type": "string"}, "state": {"description": "Output only. State of the resource. New values may be added to this enum when appropriate.", "enum": ["STATE_UNSPECIFIED", "ACTIVE", "INACTIVE", "DELETING", "CREATING"], "enumDescriptions": ["The default value. This value should never be used.", "Active announcement which should be visible to user.", "Inactive announcement which should not be visible to user.", "Announcement which is being deleted", "Announcement which being created"], "readOnly": true, "type": "string"}, "targetResourceType": {"description": "Output only. Target Resource Type defines the type of the target for the announcement", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. Last update time of this resource.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "AuditConfig": {"description": "Specifies the audit configuration for a service. The configuration determines which permission types are logged, and what identities, if any, are exempted from logging. An AuditConfig must have one or more AuditLogConfigs. If there are AuditConfigs for both `allServices` and a specific service, the union of the two AuditConfigs is used for that service: the log_types specified in each AuditConfig are enabled, and the exempted_members in each AuditLogConfig are exempted. Example Policy with multiple AuditConfigs: { \"audit_configs\": [ { \"service\": \"allServices\", \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\", \"exempted_members\": [ \"user:<EMAIL>\" ] }, { \"log_type\": \"DATA_WRITE\" }, { \"log_type\": \"ADMIN_READ\" } ] }, { \"service\": \"sampleservice.googleapis.com\", \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\" }, { \"log_type\": \"DATA_WRITE\", \"exempted_members\": [ \"user:<EMAIL>\" ] } ] } ] } For sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ logging. It also exempts `<EMAIL>` from DATA_READ logging, and `<EMAIL>` from DATA_WRITE logging.", "id": "AuditConfig", "properties": {"auditLogConfigs": {"description": "The configuration for logging of each type of permission.", "items": {"$ref": "AuditLogConfig"}, "type": "array"}, "service": {"description": "Specifies a service that will be enabled for audit logging. For example, `storage.googleapis.com`, `cloudsql.googleapis.com`. `allServices` is a special value that covers all services.", "type": "string"}}, "type": "object"}, "AuditLogConfig": {"description": "Provides the configuration for logging a type of permissions. Example: { \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\", \"exempted_members\": [ \"user:<EMAIL>\" ] }, { \"log_type\": \"DATA_WRITE\" } ] } This enables 'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from DATA_READ logging.", "id": "AuditLogConfig", "properties": {"exemptedMembers": {"description": "Specifies the identities that do not cause logging for this type of permission. Follows the same format of Binding.members.", "items": {"type": "string"}, "type": "array"}, "logType": {"description": "The log type that this config enables.", "enum": ["LOG_TYPE_UNSPECIFIED", "ADMIN_READ", "DATA_WRITE", "DATA_READ"], "enumDescriptions": ["Default case. Should never be this.", "Admin reads. Example: CloudIAM getIamPolicy", "Data writes. Example: CloudSQL Users create", "Data reads. Example: CloudSQL Users list"], "type": "string"}}, "type": "object"}, "AutoscalingPolicy": {"description": "Autoscaling policy describes the behavior of the autoscaling with respect to the resource utilization. The scale-out operation is initiated if the utilization exceeds ANY of the respective thresholds. The scale-in operation is initiated if the utilization is below ALL of the respective thresholds.", "id": "AutoscalingPolicy", "properties": {"consumedMemoryThresholds": {"$ref": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Optional. Utilization thresholds pertaining to amount of consumed memory."}, "cpuThresholds": {"$ref": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Optional. Utilization thresholds pertaining to CPU utilization."}, "grantedMemoryThresholds": {"$ref": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Optional. Utilization thresholds pertaining to amount of granted memory."}, "nodeTypeId": {"description": "Required. The canonical identifier of the node type to add or remove. Corresponds to the `NodeType`.", "type": "string"}, "scaleOutSize": {"description": "Required. Number of nodes to add to a cluster during a scale-out operation. Must be divisible by 2 for stretched clusters. During a scale-in operation only one node (or 2 for stretched clusters) are removed in a single iteration.", "format": "int32", "type": "integer"}, "storageThresholds": {"$ref": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Optional. Utilization thresholds pertaining to amount of consumed storage."}}, "type": "object"}, "AutoscalingSettings": {"description": "Autoscaling settings define the rules used by VMware Engine to automatically scale-out and scale-in the clusters in a private cloud.", "id": "AutoscalingSettings", "properties": {"autoscalingPolicies": {"additionalProperties": {"$ref": "AutoscalingPolicy"}, "description": "Required. The map with autoscaling policies applied to the cluster. The key is the identifier of the policy. It must meet the following requirements: * Only contains 1-63 alphanumeric characters and hyphens * Begins with an alphabetical character * Ends with a non-hyphen character * Not formatted as a UUID * Complies with [RFC 1034](https://datatracker.ietf.org/doc/html/rfc1034) (section 3.5) Currently there map must contain only one element that describes the autoscaling policy for compute nodes.", "type": "object"}, "coolDownPeriod": {"description": "Optional. The minimum duration between consecutive autoscale operations. It starts once addition or removal of nodes is fully completed. Defaults to 30 minutes if not specified. Cool down period must be in whole minutes (for example, 30, 31, 50, 180 minutes).", "format": "google-duration", "type": "string"}, "maxClusterNodeCount": {"description": "Optional. Maximum number of nodes of any type in a cluster. If not specified the default limits apply.", "format": "int32", "type": "integer"}, "minClusterNodeCount": {"description": "Optional. Minimum number of nodes of any type in a cluster. If not specified the default limits apply.", "format": "int32", "type": "integer"}}, "type": "object"}, "Binding": {"description": "Associates `members`, or principals, with a `role`.", "id": "Binding", "properties": {"condition": {"$ref": "Expr", "description": "The condition that is associated with this binding. If the condition evaluates to `true`, then this binding applies to the current request. If the condition evaluates to `false`, then this binding does not apply to the current request. However, a different role binding might grant the same role to one or more of the principals in this binding. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies)."}, "members": {"description": "Specifies the principals requesting access for a Google Cloud resource. `members` can have the following values: * `allUsers`: A special identifier that represents anyone who is on the internet; with or without a Google account. * `allAuthenticatedUsers`: A special identifier that represents anyone who is authenticated with a Google account or a service account. Does not include identities that come from external identity providers (IdPs) through identity federation. * `user:{emailid}`: An email address that represents a specific Google account. For example, `<EMAIL>` . * `serviceAccount:{emailid}`: An email address that represents a Google service account. For example, `<EMAIL>`. * `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`: An identifier for a [Kubernetes service account](https://cloud.google.com/kubernetes-engine/docs/how-to/kubernetes-service-accounts). For example, `my-project.svc.id.goog[my-namespace/my-kubernetes-sa]`. * `group:{emailid}`: An email address that represents a Google group. For example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain (primary) that represents all the users of that domain. For example, `google.com` or `example.com`. * `principal://iam.googleapis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: A single identity in a workforce identity pool. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/group/{group_id}`: All workforce identities in a group. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/attribute.{attribute_name}/{attribute_value}`: All workforce identities with a specific attribute value. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/*`: All identities in a workforce identity pool. * `principal://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/subject/{subject_attribute_value}`: A single identity in a workload identity pool. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/group/{group_id}`: A workload identity pool group. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/attribute.{attribute_name}/{attribute_value}`: All identities in a workload identity pool with a certain attribute. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/*`: All identities in a workload identity pool. * `deleted:user:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a user that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the user is recovered, this value reverts to `user:{emailid}` and the recovered user retains the role in the binding. * `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a service account that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the service account is undeleted, this value reverts to `serviceAccount:{emailid}` and the undeleted service account retains the role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a Google group that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the group is recovered, this value reverts to `group:{emailid}` and the recovered group retains the role in the binding. * `deleted:principal://iam.googleapis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: Deleted single identity in a workforce identity pool. For example, `deleted:principal://iam.googleapis.com/locations/global/workforcePools/my-pool-id/subject/my-subject-attribute-value`.", "items": {"type": "string"}, "type": "array"}, "role": {"description": "Role that is assigned to the list of `members`, or principals. For example, `roles/viewer`, `roles/editor`, or `roles/owner`. For an overview of the IAM roles and permissions, see the [IAM documentation](https://cloud.google.com/iam/docs/roles-overview). For a list of the available pre-defined roles, see [here](https://cloud.google.com/iam/docs/understanding-roles).", "type": "string"}}, "type": "object"}, "Cluster": {"description": "A cluster in a private cloud.", "id": "Cluster", "properties": {"autoscalingSettings": {"$ref": "AutoscalingSettings", "description": "Optional. Configuration of the autoscaling applied to this cluster."}, "createTime": {"description": "Output only. Creation time of this resource.", "format": "google-datetime", "readOnly": true, "type": "string"}, "management": {"description": "Output only. True if the cluster is a management cluster; false otherwise. There can only be one management cluster in a private cloud and it has to be the first one.", "readOnly": true, "type": "boolean"}, "name": {"description": "Output only. Identifier. The resource name of this cluster. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/us-central1-a/privateClouds/my-cloud/clusters/my-cluster`", "readOnly": true, "type": "string"}, "nodeTypeConfigs": {"additionalProperties": {"$ref": "NodeTypeConfig"}, "description": "Required. The map of cluster node types in this cluster, where the key is canonical identifier of the node type (corresponds to the `NodeType`).", "type": "object"}, "state": {"description": "Output only. State of the resource.", "enum": ["STATE_UNSPECIFIED", "ACTIVE", "CREATING", "UPDATING", "DELETING", "REPAIRING"], "enumDescriptions": ["The default value. This value should never be used.", "The Cluster is operational and can be used by the user.", "The Cluster is being deployed.", "Adding or removing of a node to the cluster, any other cluster specific updates.", "The Cluster is being deleted.", "The Cluster is undergoing maintenance, for example: a failed node is getting replaced."], "readOnly": true, "type": "string"}, "stretchedClusterConfig": {"$ref": "StretchedClusterConfig", "description": "Optional. Configuration of a stretched cluster. Required for clusters that belong to a STRETCHED private cloud."}, "uid": {"description": "Output only. System-generated unique identifier for the resource.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. Last update time of this resource.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "Constraints": {"description": "Constraints to be applied while editing a schedule. These constraints ensure that `Upgrade` specific requirements are met.", "id": "Constraints", "properties": {"disallowedIntervals": {"description": "Output only. Output Only. A list of intervals in which maintenance windows are not allowed. Any time window that overlaps with any of these intervals will be considered invalid.", "items": {"$ref": "WeeklyTimeInterval"}, "readOnly": true, "type": "array"}, "minHoursDay": {"description": "Output only. Minimum number of hours must be allotted for the upgrade activities for each selected day. This is a minimum; the upgrade schedule can allot more hours for the given day.", "format": "int32", "readOnly": true, "type": "integer"}, "minHoursWeek": {"description": "Output only. The minimum number of weekly hours must be allotted for the upgrade activities. This is just a minimum; the schedule can assign more weekly hours.", "format": "int32", "readOnly": true, "type": "integer"}, "rescheduleDateRange": {"$ref": "Interval", "description": "Output only. Output Only. The user can only reschedule an upgrade that starts within this range.", "readOnly": true}}, "type": "object"}, "Credentials": {"description": "Credentials for a private cloud.", "id": "Credentials", "properties": {"password": {"description": "Initial password.", "type": "string"}, "username": {"description": "Initial username.", "type": "string"}}, "type": "object"}, "DnsBindPermission": {"description": "DnsBindPermission resource that contains the accounts having the consumer DNS bind permission on the corresponding intranet VPC of the consumer project.", "id": "DnsBindPermission", "properties": {"name": {"description": "Required. Output only. The name of the resource which stores the users/service accounts having the permission to bind to the corresponding intranet VPC of the consumer project. DnsBindPermission is a global resource and location can only be global. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/global/dnsBindPermission`", "readOnly": true, "type": "string"}, "principals": {"description": "Output only. Users/Service accounts which have access for binding on the intranet VPC project corresponding to the consumer project.", "items": {"$ref": "Principal"}, "readOnly": true, "type": "array"}}, "type": "object"}, "DnsForwarding": {"description": "DNS forwarding config. This config defines a list of domain to name server mappings, and is attached to the private cloud for custom domain resolution.", "id": "DnsForwarding", "properties": {"createTime": {"description": "Output only. Creation time of this resource.", "format": "google-datetime", "readOnly": true, "type": "string"}, "forwardingRules": {"description": "Required. List of domain mappings to configure", "items": {"$ref": "ForwardingRule"}, "type": "array"}, "name": {"description": "Output only. Identifier. The resource name of this DNS profile. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/us-central1-a/privateClouds/my-cloud/dnsForwarding`", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. Last update time of this resource.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "Empty": {"description": "A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }", "id": "Empty", "properties": {}, "type": "object"}, "Expr": {"description": "Represents a textual expression in the Common Expression Language (CEL) syntax. CEL is a C-like expression language. The syntax and semantics of CEL are documented at https://github.com/google/cel-spec. Example (Comparison): title: \"Summary size limit\" description: \"Determines if a summary is less than 100 chars\" expression: \"document.summary.size() < 100\" Example (Equality): title: \"Requestor is owner\" description: \"Determines if requestor is the document owner\" expression: \"document.owner == request.auth.claims.email\" Example (Logic): title: \"Public documents\" description: \"Determine whether the document should be publicly visible\" expression: \"document.type != 'private' && document.type != 'internal'\" Example (Data Manipulation): title: \"Notification string\" description: \"Create a notification string with a timestamp.\" expression: \"'New message received at ' + string(document.create_time)\" The exact variables and functions that may be referenced within an expression are determined by the service that evaluates it. See the service documentation for additional information.", "id": "Expr", "properties": {"description": {"description": "Optional. Description of the expression. This is a longer text which describes the expression, e.g. when hovered over it in a UI.", "type": "string"}, "expression": {"description": "Textual representation of an expression in Common Expression Language syntax.", "type": "string"}, "location": {"description": "Optional. String indicating the location of the expression for error reporting, e.g. a file name and a position in the file.", "type": "string"}, "title": {"description": "Optional. Title for the expression, i.e. a short string describing its purpose. This can be used e.g. in UIs which allow to enter the expression.", "type": "string"}}, "type": "object"}, "ExternalAccessRule": {"description": "External access firewall rules for filtering incoming traffic destined to `ExternalAddress` resources.", "id": "ExternalAccessRule", "properties": {"action": {"description": "The action that the external access rule performs.", "enum": ["ACTION_UNSPECIFIED", "ALLOW", "DENY"], "enumDescriptions": ["Defaults to allow.", "Allows connections that match the other specified components.", "Blocks connections that match the other specified components."], "type": "string"}, "createTime": {"description": "Output only. Creation time of this resource.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "User-provided description for this external access rule.", "type": "string"}, "destinationIpRanges": {"description": "If destination ranges are specified, the external access rule applies only to the traffic that has a destination IP address in these ranges. The specified IP addresses must have reserved external IP addresses in the scope of the parent network policy. To match all external IP addresses in the scope of the parent network policy, specify `0.0.0.0/0`. To match a specific external IP address, specify it using the `IpRange.external_address` property.", "items": {"$ref": "IpRange"}, "type": "array"}, "destinationPorts": {"description": "A list of destination ports to which the external access rule applies. This field is only applicable for the UDP or TCP protocol. Each entry must be either an integer or a range. For example: `[\"22\"]`, `[\"80\",\"443\"]`, or `[\"12345-12349\"]`. To match all destination ports, specify `[\"0-65535\"]`.", "items": {"type": "string"}, "type": "array"}, "ipProtocol": {"description": "The IP protocol to which the external access rule applies. This value can be one of the following three protocol strings (not case-sensitive): `tcp`, `udp`, or `icmp`.", "type": "string"}, "name": {"description": "Output only. The resource name of this external access rule. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/us-central1/networkPolicies/my-policy/externalAccessRules/my-rule`", "readOnly": true, "type": "string"}, "priority": {"description": "External access rule priority, which determines the external access rule to use when multiple rules apply. If multiple rules have the same priority, their ordering is non-deterministic. If specific ordering is required, assign unique priorities to enforce such ordering. The external access rule priority is an integer from 100 to 4096, both inclusive. Lower integers indicate higher precedence. For example, a rule with priority `100` has higher precedence than a rule with priority `101`.", "format": "int32", "type": "integer"}, "sourceIpRanges": {"description": "If source ranges are specified, the external access rule applies only to traffic that has a source IP address in these ranges. These ranges can either be expressed in the CIDR format or as an IP address. As only inbound rules are supported, `ExternalAddress` resources cannot be the source IP addresses of an external access rule. To match all source addresses, specify `0.0.0.0/0`.", "items": {"$ref": "IpRange"}, "type": "array"}, "sourcePorts": {"description": "A list of source ports to which the external access rule applies. This field is only applicable for the UDP or TCP protocol. Each entry must be either an integer or a range. For example: `[\"22\"]`, `[\"80\",\"443\"]`, or `[\"12345-12349\"]`. To match all source ports, specify `[\"0-65535\"]`.", "items": {"type": "string"}, "type": "array"}, "state": {"description": "Output only. The state of the resource.", "enum": ["STATE_UNSPECIFIED", "ACTIVE", "CREATING", "UPDATING", "DELETING"], "enumDescriptions": ["The default value. This value is used if the state is omitted.", "The rule is ready.", "The rule is being created.", "The rule is being updated.", "The rule is being deleted."], "readOnly": true, "type": "string"}, "uid": {"description": "Output only. System-generated unique identifier for the resource.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. Last update time of this resource.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "ExternalAddress": {"description": "Represents an allocated external IP address and its corresponding internal IP address in a private cloud.", "id": "ExternalAddress", "properties": {"createTime": {"description": "Output only. Creation time of this resource.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "User-provided description for this resource.", "type": "string"}, "externalIp": {"description": "Output only. The external IP address of a workload VM.", "readOnly": true, "type": "string"}, "internalIp": {"description": "The internal IP address of a workload VM.", "type": "string"}, "name": {"description": "Output only. Identifier. The resource name of this external IP address. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/us-central1-a/privateClouds/my-cloud/externalAddresses/my-address`", "readOnly": true, "type": "string"}, "state": {"description": "Output only. The state of the resource.", "enum": ["STATE_UNSPECIFIED", "ACTIVE", "CREATING", "UPDATING", "DELETING"], "enumDescriptions": ["The default value. This value should never be used.", "The address is ready.", "The address is being created.", "The address is being updated.", "The address is being deleted."], "readOnly": true, "type": "string"}, "uid": {"description": "Output only. System-generated unique identifier for the resource.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. Last update time of this resource.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "FetchNetworkPolicyExternalAddressesResponse": {"description": "Response message for VmwareEngine.FetchNetworkPolicyExternalAddresses", "id": "FetchNetworkPolicyExternalAddressesResponse", "properties": {"externalAddresses": {"description": "A list of external IP addresses assigned to VMware workload VMs within the scope of the given network policy.", "items": {"$ref": "ExternalAddress"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}}, "type": "object"}, "ForwardingRule": {"description": "A forwarding rule is a mapping of a `domain` to `name_servers`. This mapping allows VMware Engine to resolve domains for attached private clouds by forwarding DNS requests for a given domain to the specified nameservers.", "id": "ForwardingRule", "properties": {"domain": {"description": "Required. Domain used to resolve a `name_servers` list.", "type": "string"}, "nameServers": {"description": "Required. List of DNS servers to use for domain resolution", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GrantDnsBindPermissionRequest": {"description": "Request message for VmwareEngine.GrantDnsBindPermission", "id": "GrantDnsBindPermissionRequest", "properties": {"principal": {"$ref": "Principal", "description": "Required. The consumer provided user/service account which needs to be granted permission to bind with the intranet VPC corresponding to the consumer project."}, "requestId": {"description": "Optional. A request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server guarantees that a request doesn't result in creation of duplicate commitments for at least 60 minutes. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (********-0000-0000-0000-********0000).", "type": "string"}}, "type": "object"}, "Hcx": {"description": "Details about a HCX Cloud Manager appliance.", "id": "Hcx", "properties": {"fqdn": {"description": "Fully qualified domain name of the appliance.", "type": "string"}, "internalIp": {"description": "Internal IP address of the appliance.", "type": "string"}, "state": {"description": "Output only. The state of the appliance.", "enum": ["STATE_UNSPECIFIED", "ACTIVE", "CREATING", "ACTIVATING"], "enumDescriptions": ["Unspecified appliance state. This is the default value.", "The appliance is operational and can be used.", "The appliance is being deployed.", "The appliance is being activated."], "readOnly": true, "type": "string"}, "version": {"description": "Version of the appliance.", "type": "string"}}, "type": "object"}, "HcxActivationKey": {"description": "HCX activation key. A default key is created during private cloud provisioning, but this behavior is subject to change and you should always verify active keys. Use VmwareEngine.ListHcxActivationKeys to retrieve existing keys and VmwareEngine.CreateHcxActivationKey to create new ones.", "id": "HcxActivationKey", "properties": {"activationKey": {"description": "Output only. HCX activation key.", "readOnly": true, "type": "string"}, "createTime": {"description": "Output only. Creation time of HCX activation key.", "format": "google-datetime", "readOnly": true, "type": "string"}, "name": {"description": "Output only. The resource name of this HcxActivationKey. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/us-central1/privateClouds/my-cloud/hcxActivationKeys/my-key`", "readOnly": true, "type": "string"}, "state": {"description": "Output only. State of HCX activation key.", "enum": ["STATE_UNSPECIFIED", "AVAILABLE", "CONSUMED", "CREATING"], "enumDescriptions": ["Unspecified state.", "State of a newly generated activation key.", "State of key when it has been used to activate HCX appliance.", "State of key when it is being created."], "readOnly": true, "type": "string"}, "uid": {"description": "Output only. System-generated unique identifier for the resource.", "readOnly": true, "type": "string"}}, "type": "object"}, "Interval": {"description": "Represents a time interval, encoded as a Timestamp start (inclusive) and a Timestamp end (exclusive). The start must be less than or equal to the end. When the start equals the end, the interval is empty (matches no time). When both start and end are unspecified, the interval matches any time.", "id": "Interval", "properties": {"endTime": {"description": "Optional. Exclusive end of the interval. If specified, a Timestamp matching this interval will have to be before the end.", "format": "google-datetime", "type": "string"}, "startTime": {"description": "Optional. Inclusive start of the interval. If specified, a Timestamp matching this interval will have to be the same or after the start.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "IpRange": {"description": "An IP range provided in any one of the supported formats.", "id": "IpRange", "properties": {"externalAddress": {"description": "The name of an `ExternalAddress` resource. The external address must have been reserved in the scope of this external access rule's parent network policy. Provide the external address name in the form of `projects/{project}/locations/{location}/privateClouds/{private_cloud}/externalAddresses/{external_address}`. For example: `projects/my-project/locations/us-central1-a/privateClouds/my-cloud/externalAddresses/my-address`.", "type": "string"}, "ipAddress": {"description": "A single IP address. For example: `********`.", "type": "string"}, "ipAddressRange": {"description": "An IP address range in the CIDR format. For example: `10.0.0.0/24`.", "type": "string"}}, "type": "object"}, "ListAnnouncementsResponse": {"description": "Response message for VmwareEngine.ListAnnouncements", "id": "ListAnnouncementsResponse", "properties": {"announcements": {"description": "A list of announcement runs.", "items": {"$ref": "Announcement"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "unreachable": {"description": "list of unreachable locations", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListClustersResponse": {"description": "Response message for VmwareEngine.ListClusters", "id": "ListClustersResponse", "properties": {"clusters": {"description": "A list of private cloud clusters.", "items": {"$ref": "Cluster"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "unreachable": {"description": "Locations that could not be reached when making an aggregated query using wildcards.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListExternalAccessRulesResponse": {"description": "Response message for VmwareEngine.ListExternalAccessRules", "id": "ListExternalAccessRulesResponse", "properties": {"externalAccessRules": {"description": "A list of external access firewall rules.", "items": {"$ref": "ExternalAccessRule"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "unreachable": {"description": "Locations that could not be reached when making an aggregated query using wildcards.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListExternalAddressesResponse": {"description": "Response message for VmwareEngine.ListExternalAddresses", "id": "ListExternalAddressesResponse", "properties": {"externalAddresses": {"description": "A list of external IP addresses.", "items": {"$ref": "ExternalAddress"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "unreachable": {"description": "Locations that could not be reached when making an aggregated query using wildcards.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListHcxActivationKeysResponse": {"description": "Response message for VmwareEngine.ListHcxActivationKeys", "id": "ListHcxActivationKeysResponse", "properties": {"hcxActivationKeys": {"description": "List of HCX activation keys.", "items": {"$ref": "HcxActivationKey"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "unreachable": {"description": "Locations that could not be reached when making an aggregated query using wildcards.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListLocationsResponse": {"description": "The response message for Locations.ListLocations.", "id": "ListLocationsResponse", "properties": {"locations": {"description": "A list of locations that matches the specified filter in the request.", "items": {"$ref": "Location"}, "type": "array"}, "nextPageToken": {"description": "The standard List next-page token.", "type": "string"}}, "type": "object"}, "ListLoggingServersResponse": {"description": "Response message for VmwareEngine.ListLoggingServers", "id": "ListLoggingServersResponse", "properties": {"loggingServers": {"description": "A list of Logging Servers.", "items": {"$ref": "LoggingServer"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be send as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "unreachable": {"description": "Locations that could not be reached when making an aggregated query using wildcards.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListManagementDnsZoneBindingsResponse": {"description": "Response message for VmwareEngine.ListManagementDnsZoneBindings", "id": "ListManagementDnsZoneBindingsResponse", "properties": {"managementDnsZoneBindings": {"description": "A list of management DNS zone bindings.", "items": {"$ref": "ManagementDnsZoneBinding"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "unreachable": {"description": "Locations that could not be reached when making an aggregated query using wildcards.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListNetworkPeeringsResponse": {"description": "Response message for VmwareEngine.ListNetworkPeerings", "id": "ListNetworkPeeringsResponse", "properties": {"networkPeerings": {"description": "A list of network peerings.", "items": {"$ref": "NetworkPeering"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "unreachable": {"description": "Unreachable resources.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListNetworkPoliciesResponse": {"description": "Response message for VmwareEngine.ListNetworkPolicies", "id": "ListNetworkPoliciesResponse", "properties": {"networkPolicies": {"description": "A list of network policies.", "items": {"$ref": "NetworkPolicy"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be send as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "unreachable": {"description": "Locations that could not be reached when making an aggregated query using wildcards.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListNodeTypesResponse": {"description": "Response message for VmwareEngine.ListNodeTypes", "id": "ListNodeTypesResponse", "properties": {"nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "nodeTypes": {"description": "A list of Node Types.", "items": {"$ref": "NodeType"}, "type": "array"}, "unreachable": {"description": "Locations that could not be reached when making an aggregated query using wildcards.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListNodesResponse": {"description": "Response message for VmwareEngine.ListNodes", "id": "ListNodesResponse", "properties": {"nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "nodes": {"description": "The nodes.", "items": {"$ref": "Node"}, "type": "array"}}, "type": "object"}, "ListOperationsResponse": {"description": "The response message for Operations.ListOperations.", "id": "ListOperationsResponse", "properties": {"nextPageToken": {"description": "The standard List next-page token.", "type": "string"}, "operations": {"description": "A list of operations that matches the specified filter in the request.", "items": {"$ref": "Operation"}, "type": "array"}}, "type": "object"}, "ListPeeringRoutesResponse": {"description": "Response message for VmwareEngine.ListPeeringRoutes", "id": "ListPeeringRoutesResponse", "properties": {"nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "peeringRoutes": {"description": "A list of peering routes.", "items": {"$ref": "PeeringRoute"}, "type": "array"}}, "type": "object"}, "ListPrivateCloudsResponse": {"description": "Response message for VmwareEngine.ListPrivateClouds", "id": "ListPrivateCloudsResponse", "properties": {"nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "privateClouds": {"description": "A list of private clouds.", "items": {"$ref": "PrivateCloud"}, "type": "array"}, "unreachable": {"description": "Locations that could not be reached when making an aggregated query using wildcards.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListPrivateConnectionPeeringRoutesResponse": {"description": "Response message for VmwareEngine.ListPrivateConnectionPeeringRoutes", "id": "ListPrivateConnectionPeeringRoutesResponse", "properties": {"nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "peeringRoutes": {"description": "A list of peering routes.", "items": {"$ref": "PeeringRoute"}, "type": "array"}}, "type": "object"}, "ListPrivateConnectionsResponse": {"description": "Response message for VmwareEngine.ListPrivateConnections", "id": "ListPrivateConnectionsResponse", "properties": {"nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "privateConnections": {"description": "A list of private connections.", "items": {"$ref": "PrivateConnection"}, "type": "array"}, "unreachable": {"description": "Unreachable resources.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListSubnetsResponse": {"description": "Response message for VmwareEngine.ListSubnets", "id": "ListSubnetsResponse", "properties": {"nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "subnets": {"description": "A list of subnets.", "items": {"$ref": "Subnet"}, "type": "array"}, "unreachable": {"description": "Locations that could not be reached when making an aggregated query using wildcards.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListUpgradesResponse": {"description": "Response message for VmwareEngine.ListUpgrades.", "id": "ListUpgradesResponse", "properties": {"nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "unreachable": {"description": "List of unreachable resources.", "items": {"type": "string"}, "type": "array"}, "upgrades": {"description": "A list of `Upgrades`.", "items": {"$ref": "Upgrade"}, "type": "array"}}, "type": "object"}, "ListVmwareEngineNetworksResponse": {"description": "Response message for VmwareEngine.ListVmwareEngineNetworks", "id": "ListVmwareEngineNetworksResponse", "properties": {"nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "unreachable": {"description": "Unreachable resources.", "items": {"type": "string"}, "type": "array"}, "vmwareEngineNetworks": {"description": "A list of VMware Engine networks.", "items": {"$ref": "VmwareEngineNetwork"}, "type": "array"}}, "type": "object"}, "Location": {"description": "A resource that represents a Google Cloud location.", "id": "Location", "properties": {"displayName": {"description": "The friendly name for this location, typically a nearby city name. For example, \"Tokyo\".", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Cross-service attributes for the location. For example {\"cloud.googleapis.com/region\": \"us-east1\"}", "type": "object"}, "locationId": {"description": "The canonical id for this location. For example: `\"us-east1\"`.", "type": "string"}, "metadata": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "Service-specific metadata. For example the available capacity at the given location.", "type": "object"}, "name": {"description": "Resource name for the location, which may vary between implementations. For example: `\"projects/example-project/locations/us-east1\"`", "type": "string"}}, "type": "object"}, "LocationMetadata": {"description": "VmwareEngine specific metadata for the given google.cloud.location.Location. It is returned as a content of the `google.cloud.location.Location.metadata` field.", "id": "LocationMetadata", "properties": {"capabilities": {"description": "Output only. Capabilities of this location.", "items": {"enum": ["CAPABILITY_UNSPECIFIED", "STRETCHED_CLUSTERS"], "enumDescriptions": ["The default value. This value is used if the capability is omitted or unknown.", "Stretch clusters are supported in this location."], "type": "string"}, "readOnly": true, "type": "array"}}, "type": "object"}, "LoggingServer": {"description": "Logging server to receive vCenter or ESXi logs.", "id": "LoggingServer", "properties": {"createTime": {"description": "Output only. Creation time of this resource.", "format": "google-datetime", "readOnly": true, "type": "string"}, "hostname": {"description": "Required. Fully-qualified domain name (FQDN) or IP Address of the logging server.", "type": "string"}, "name": {"description": "Output only. The resource name of this logging server. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/us-central1-a/privateClouds/my-cloud/loggingServers/my-logging-server`", "readOnly": true, "type": "string"}, "port": {"description": "Required. Port number at which the logging server receives logs.", "format": "int32", "type": "integer"}, "protocol": {"description": "Required. Protocol used by vCenter to send logs to a logging server.", "enum": ["PROTOCOL_UNSPECIFIED", "UDP", "TCP", "TLS", "SSL", "RELP"], "enumDescriptions": ["Unspecified communications protocol. This is the default value.", "UDP", "TCP", "TLS", "SSL", "RELP"], "type": "string"}, "sourceType": {"description": "Required. The type of component that produces logs that will be forwarded to this logging server.", "enum": ["SOURCE_TYPE_UNSPECIFIED", "ESXI", "VCSA"], "enumDescriptions": ["The default value. This value should never be used.", "Logs produced by ESXI hosts", "Logs produced by vCenter server"], "type": "string"}, "uid": {"description": "Output only. System-generated unique identifier for the resource.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. Last update time of this resource.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "ManagementCluster": {"description": "Management cluster configuration.", "id": "ManagementCluster", "properties": {"clusterId": {"description": "Required. The user-provided identifier of the new `Cluster`. The identifier must meet the following requirements: * Only contains 1-63 alphanumeric characters and hyphens * Begins with an alphabetical character * Ends with a non-hyphen character * Not formatted as a UUID * Complies with [RFC 1034](https://datatracker.ietf.org/doc/html/rfc1034) (section 3.5)", "type": "string"}, "nodeTypeConfigs": {"additionalProperties": {"$ref": "NodeTypeConfig"}, "description": "Required. The map of cluster node types in this cluster, where the key is canonical identifier of the node type (corresponds to the `NodeType`).", "type": "object"}, "stretchedClusterConfig": {"$ref": "StretchedClusterConfig", "description": "Optional. Configuration of a stretched cluster. Required for STRETCHED private clouds."}}, "type": "object"}, "ManagementDnsZoneBinding": {"description": "Represents a binding between a network and the management DNS zone. A management DNS zone is the Cloud DNS cross-project binding zone that VMware Engine creates for each private cloud. It contains FQDNs and corresponding IP addresses for the private cloud's ESXi hosts and management VM appliances like vCenter and NSX Manager.", "id": "ManagementDnsZoneBinding", "properties": {"createTime": {"description": "Output only. Creation time of this resource.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "User-provided description for this resource.", "type": "string"}, "name": {"description": "Output only. The resource name of this binding. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/us-central1-a/privateClouds/my-cloud/managementDnsZoneBindings/my-management-dns-zone-binding`", "readOnly": true, "type": "string"}, "state": {"description": "Output only. The state of the resource.", "enum": ["STATE_UNSPECIFIED", "ACTIVE", "CREATING", "UPDATING", "DELETING", "FAILED"], "enumDescriptions": ["The default value. This value should never be used.", "The binding is ready.", "The binding is being created.", "The binding is being updated.", "The binding is being deleted.", "The binding has failed."], "readOnly": true, "type": "string"}, "uid": {"description": "Output only. System-generated unique identifier for the resource.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. Last update time of this resource.", "format": "google-datetime", "readOnly": true, "type": "string"}, "vmwareEngineNetwork": {"description": "Network to bind is a VMware Engine network. Specify the name in the following form for VMware engine network: `projects/{project}/locations/global/vmwareEngineNetworks/{vmware_engine_network_id}`. `{project}` can either be a project number or a project ID.", "type": "string"}, "vpcNetwork": {"description": "Network to bind is a standard consumer VPC. Specify the name in the following form for consumer VPC network: `projects/{project}/global/networks/{network_id}`. `{project}` can either be a project number or a project ID.", "type": "string"}}, "type": "object"}, "NetworkConfig": {"description": "Network configuration in the consumer project with which the peering has to be done.", "id": "NetworkConfig", "properties": {"dnsServerIp": {"description": "Output only. DNS Server IP of the Private Cloud. All DNS queries can be forwarded to this address for name resolution of Private Cloud's management entities like vCenter, NSX-T Manager and ESXi hosts.", "readOnly": true, "type": "string"}, "managementCidr": {"description": "Required. Management CIDR used by VMware management appliances.", "type": "string"}, "managementIpAddressLayoutVersion": {"description": "Output only. The IP address layout version of the management IP address range. Possible versions include: * `managementIpAddressLayoutVersion=1`: Indicates the legacy IP address layout used by some existing private clouds. This is no longer supported for new private clouds as it does not support all features. * `managementIpAddressLayoutVersion=2`: Indicates the latest IP address layout used by all newly created private clouds. This version supports all current features.", "format": "int32", "readOnly": true, "type": "integer"}, "vmwareEngineNetwork": {"description": "Optional. The relative resource name of the VMware Engine network attached to the private cloud. Specify the name in the following form: `projects/{project}/locations/{location}/vmwareEngineNetworks/{vmware_engine_network_id}` where `{project}` can either be a project number or a project ID.", "type": "string"}, "vmwareEngineNetworkCanonical": {"description": "Output only. The canonical name of the VMware Engine network in the form: `projects/{project_number}/locations/{location}/vmwareEngineNetworks/{vmware_engine_network_id}`", "readOnly": true, "type": "string"}}, "type": "object"}, "NetworkPeering": {"description": "Details of a network peering.", "id": "NetworkPeering", "properties": {"createTime": {"description": "Output only. Creation time of this resource.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "Optional. User-provided description for this network peering.", "type": "string"}, "exchangeSubnetRoutes": {"description": "Optional. True if full mesh connectivity is created and managed automatically between peered networks; false otherwise. Currently this field is always true because Google Compute Engine automatically creates and manages subnetwork routes between two VPC networks when peering state is 'ACTIVE'.", "type": "boolean"}, "exportCustomRoutes": {"description": "Optional. True if custom routes are exported to the peered network; false otherwise. The default value is true.", "type": "boolean"}, "exportCustomRoutesWithPublicIp": {"description": "Optional. True if all subnet routes with a public IP address range are exported; false otherwise. The default value is true. IPv4 special-use ranges (https://en.wikipedia.org/wiki/IPv4#Special_addresses) are always exported to peers and are not controlled by this field.", "type": "boolean"}, "importCustomRoutes": {"description": "Optional. True if custom routes are imported from the peered network; false otherwise. The default value is true.", "type": "boolean"}, "importCustomRoutesWithPublicIp": {"description": "Optional. True if all subnet routes with public IP address range are imported; false otherwise. The default value is true. IPv4 special-use ranges (https://en.wikipedia.org/wiki/IPv4#Special_addresses) are always imported to peers and are not controlled by this field.", "type": "boolean"}, "name": {"description": "Output only. Identifier. The resource name of the network peering. NetworkPeering is a global resource and location can only be global. Resource names are scheme-less URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/global/networkPeerings/my-peering`", "readOnly": true, "type": "string"}, "peerMtu": {"description": "Optional. Maximum transmission unit (MTU) in bytes. The default value is `1500`. If a value of `0` is provided for this field, VMware Engine uses the default value instead.", "format": "int32", "type": "integer"}, "peerNetwork": {"description": "Required. The relative resource name of the network to peer with a standard VMware Engine network. The provided network can be a consumer VPC network or another standard VMware Engine network. If the `peer_network_type` is VMWARE_ENGINE_NETWORK, specify the name in the form: `projects/{project}/locations/global/vmwareEngineNetworks/{vmware_engine_network_id}`. Otherwise specify the name in the form: `projects/{project}/global/networks/{network_id}`, where `{project}` can either be a project number or a project ID.", "type": "string"}, "peerNetworkType": {"description": "Required. The type of the network to peer with the VMware Engine network.", "enum": ["PEER_NETWORK_TYPE_UNSPECIFIED", "STANDARD", "VMWARE_ENGINE_NETWORK", "PRIVATE_SERVICES_ACCESS", "NETAPP_CLOUD_VOLUMES", "THIRD_PARTY_SERVICE", "DELL_POWERSCALE", "GOOGLE_CLOUD_NETAPP_VOLUMES", "GOOGLE_CLOUD_FILESTORE_INSTANCES"], "enumDescriptions": ["Unspecified", "Peering connection used for connecting to another VPC network established by the same user. For example, a peering connection to another VPC network in the same project or to an on-premises network.", "Peering connection used for connecting to another VMware Engine network.", "Peering connection used for establishing [private services access](https://cloud.google.com/vpc/docs/private-services-access).", "Peering connection used for connecting to NetApp Cloud Volumes.", "Peering connection used for connecting to third-party services. Most third-party services require manual setup of reverse peering on the VPC network associated with the third-party service.", "Peering connection used for connecting to Dell PowerScale Filers", "Peering connection used for connecting to Google Cloud NetApp Volumes.", "Peering connection used for connecting to Google Cloud Filestore Instances."], "type": "string"}, "state": {"description": "Output only. State of the network peering. This field has a value of 'ACTIVE' when there's a matching configuration in the peer network. New values may be added to this enum when appropriate.", "enum": ["STATE_UNSPECIFIED", "INACTIVE", "ACTIVE", "CREATING", "DELETING"], "enumDescriptions": ["Unspecified network peering state. This is the default value.", "The peering is not active.", "The peering is active.", "The peering is being created.", "The peering is being deleted."], "readOnly": true, "type": "string"}, "stateDetails": {"description": "Output only. Output Only. Details about the current state of the network peering.", "readOnly": true, "type": "string"}, "uid": {"description": "Output only. System-generated unique identifier for the resource.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. Last update time of this resource.", "format": "google-datetime", "readOnly": true, "type": "string"}, "vmwareEngineNetwork": {"description": "Required. The relative resource name of the VMware Engine network. Specify the name in the following form: `projects/{project}/locations/{location}/vmwareEngineNetworks/{vmware_engine_network_id}` where `{project}` can either be a project number or a project ID.", "type": "string"}}, "type": "object"}, "NetworkPolicy": {"description": "Represents a network policy resource. Network policies are regional resources. You can use a network policy to enable or disable internet access and external IP access. Network policies are associated with a VMware Engine network, which might span across regions. For a given region, a network policy applies to all private clouds in the VMware Engine network associated with the policy.", "id": "NetworkPolicy", "properties": {"createTime": {"description": "Output only. Creation time of this resource.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "Optional. User-provided description for this network policy.", "type": "string"}, "edgeServicesCidr": {"description": "Required. IP address range in CIDR notation used to create internet access and external IP access. An RFC 1918 CIDR block, with a \"/26\" prefix, is required. The range cannot overlap with any prefixes either in the consumer VPC network or in use by the private clouds attached to that VPC network.", "type": "string"}, "externalIp": {"$ref": "NetworkService", "description": "Network service that allows External IP addresses to be assigned to VMware workloads. This service can only be enabled when `internet_access` is also enabled."}, "internetAccess": {"$ref": "NetworkService", "description": "Network service that allows VMware workloads to access the internet."}, "name": {"description": "Output only. Identifier. The resource name of this network policy. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/us-central1/networkPolicies/my-network-policy`", "readOnly": true, "type": "string"}, "uid": {"description": "Output only. System-generated unique identifier for the resource.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. Last update time of this resource.", "format": "google-datetime", "readOnly": true, "type": "string"}, "vmwareEngineNetwork": {"description": "Optional. The relative resource name of the VMware Engine network. Specify the name in the following form: `projects/{project}/locations/{location}/vmwareEngineNetworks/{vmware_engine_network_id}` where `{project}` can either be a project number or a project ID.", "type": "string"}, "vmwareEngineNetworkCanonical": {"description": "Output only. The canonical name of the VMware Engine network in the form: `projects/{project_number}/locations/{location}/vmwareEngineNetworks/{vmware_engine_network_id}`", "readOnly": true, "type": "string"}}, "type": "object"}, "NetworkService": {"description": "Represents a network service that is managed by a `NetworkPolicy` resource. A network service provides a way to control an aspect of external access to VMware workloads. For example, whether the VMware workloads in the private clouds governed by a network policy can access or be accessed from the internet.", "id": "NetworkService", "properties": {"enabled": {"description": "True if the service is enabled; false otherwise.", "type": "boolean"}, "state": {"description": "Output only. State of the service. New values may be added to this enum when appropriate.", "enum": ["STATE_UNSPECIFIED", "UNPROVISIONED", "RECONCILING", "ACTIVE"], "enumDescriptions": ["Unspecified service state. This is the default value.", "Service is not provisioned.", "Service is in the process of being provisioned/deprovisioned.", "Service is active."], "readOnly": true, "type": "string"}}, "type": "object"}, "Node": {"description": "Node in a cluster.", "id": "Node", "properties": {"customCoreCount": {"description": "Output only. Customized number of cores", "format": "int64", "readOnly": true, "type": "string"}, "fqdn": {"description": "Output only. Fully qualified domain name of the node.", "readOnly": true, "type": "string"}, "internalIp": {"description": "Output only. Internal IP address of the node.", "readOnly": true, "type": "string"}, "name": {"description": "Output only. The resource name of this node. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: projects/my-project/locations/us-central1-a/privateClouds/my-cloud/clusters/my-cluster/nodes/my-node", "readOnly": true, "type": "string"}, "nodeTypeId": {"description": "Output only. The canonical identifier of the node type (corresponds to the `NodeType`). For example: standard-72.", "readOnly": true, "type": "string"}, "state": {"description": "Output only. The state of the appliance.", "enum": ["STATE_UNSPECIFIED", "ACTIVE", "CREATING", "FAILED", "UPGRADING"], "enumDescriptions": ["The default value. This value should never be used.", "Node is operational and can be used by the user.", "Node is being provisioned.", "Node is in a failed state.", "Node is undergoing maintenance, e.g.: during private cloud upgrade."], "readOnly": true, "type": "string"}, "version": {"description": "Output only. The version number of the VMware ESXi management component in this cluster.", "readOnly": true, "type": "string"}}, "type": "object"}, "NodeType": {"description": "Describes node type.", "id": "NodeType", "properties": {"availableCustomCoreCounts": {"description": "Output only. List of possible values of custom core count.", "items": {"format": "int32", "type": "integer"}, "readOnly": true, "type": "array"}, "capabilities": {"description": "Output only. Capabilities of this node type.", "items": {"enum": ["CAPABILITY_UNSPECIFIED", "STRETCHED_CLUSTERS"], "enumDescriptions": ["The default value. This value is used if the capability is omitted or unknown.", "This node type supports stretch clusters."], "type": "string"}, "readOnly": true, "type": "array"}, "diskSizeGb": {"description": "Output only. The amount of storage available, defined in GB.", "format": "int32", "readOnly": true, "type": "integer"}, "displayName": {"description": "Output only. The friendly name for this node type. For example: ve1-standard-72", "readOnly": true, "type": "string"}, "families": {"description": "Output only. Families of the node type. For node types to be in the same cluster they must share at least one element in the `families`.", "items": {"type": "string"}, "readOnly": true, "type": "array"}, "kind": {"description": "Output only. The type of the resource.", "enum": ["KIND_UNSPECIFIED", "STANDARD", "STORAGE_ONLY"], "enumDescriptions": ["The default value. This value should never be used.", "Standard HCI node.", "Storage only Node."], "readOnly": true, "type": "string"}, "memoryGb": {"description": "Output only. The amount of physical memory available, defined in GB.", "format": "int32", "readOnly": true, "type": "integer"}, "name": {"description": "Output only. The resource name of this node type. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-proj/locations/us-central1-a/nodeTypes/standard-72`", "readOnly": true, "type": "string"}, "nodeTypeId": {"description": "Output only. The canonical identifier of the node type (corresponds to the `NodeType`). For example: standard-72.", "readOnly": true, "type": "string"}, "totalCoreCount": {"description": "Output only. The total number of CPU cores in a single node.", "format": "int32", "readOnly": true, "type": "integer"}, "virtualCpuCount": {"description": "Output only. The total number of virtual CPUs in a single node.", "format": "int32", "readOnly": true, "type": "integer"}}, "type": "object"}, "NodeTypeConfig": {"description": "Information about the type and number of nodes associated with the cluster.", "id": "NodeTypeConfig", "properties": {"customCoreCount": {"description": "Optional. Customized number of cores available to each node of the type. This number must always be one of `nodeType.availableCustomCoreCounts`. If zero is provided max value from `nodeType.availableCustomCoreCounts` will be used.", "format": "int32", "type": "integer"}, "nodeCount": {"description": "Required. The number of nodes of this type in the cluster", "format": "int32", "type": "integer"}}, "type": "object"}, "Nsx": {"description": "Details about a NSX Manager appliance.", "id": "Nsx", "properties": {"fqdn": {"description": "Fully qualified domain name of the appliance.", "type": "string"}, "internalIp": {"description": "Internal IP address of the appliance.", "type": "string"}, "state": {"description": "Output only. The state of the appliance.", "enum": ["STATE_UNSPECIFIED", "ACTIVE", "CREATING"], "enumDescriptions": ["Unspecified appliance state. This is the default value.", "The appliance is operational and can be used.", "The appliance is being deployed."], "readOnly": true, "type": "string"}, "version": {"description": "Version of the appliance.", "type": "string"}}, "type": "object"}, "Operation": {"description": "This resource represents a long-running operation that is the result of a network API call.", "id": "Operation", "properties": {"done": {"description": "If the value is `false`, it means the operation is still in progress. If `true`, the operation is completed, and either `error` or `response` is available.", "type": "boolean"}, "error": {"$ref": "Status", "description": "The error result of the operation in case of failure or cancellation."}, "metadata": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata. Any method that returns a long-running operation should document the metadata type, if any.", "type": "object"}, "name": {"description": "The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the `name` should be a resource name ending with `operations/{unique_id}`.", "type": "string"}, "response": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "The normal, successful response of the operation. If the original method returns no data on success, such as `Delete`, the response is `google.protobuf.Empty`. If the original method is standard `Get`/`Create`/`Update`, the response should be the resource. For other methods, the response should have the type `XxxResponse`, where `Xxx` is the original method name. For example, if the original method name is `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.", "type": "object"}}, "type": "object"}, "OperationMetadata": {"description": "Represents the metadata of the long-running operation.", "id": "OperationMetadata", "properties": {"apiVersion": {"description": "Output only. API version used to start the operation.", "readOnly": true, "type": "string"}, "createTime": {"description": "Output only. The time the operation was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "endTime": {"description": "Output only. The time the operation finished running.", "format": "google-datetime", "readOnly": true, "type": "string"}, "requestedCancellation": {"description": "Output only. True if the user has requested cancellation of the operation; false otherwise. Operations that have successfully been cancelled have Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.", "readOnly": true, "type": "boolean"}, "statusMessage": {"description": "Output only. Human-readable status of the operation, if any.", "readOnly": true, "type": "string"}, "target": {"description": "Output only. Server-defined resource path for the target of the operation.", "readOnly": true, "type": "string"}, "verb": {"description": "Output only. Name of the verb executed by the operation.", "readOnly": true, "type": "string"}}, "type": "object"}, "PeeringRoute": {"description": "Exchanged network peering route.", "id": "PeeringRoute", "properties": {"destRange": {"description": "Output only. Destination range of the peering route in CIDR notation.", "readOnly": true, "type": "string"}, "direction": {"description": "Output only. Direction of the routes exchanged with the peer network, from the VMware Engine network perspective: * Routes of direction `INCOMING` are imported from the peer network. * Routes of direction `OUTGOING` are exported from the intranet VPC network of the VMware Engine network.", "enum": ["DIRECTION_UNSPECIFIED", "INCOMING", "OUTGOING"], "enumDescriptions": ["Unspecified exchanged routes direction. This is default.", "Routes imported from the peer network.", "Routes exported to the peer network."], "readOnly": true, "type": "string"}, "imported": {"description": "Output only. True if the peering route has been imported from a peered VPC network; false otherwise. The import happens if the field `NetworkPeering.importCustomRoutes` is true for this network, `NetworkPeering.exportCustomRoutes` is true for the peer VPC network, and the import does not result in a route conflict.", "readOnly": true, "type": "boolean"}, "nextHopRegion": {"description": "Output only. Region containing the next hop of the peering route. This field only applies to dynamic routes in the peer VPC network.", "readOnly": true, "type": "string"}, "priority": {"description": "Output only. The priority of the peering route.", "format": "int64", "readOnly": true, "type": "string"}, "type": {"description": "Output only. Type of the route in the peer VPC network.", "enum": ["TYPE_UNSPECIFIED", "DYNAMIC_PEERING_ROUTE", "STATIC_PEERING_ROUTE", "SUBNET_PEERING_ROUTE"], "enumDescriptions": ["Unspecified peering route type. This is the default value.", "Dynamic routes in the peer network.", "Static routes in the peer network.", "Created, updated, and removed automatically by Google Cloud when subnets are created, modified, or deleted in the peer network."], "readOnly": true, "type": "string"}}, "type": "object"}, "Policy": {"description": "An Identity and Access Management (IAM) policy, which specifies access controls for Google Cloud resources. A `Policy` is a collection of `bindings`. A `binding` binds one or more `members`, or principals, to a single `role`. Principals can be user accounts, service accounts, Google groups, and domains (such as G Suite). A `role` is a named list of permissions; each `role` can be an IAM predefined role or a user-created custom role. For some types of Google Cloud resources, a `binding` can also specify a `condition`, which is a logical expression that allows access to a resource only if the expression evaluates to `true`. A condition can add constraints based on attributes of the request, the resource, or both. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies). **JSON example:** ``` { \"bindings\": [ { \"role\": \"roles/resourcemanager.organizationAdmin\", \"members\": [ \"user:<EMAIL>\", \"group:<EMAIL>\", \"domain:google.com\", \"serviceAccount:<EMAIL>\" ] }, { \"role\": \"roles/resourcemanager.organizationViewer\", \"members\": [ \"user:<EMAIL>\" ], \"condition\": { \"title\": \"expirable access\", \"description\": \"Does not grant access after Sep 2020\", \"expression\": \"request.time < timestamp('2020-10-01T00:00:00.000Z')\", } } ], \"etag\": \"BwWWja0YfJA=\", \"version\": 3 } ``` **YAML example:** ``` bindings: - members: - user:<EMAIL> - group:<EMAIL> - domain:google.com - serviceAccount:<EMAIL> role: roles/resourcemanager.organizationAdmin - members: - user:<EMAIL> role: roles/resourcemanager.organizationViewer condition: title: expirable access description: Does not grant access after Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z') etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features, see the [IAM documentation](https://cloud.google.com/iam/docs/).", "id": "Policy", "properties": {"auditConfigs": {"description": "Specifies cloud audit logging configuration for this policy.", "items": {"$ref": "AuditConfig"}, "type": "array"}, "bindings": {"description": "Associates a list of `members`, or principals, with a `role`. Optionally, may specify a `condition` that determines how and when the `bindings` are applied. Each of the `bindings` must contain at least one principal. The `bindings` in a `Policy` can refer to up to 1,500 principals; up to 250 of these principals can be Google groups. Each occurrence of a principal counts towards these limits. For example, if the `bindings` grant 50 different roles to `user:<EMAIL>`, and not to any other principal, then you can add another 1,450 principals to the `bindings` in the `Policy`.", "items": {"$ref": "Binding"}, "type": "array"}, "etag": {"description": "`etag` is used for optimistic concurrency control as a way to help prevent simultaneous updates of a policy from overwriting each other. It is strongly suggested that systems make use of the `etag` in the read-modify-write cycle to perform policy updates in order to avoid race conditions: An `etag` is returned in the response to `getIamPolicy`, and systems are expected to put that etag in the request to `setIamPolicy` to ensure that their change will be applied to the same version of the policy. **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost.", "format": "byte", "type": "string"}, "version": {"description": "Specifies the format of the policy. Valid values are `0`, `1`, and `3`. Requests that specify an invalid value are rejected. Any operation that affects conditional role bindings must specify version `3`. This requirement applies to the following operations: * Getting a policy that includes a conditional role binding * Adding a conditional role binding to a policy * Changing a conditional role binding in a policy * Removing any role binding, with or without a condition, from a policy that includes conditions **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost. If a policy does not include any conditions, operations on that policy may specify any valid version or leave the field unset. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "type": "integer"}}, "type": "object"}, "Principal": {"description": "Users/Service accounts which have access for DNS binding on the intranet VPC corresponding to the consumer project.", "id": "Principal", "properties": {"serviceAccount": {"description": "The service account which needs to be granted the permission.", "type": "string"}, "user": {"description": "The user who needs to be granted permission.", "type": "string"}}, "type": "object"}, "PrivateCloud": {"description": "Represents a private cloud resource. Private clouds of type `STANDARD` and `TIME_LIMITED` are zonal resources, `STRETCHED` private clouds are regional.", "id": "PrivateCloud", "properties": {"createTime": {"description": "Output only. Creation time of this resource.", "format": "google-datetime", "readOnly": true, "type": "string"}, "deleteTime": {"description": "Output only. Time when the resource was scheduled for deletion.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "User-provided description for this private cloud.", "type": "string"}, "expireTime": {"description": "Output only. Time when the resource will be irreversibly deleted.", "format": "google-datetime", "readOnly": true, "type": "string"}, "hcx": {"$ref": "Hcx", "description": "Output only. HCX appliance.", "readOnly": true}, "managementCluster": {"$ref": "ManagementCluster", "description": "Required. Input only. The management cluster for this private cloud. This field is required during creation of the private cloud to provide details for the default cluster. The following fields can't be changed after private cloud creation: `ManagementCluster.clusterId`, `ManagementCluster.nodeTypeId`."}, "name": {"description": "Output only. Identifier. The resource name of this private cloud. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/us-central1-a/privateClouds/my-cloud`", "readOnly": true, "type": "string"}, "networkConfig": {"$ref": "NetworkConfig", "description": "Required. Network configuration of the private cloud."}, "nsx": {"$ref": "Nsx", "description": "Output only. NSX appliance.", "readOnly": true}, "state": {"description": "Output only. State of the resource. New values may be added to this enum when appropriate.", "enum": ["STATE_UNSPECIFIED", "ACTIVE", "CREATING", "UPDATING", "FAILED", "DELETED", "PURGING"], "enumDescriptions": ["The default value. This value should never be used.", "The private cloud is ready.", "The private cloud is being created.", "The private cloud is being updated.", "The private cloud is in failed state.", "The private cloud is scheduled for deletion. The deletion process can be cancelled by using the corresponding undelete method.", "The private cloud is irreversibly deleted and is being removed from the system."], "readOnly": true, "type": "string"}, "type": {"description": "Optional. Type of the private cloud. Defaults to STANDARD.", "enum": ["STANDARD", "TIME_LIMITED", "STRETCHED"], "enumDescriptions": ["Standard private is a zonal resource, with 3+ nodes. Default type.", "Time limited private cloud is a zonal resource, can have only 1 node and has limited life span. Will be deleted after defined period of time, can be converted into standard private cloud by expanding it up to 3 or more nodes.", "Stretched private cloud is a regional resource with redundancy, with a minimum of 6 nodes, nodes count has to be even."], "type": "string"}, "uid": {"description": "Output only. System-generated unique identifier for the resource.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. Last update time of this resource.", "format": "google-datetime", "readOnly": true, "type": "string"}, "vcenter": {"$ref": "Vcenter", "description": "Output only. Vcenter appliance.", "readOnly": true}}, "type": "object"}, "PrivateConnection": {"description": "Private connection resource that provides connectivity for VMware Engine private clouds.", "id": "PrivateConnection", "properties": {"createTime": {"description": "Output only. Creation time of this resource.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "Optional. User-provided description for this private connection.", "type": "string"}, "name": {"description": "Output only. The resource name of the private connection. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/us-central1/privateConnections/my-connection`", "readOnly": true, "type": "string"}, "peeringId": {"description": "Output only. VPC network peering id between given network VPC and VMwareEngineNetwork.", "readOnly": true, "type": "string"}, "peeringState": {"description": "Output only. Peering state between service network and VMware Engine network.", "enum": ["PEERING_STATE_UNSPECIFIED", "PEERING_ACTIVE", "PEERING_INACTIVE"], "enumDescriptions": ["The default value. This value is used if the peering state is omitted or unknown.", "The peering is in active state.", "The peering is in inactive state."], "readOnly": true, "type": "string"}, "routingMode": {"description": "Optional. Routing Mode. Default value is set to GLOBAL. For type = PRIVATE_SERVICE_ACCESS, this field can be set to GLOBAL or REGIONAL, for other types only GLOBAL is supported.", "enum": ["ROUTING_MODE_UNSPECIFIED", "GLOBAL", "REGIONAL"], "enumDescriptions": ["The default value. This value should never be used.", "Global Routing Mode", "Regional Routing Mode"], "type": "string"}, "serviceNetwork": {"description": "Required. Service network to create private connection. Specify the name in the following form: `projects/{project}/global/networks/{network_id}` For type = PRIVATE_SERVICE_ACCESS, this field represents servicenetworking VPC, e.g. projects/project-tp/global/networks/servicenetworking. For type = NETAPP_CLOUD_VOLUME, this field represents NetApp service VPC, e.g. projects/project-tp/global/networks/netapp-tenant-vpc. For type = DELL_POWERSCALE, this field represent Dell service VPC, e.g. projects/project-tp/global/networks/dell-tenant-vpc. For type= THIRD_PARTY_SERVICE, this field could represent a consumer VPC or any other producer VPC to which the VMware Engine Network needs to be connected, e.g. projects/project/global/networks/vpc.", "type": "string"}, "state": {"description": "Output only. State of the private connection.", "enum": ["STATE_UNSPECIFIED", "CREATING", "ACTIVE", "UPDATING", "DELETING", "UNPROVISIONED", "FAILED"], "enumDescriptions": ["The default value. This value is used if the state is omitted.", "The private connection is being created.", "The private connection is ready.", "The private connection is being updated.", "The private connection is being deleted.", "The private connection is not provisioned, since no private cloud is present for which this private connection is needed.", "The private connection is in failed state."], "readOnly": true, "type": "string"}, "type": {"description": "Required. Private connection type.", "enum": ["TYPE_UNSPECIFIED", "PRIVATE_SERVICE_ACCESS", "NETAPP_CLOUD_VOLUMES", "DELL_POWERSCALE", "THIRD_PARTY_SERVICE"], "enumDescriptions": ["The default value. This value should never be used.", "Connection used for establishing [private services access](https://cloud.google.com/vpc/docs/private-services-access).", "Connection used for connecting to NetApp Cloud Volumes.", "Connection used for connecting to Dell PowerScale.", "Connection used for connecting to third-party services."], "type": "string"}, "uid": {"description": "Output only. System-generated unique identifier for the resource.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. Last update time of this resource.", "format": "google-datetime", "readOnly": true, "type": "string"}, "vmwareEngineNetwork": {"description": "Required. The relative resource name of Legacy VMware Engine network. Specify the name in the following form: `projects/{project}/locations/{location}/vmwareEngineNetworks/{vmware_engine_network_id}` where `{project}`, `{location}` will be same as specified in private connection resource name and `{vmware_engine_network_id}` will be in the form of `{location}`-default e.g. projects/project/locations/us-central1/vmwareEngineNetworks/us-central1-default.", "type": "string"}, "vmwareEngineNetworkCanonical": {"description": "Output only. The canonical name of the VMware Engine network in the form: `projects/{project_number}/locations/{location}/vmwareEngineNetworks/{vmware_engine_network_id}`", "readOnly": true, "type": "string"}}, "type": "object"}, "RepairManagementDnsZoneBindingRequest": {"description": "Request message for VmwareEngine.RepairManagementDnsZoneBindings", "id": "RepairManagementDnsZoneBindingRequest", "properties": {"requestId": {"description": "Optional. A request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server guarantees that a request doesn't result in creation of duplicate commitments for at least 60 minutes. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if the original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (********-0000-0000-0000-********0000).", "type": "string"}}, "type": "object"}, "ResetNsxCredentialsRequest": {"description": "Request message for VmwareEngine.ResetNsxCredentials", "id": "ResetNsxCredentialsRequest", "properties": {"requestId": {"description": "Optional. A request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server guarantees that a request doesn't result in creation of duplicate commitments for at least 60 minutes. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (********-0000-0000-0000-********0000).", "type": "string"}}, "type": "object"}, "ResetVcenterCredentialsRequest": {"description": "Request message for VmwareEngine.ResetVcenterCredentials", "id": "ResetVcenterCredentialsRequest", "properties": {"requestId": {"description": "Optional. A request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server guarantees that a request doesn't result in creation of duplicate commitments for at least 60 minutes. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (********-0000-0000-0000-********0000).", "type": "string"}, "username": {"description": "Optional. The username of the user to be to reset the credentials. The default value of this <NAME_EMAIL>. The provided value should be one of the following: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>.", "type": "string"}}, "type": "object"}, "RevokeDnsBindPermissionRequest": {"description": "Request message for VmwareEngine.RevokeDnsBindPermission", "id": "RevokeDnsBindPermissionRequest", "properties": {"principal": {"$ref": "Principal", "description": "Required. The consumer provided user/service account which needs to be granted permission to bind with the intranet VPC corresponding to the consumer project."}, "requestId": {"description": "Optional. A request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server guarantees that a request doesn't result in creation of duplicate commitments for at least 60 minutes. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (********-0000-0000-0000-********0000).", "type": "string"}}, "type": "object"}, "Schedule": {"description": "Schedule for the upgrade.", "id": "Schedule", "properties": {"constraints": {"$ref": "Constraints", "description": "Output only. Output Only. Constraints applied to the schedule. These constraints should be applicable at the time of any rescheduling.", "readOnly": true}, "editWindow": {"$ref": "Interval", "description": "Output only. Output Only. The schedule is open for edits during this time interval or window.", "readOnly": true}, "lastEditor": {"description": "Output only. Output Only. Indicates who most recently edited the upgrade schedule. The value is updated whenever the upgrade is rescheduled.", "enum": ["EDITOR_UNSPECIFIED", "SYSTEM", "USER"], "enumDescriptions": ["The default value. This value should never be used.", "The upgrade is scheduled by the System or internal service.", "The upgrade is scheduled by the end user."], "readOnly": true, "type": "string"}, "startTime": {"description": "Required. The scheduled start time for the upgrade.", "format": "google-datetime", "type": "string"}, "weeklyWindows": {"description": "Required. Weekly time windows for upgrade activities. The server performs upgrade activities during these time windows to minimize disruptions.", "items": {"$ref": "TimeWindow"}, "type": "array"}}, "type": "object"}, "SetIamPolicyRequest": {"description": "Request message for `SetIamPolicy` method.", "id": "SetIamPolicyRequest", "properties": {"policy": {"$ref": "Policy", "description": "REQUIRED: The complete policy to be applied to the `resource`. The size of the policy is limited to a few 10s of KB. An empty policy is a valid policy but certain Google Cloud services (such as Projects) might reject them."}, "updateMask": {"description": "OPTIONAL: A FieldMask specifying which fields of the policy to modify. Only the fields in the mask will be modified. If no mask is provided, the following default mask is used: `paths: \"bindings, etag\"`", "format": "google-fieldmask", "type": "string"}}, "type": "object"}, "Status": {"description": "The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).", "id": "Status", "properties": {"code": {"description": "The status code, which should be an enum value of google.rpc.Code.", "format": "int32", "type": "integer"}, "details": {"description": "A list of messages that carry the error details. There is a common set of message types for APIs to use.", "items": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "type": "object"}, "type": "array"}, "message": {"description": "A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.", "type": "string"}}, "type": "object"}, "StretchedClusterConfig": {"description": "Configuration of a stretched cluster.", "id": "StretchedClusterConfig", "properties": {"preferredLocation": {"description": "Required. Zone that will remain operational when connection between the two zones is lost. Specify the resource name of a zone that belongs to the region of the private cloud. For example: `projects/{project}/locations/europe-west3-a` where `{project}` can either be a project number or a project ID.", "type": "string"}, "secondaryLocation": {"description": "Required. Additional zone for a higher level of availability and load balancing. Specify the resource name of a zone that belongs to the region of the private cloud. For example: `projects/{project}/locations/europe-west3-b` where `{project}` can either be a project number or a project ID.", "type": "string"}}, "type": "object"}, "Subnet": {"description": "Subnet in a private cloud. Either `management` subnets (such as vMotion) that are read-only, or `userDefined`, which can also be updated.", "id": "Subnet", "properties": {"gatewayIp": {"description": "The IP address of the gateway of this subnet. Must fall within the IP prefix defined above.", "type": "string"}, "ipCidrRange": {"description": "The IP address range of the subnet in CIDR format '10.0.0.0/24'.", "type": "string"}, "name": {"description": "Output only. Identifier. The resource name of this subnet. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/us-central1-a/privateClouds/my-cloud/subnets/my-subnet`", "readOnly": true, "type": "string"}, "state": {"description": "Output only. The state of the resource.", "enum": ["STATE_UNSPECIFIED", "ACTIVE", "CREATING", "UPDATING", "DELETING", "RECONCILING", "FAILED"], "enumDescriptions": ["The default value. This value should never be used.", "The subnet is ready.", "The subnet is being created.", "The subnet is being updated.", "The subnet is being deleted.", "Changes requested in the last operation are being propagated.", "Last operation on the subnet did not succeed. Subnet's payload is reverted back to its most recent working state."], "readOnly": true, "type": "string"}, "type": {"description": "Output only. The type of the subnet. For example \"management\" or \"userDefined\".", "readOnly": true, "type": "string"}, "vlanId": {"description": "Output only. VLAN ID of the VLAN on which the subnet is configured", "format": "int32", "readOnly": true, "type": "integer"}}, "type": "object"}, "TestIamPermissionsRequest": {"description": "Request message for `TestIamPermissions` method.", "id": "TestIamPermissionsRequest", "properties": {"permissions": {"description": "The set of permissions to check for the `resource`. Permissions with wildcards (such as `*` or `storage.*`) are not allowed. For more information see [IAM Overview](https://cloud.google.com/iam/docs/overview#permissions).", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "TestIamPermissionsResponse": {"description": "Response message for `TestIamPermissions` method.", "id": "TestIamPermissionsResponse", "properties": {"permissions": {"description": "A subset of `TestPermissionsRequest.permissions` that the caller is allowed.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "Thresholds": {"description": "Thresholds define the utilization of resources triggering scale-out and scale-in operations.", "id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "properties": {"scaleIn": {"description": "Required. The utilization triggering the scale-in operation in percent.", "format": "int32", "type": "integer"}, "scaleOut": {"description": "Required. The utilization triggering the scale-out operation in percent.", "format": "int32", "type": "integer"}}, "type": "object"}, "TimeOfDay": {"description": "Represents a time of day. The date and time zone are either not significant or are specified elsewhere. An API may choose to allow leap seconds. Related types are google.type.Date and `google.protobuf.Timestamp`.", "id": "TimeOfDay", "properties": {"hours": {"description": "Hours of a day in 24 hour format. Must be greater than or equal to 0 and typically must be less than or equal to 23. An API may choose to allow the value \"24:00:00\" for scenarios like business closing time.", "format": "int32", "type": "integer"}, "minutes": {"description": "Minutes of an hour. Must be greater than or equal to 0 and less than or equal to 59.", "format": "int32", "type": "integer"}, "nanos": {"description": "Fractions of seconds, in nanoseconds. Must be greater than or equal to 0 and less than or equal to 999,999,999.", "format": "int32", "type": "integer"}, "seconds": {"description": "Seconds of a minute. Must be greater than or equal to 0 and typically must be less than or equal to 59. An API may allow the value 60 if it allows leap-seconds.", "format": "int32", "type": "integer"}}, "type": "object"}, "TimeWindow": {"description": "Represents the time window to perform upgrade activities.", "id": "TimeWindow", "properties": {"dayOfWeek": {"description": "Required. Day of the week for this window.", "enum": ["DAY_OF_WEEK_UNSPECIFIED", "MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY", "SATURDAY", "SUNDAY"], "enumDescriptions": ["The day of the week is unspecified.", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"], "type": "string"}, "duration": {"description": "Required. The duration of the window. The max allowed duration for any window is 24 hours.", "format": "google-duration", "type": "string"}, "startTime": {"$ref": "TimeOfDay", "description": "Required. Time in UTC when the window starts."}}, "type": "object"}, "UndeletePrivateCloudRequest": {"description": "Request message for VmwareEngine.UndeletePrivateCloud", "id": "UndeletePrivateCloudRequest", "properties": {"requestId": {"description": "Optional. The request ID must be a valid UUID with the exception that zero UUID is not supported (********-0000-0000-0000-********0000).", "type": "string"}}, "type": "object"}, "Upgrade": {"description": "Describes Private cloud Upgrade.", "id": "Upgrade", "properties": {"componentUpgrades": {"description": "Output only. Output Only. The list of component upgrades.", "items": {"$ref": "VmwareUpgradeComponent"}, "readOnly": true, "type": "array"}, "createTime": {"description": "Output only. Output Only. Creation time of this resource.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "Output only. Output Only. The description of the upgrade. This is used to provide additional information about the private cloud upgrade, such as the upgrade's purpose, the changes included in the upgrade, or any other relevant information about the upgrade.", "readOnly": true, "type": "string"}, "endTime": {"description": "Output only. Output Only. End time of the upgrade.", "format": "google-datetime", "readOnly": true, "type": "string"}, "estimatedDuration": {"description": "Output only. Output Only. The estimated total duration of the upgrade. This information can be used to plan or schedule upgrades to minimize disruptions. Please note that the estimated duration is only an estimate. The actual upgrade duration may vary.", "format": "google-duration", "readOnly": true, "type": "string"}, "etag": {"description": "The etag for the upgrade resource. If this is provided on update, it must match the server's etag.", "type": "string"}, "name": {"description": "Output only. Identifier. The resource name of the private cloud `Upgrade`. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/us-west1-a/privateClouds/my-cloud/upgrades/my-upgrade`", "readOnly": true, "type": "string"}, "schedule": {"$ref": "Schedule", "description": "Schedule details for the upgrade."}, "startVersion": {"description": "Output only. Output Only. The start version", "readOnly": true, "type": "string"}, "state": {"description": "Output only. The current state of the upgrade.", "enum": ["STATE_UNSPECIFIED", "SCHEDULED", "ONGOING", "SUCCEEDED", "PAUSED", "FAILED", "CANCELLING", "CANCELLED", "RESCHEDULING"], "enumDescriptions": ["The default value. This value should never be used.", "The upgrade is scheduled but not started yet.", "The upgrade is currently in progress and has not completed yet.", "The upgrade completed successfully.", "The upgrade is currently paused.", "The upgrade failed.", "The upgrade is in process of being canceled.", "The upgrade is canceled.", "The upgrade is in process of being rescheduled."], "readOnly": true, "type": "string"}, "targetVersion": {"description": "Output only. Output Only. The target version", "readOnly": true, "type": "string"}, "type": {"description": "Output only. Output Only. The type of upgrade.", "enum": ["TYPE_UNSPECIFIED", "VSPHERE_UPGRADE", "VSPHERE_PATCH", "WORKAROUND", "FIRMWARE_UPGRADE", "SWITCH_UPGRADE", "OTHER", "INFRASTRUCTURE_UPGRADE"], "enumDescriptions": ["The default value. This value should never be used.", "Upgrade of vmware components when a major version is available. 7.0u2 -> 7.0u3.", "Patching of vmware components when a minor version is available. 7.0u2c -> 7.0u2d.", "Workarounds are hotfixes for vulnerabilities or issues applied to mitigate the known vulnerability or issue until a patch or update is released. The description of the upgrade will have more details.", "Firmware upgrade for VMware product used in the private cloud.", "Switch upgrade.", "The upgrade type that doesn't fall into any other category.", "Infrastructure upgrade in BM node maintenance."], "readOnly": true, "type": "string"}, "uid": {"description": "Output only. System-generated unique identifier for the resource.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. Output Only. Last update time of this resource.", "format": "google-datetime", "readOnly": true, "type": "string"}, "version": {"description": "Output only. ", "readOnly": true, "type": "string"}}, "type": "object"}, "Vcenter": {"description": "Details about a vCenter Server management appliance.", "id": "Vcenter", "properties": {"fqdn": {"description": "Fully qualified domain name of the appliance.", "type": "string"}, "internalIp": {"description": "Internal IP address of the appliance.", "type": "string"}, "state": {"description": "Output only. The state of the appliance.", "enum": ["STATE_UNSPECIFIED", "ACTIVE", "CREATING"], "enumDescriptions": ["Unspecified appliance state. This is the default value.", "The appliance is operational and can be used.", "The appliance is being deployed."], "readOnly": true, "type": "string"}, "version": {"description": "Version of the appliance.", "type": "string"}}, "type": "object"}, "VmwareEngineNetwork": {"description": "VMware Engine network resource that provides connectivity for VMware Engine private clouds.", "id": "VmwareEngineNetwork", "properties": {"createTime": {"description": "Output only. Creation time of this resource.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "User-provided description for this VMware Engine network.", "type": "string"}, "etag": {"description": "Checksum that may be sent on update and delete requests to ensure that the user-provided value is up to date before the server processes a request. The server computes checksums based on the value of other fields in the request.", "type": "string"}, "name": {"description": "Output only. Identifier. The resource name of the VMware Engine network. Resource names are schemeless URIs that follow the conventions in https://cloud.google.com/apis/design/resource_names. For example: `projects/my-project/locations/global/vmwareEngineNetworks/my-network`", "readOnly": true, "type": "string"}, "state": {"description": "Output only. State of the VMware Engine network.", "enum": ["STATE_UNSPECIFIED", "CREATING", "ACTIVE", "UPDATING", "DELETING"], "enumDescriptions": ["The default value. This value is used if the state is omitted.", "The VMware Engine network is being created.", "The VMware Engine network is ready.", "The VMware Engine network is being updated.", "The VMware Engine network is being deleted."], "readOnly": true, "type": "string"}, "type": {"description": "Required. VMware Engine network type.", "enum": ["TYPE_UNSPECIFIED", "LEGACY", "STANDARD"], "enumDescriptions": ["The default value. This value should never be used.", "Network type used by private clouds created in projects without a network of type `STANDARD`. This network type is no longer used for new VMware Engine private cloud deployments.", "Standard network type used for private cloud connectivity."], "type": "string"}, "uid": {"description": "Output only. System-generated unique identifier for the resource.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. Last update time of this resource.", "format": "google-datetime", "readOnly": true, "type": "string"}, "vpcNetworks": {"description": "Output only. VMware Engine service VPC networks that provide connectivity from a private cloud to customer projects, the internet, and other Google Cloud services.", "items": {"$ref": "VpcNetwork"}, "readOnly": true, "type": "array"}}, "type": "object"}, "VmwareUpgradeComponent": {"description": "Per component upgrade resource", "id": "VmwareUpgradeComponent", "properties": {"componentType": {"description": "Output only. Type of component", "enum": ["VMWARE_COMPONENT_TYPE_UNSPECIFIED", "VCENTER", "ESXI", "NSXT_UC", "NSXT_EDGE", "NSXT_MGR", "HCX", "VSAN", "DVS", "NAMESERVER_VM", "KMS_VM", "WITNESS_VM", "NSXT", "CLUSTER"], "enumDescriptions": ["The default value. This value should never be used.", "vcenter", "esxi nodes + transport nodes", "nsxt upgrade coordinator", "nsxt edges cluster", "nsxt managers/management plane", "hcx", "VSAN cluster", "DVS switch", "Nameserver VMs", "KMS VM used for vsan encryption", "witness VM in case of stretch PC", "nsxt", "Cluster is used in case of BM"], "readOnly": true, "type": "string"}, "state": {"description": "Output only. Component's upgrade state.", "enum": ["STATE_UNSPECIFIED", "RUNNING", "PAUSED", "SUCCEEDED", "FAILED", "NOT_STARTED", "NOT_APPLICABLE"], "enumDescriptions": ["The default value. This value should never be used.", "Component's upgrade is in progress", "The component's upgrade is paused. Will be resumed when upgrade job is resumed", "The component's upgrade is successfully completed", "The component's upgrade has failed. This will move to resume if upgrade is resumed or stay as is", "Component's upgrade has not started yet", "Component's upgrade is not applicable in this upgrade. It will be skipped."], "readOnly": true, "type": "string"}}, "type": "object"}, "VpcNetwork": {"description": "Represents a VMware Engine VPC network that is managed by a VMware Engine network resource.", "id": "VpcNetwork", "properties": {"network": {"description": "Output only. The relative resource name of the service VPC network this VMware Engine network is attached to. For example: `projects/123123/global/networks/my-network`", "readOnly": true, "type": "string"}, "type": {"description": "Output only. Type of VPC network (INTRANET, INTERNET, or GOOGLE_CLOUD)", "enum": ["TYPE_UNSPECIFIED", "INTRANET", "INTERNET", "GOOGLE_CLOUD"], "enumDescriptions": ["The default value. This value should never be used.", "VPC network that will be peered with a consumer VPC network or the intranet VPC of another VMware Engine network. Access a private cloud through Compute Engine VMs on a peered VPC network or an on-premises resource connected to a peered consumer VPC network.", "VPC network used for internet access to and from a private cloud.", "VPC network used for access to Google Cloud services like Cloud Storage."], "readOnly": true, "type": "string"}}, "type": "object"}, "WeeklyTimeInterval": {"description": "Represents a time interval, spanning across days of the week. Until local timezones are supported, this interval is in UTC.", "id": "WeeklyTimeInterval", "properties": {"endDay": {"description": "Output only. The day on which the interval ends. Can be same as start day.", "enum": ["DAY_OF_WEEK_UNSPECIFIED", "MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY", "SATURDAY", "SUNDAY"], "enumDescriptions": ["The day of the week is unspecified.", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"], "readOnly": true, "type": "string"}, "endTime": {"$ref": "TimeOfDay", "description": "Output only. The time on the end day at which the interval ends.", "readOnly": true}, "startDay": {"description": "Output only. The day on which the interval starts.", "enum": ["DAY_OF_WEEK_UNSPECIFIED", "MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY", "SATURDAY", "SUNDAY"], "enumDescriptions": ["The day of the week is unspecified.", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"], "readOnly": true, "type": "string"}, "startTime": {"$ref": "TimeOfDay", "description": "Output only. The time on the start day at which the interval starts.", "readOnly": true}}, "type": "object"}}, "servicePath": "", "title": "VMware Engine API", "version": "v1", "version_module": true}