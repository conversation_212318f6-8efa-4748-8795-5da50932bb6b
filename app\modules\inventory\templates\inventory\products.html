{% extends "base.html" %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col-md-3">
            <a href="{{ url_for('inventory.add_product') }}" class="btn btn-primary w-100">
                <i class="fas fa-plus"></i> Nouveau produit
            </a>
        </div>
        <div class="col-md-9">
            <form method="GET" class="row g-3">
                <div class="col-md-4">
                    <div class="input-group">
                        <input type="text" class="form-control" name="search" 
                               placeholder="Rechercher un produit..." value="{{ search }}">
                        <button class="btn btn-outline-secondary" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
                <div class="col-md-3">
                    <select class="form-select" name="category" onchange="this.form.submit()">
                        <option value="">Toutes les catégories</option>
                        {% for category in categories %}
                        <option value="{{ category.id }}" {% if selected_category == category.id %}selected{% endif %}>
                            {{ category.name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <select class="form-select" name="per_page" onchange="this.form.submit()">
                        {% for n in [10, 25, 50, 100] %}
                        <option value="{{ n }}" {% if per_page == n %}selected{% endif %}>
                            {{ n }} par page
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-2">
                    <a href="{{ url_for('inventory.products') }}" class="btn btn-outline-secondary w-100">
                        <i class="fas fa-undo"></i> Réinitialiser
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Liste des produits -->
    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Image</th>
                            <th>Nom</th>
                            <th>Catégorie</th>
                            <th>Prix de vente</th>
                            <th>Prix référentiel</th>
                            <th>Coût moyen</th>
                            <th>Stock</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for product in products.items %}
                        <tr>
                            <td style="width: 100px;">
                                {% if product.image_path %}
                                <img src="{{ url_for('static', filename=product.image_path) }}" 
                                     alt="{{ product.name }}"
                                     class="img-thumbnail"
                                     style="width: 80px; height: 80px; object-fit: cover;">
                                {% else %}
                                <div class="bg-light d-flex align-items-center justify-content-center" 
                                     style="width: 80px; height: 80px;">
                                    <i class="fas fa-image text-muted"></i>
                                </div>
                                {% endif %}
                            </td>
                            <td>
                                <strong>{{ product.name }}</strong>
                                {% if product.description %}
                                <br>
                                <small class="text-muted">{{ product.description[:100] }}...</small>
                                {% endif %}
                                {% if product.has_recipe %}
                                <span class="badge bg-info ms-2">Recette</span>
                                {% endif %}
                            </td>
                            <td>
                                <span class="badge bg-primary">{{ product.category.name }}</span>
                            </td>
                            <td>
                                <strong>{{ "%.2f"|format(product.price) }} €</strong>
                            </td>
                            <td>
                                {% if product.cost_price %}
                                    <span class="text-info">{{ "%.2f"|format(product.cost_price) }} €</span>
                                {% else %}
                                    <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if product.has_recipe %}
                                    <span class="text-success">{{ "%.2f"|format(product.calculate_recipe_cost()) }} €</span>
                                    <br><small class="text-muted">Calculé</small>
                                {% elif product.average_cost and (product.average_cost or 0) > 0 %}
                                    <span class="text-primary">{{ "%.2f"|format(product.average_cost) }} €</span>
                                    {% if product.cost_price and product.average_cost != product.cost_price %}
                                        <br><small class="text-warning">
                                            <i class="fas fa-chart-line"></i> Moyen
                                        </small>
                                    {% endif %}
                                {% elif product.cost_price %}
                                    <span class="text-muted">{{ "%.2f"|format(product.cost_price) }} €</span>
                                    <br><small class="text-muted">Référentiel</small>
                                {% else %}
                                    <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td>
                                {% set status = product.get_stock_status() %}
                                {% if status == 'out_of_stock' %}
                                    <span class="badge bg-danger">Rupture de stock</span>
                                {% elif status == 'low_stock' %}
                                    <span class="badge bg-warning">Stock bas</span>
                                {% else %}
                                    <span class="badge bg-success">En stock</span>
                                {% endif %}
                                <br>
                                {% if not product.has_recipe %}
                                    <small>Stock: {{ product.stock_quantity }} {{ product.unit }}</small>
                                {% else %}
                                    <small>Disponible: {{ product.get_available_quantity() }} {{ product.unit }}</small>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group">
                                    <a href="{{ url_for('inventory.edit_product', id=product.id) }}" 
                                       class="btn btn-outline-primary btn-sm" 
                                       title="Modifier">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    
                                    {% if product.has_recipe %}
                                    <a href="{{ url_for('inventory.recipe', product_id=product.id) }}" 
                                       class="btn btn-outline-info btn-sm"
                                       title="Gérer la recette">
                                        <i class="fas fa-book"></i>
                                    </a>
                                    {% endif %}
                                    
                                    <a href="{{ url_for('inventory.delete_product', id=product.id) }}" 
                                       class="btn btn-outline-danger btn-sm"
                                       onclick="return confirm('Êtes-vous sûr de vouloir supprimer ce produit ?')"
                                       title="Supprimer">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% else %}
                        <tr>
                            <td colspan="6" class="text-center py-4">
                                <div class="alert alert-info mb-0">
                                    <i class="fas fa-info-circle"></i> Aucun produit trouvé.
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if products.pages > 1 %}
            <nav aria-label="Navigation des pages" class="mt-4">
                <ul class="pagination justify-content-center">
                    {% if products.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('inventory.products', page=products.prev_num, search=search, category=selected_category, per_page=per_page) }}">
                            Précédent
                        </a>
                    </li>
                    {% endif %}

                    {% for page_num in range(1, products.pages + 1) %}
                    <li class="page-item {{ 'active' if page_num == products.page else '' }}">
                        <a class="page-link" href="{{ url_for('inventory.products', page=page_num, search=search, category=selected_category, per_page=per_page) }}">
                            {{ page_num }}
                        </a>
                    </li>
                    {% endfor %}

                    {% if products.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('inventory.products', page=products.next_num, search=search, category=selected_category, per_page=per_page) }}">
                            Suivant
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %} 