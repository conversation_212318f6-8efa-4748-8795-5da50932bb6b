from typing import Dict

from .. import Provider as LoremProvider


class Provider(LoremProvider):
    """Implement lorem provider for ``zh_TW`` locale."""

    word_connector = ""
    word_list = (
        "一個",
        "我們",
        "時間",
        "台灣",
        "可以",
        "公司",
        "沒有",
        "信息",
        "下載",
        "軟體",
        "注冊",
        "自己",
        "產品",
        "工作",
        "論壇",
        "企業",
        "這個",
        "他們",
        "管理",
        "已經",
        "問題",
        "內容",
        "使用",
        "進行",
        "市場",
        "服務",
        "如果",
        "系統",
        "技術",
        "發展",
        "現在",
        "作者",
        "就是",
        "網絡",
        "提供",
        "相關",
        "我的",
        "文章",
        "方式",
        "電話",
        "發表",
        "所有",
        "時候",
        "因為",
        "台北",
        "有限",
        "公司",
        "什麼",
        "還是",
        "開始",
        "本站",
        "發布",
        "自己",
        "支持",
        "在線",
        "國家",
        "生活",
        "聯系",
        "積分",
        "主題",
        "所以",
        "不能",
        "的人",
        "上海",
        "中心",
        "世界",
        "游戲",
        "需要",
        "價格",
        "用戶",
        "通過",
        "要求",
        "不是",
        "免費",
        "個人",
        "但是",
        "地址",
        "網站",
        "情況",
        "最后",
        "設計",
        "同時",
        "這些",
        "活動",
        "手機",
        "推薦",
        "一些",
        "主要",
        "大家",
        "發現",
        "目前",
        "文件",
        "你的",
        "不過",
        "評論",
        "生產",
        "美國",
        "圖片",
        "經濟",
        "功能",
        "國際",
        "的是",
        "選擇",
        "其他",
        "這樣",
        "會員",
        "環境",
        "來自",
        "日期",
        "成為",
        "他的",
        "最新",
        "專業",
        "一下",
        "人員",
        "任何",
        "教育",
        "資料",
        "狀態",
        "都是",
        "點擊",
        "為了",
        "不會",
        "出現",
        "知道",
        "社會",
        "名稱",
        "而且",
        "介紹",
        "音樂",
        "等級",
        "可能",
        "這種",
        "建設",
        "朋友",
        "雖然",
        "電子",
        "資源",
        "看到",
        "精華",
        "電影",
        "如何",
        "新聞",
        "閱讀",
        "安全",
        "全國",
        "隻有",
        "回復",
        "大學",
        "學生",
        "學習",
        "關於",
        "項目",
        "不同",
        "以及",
        "有關",
        "那麼",
        "開發",
        "還有",
        "隻是",
        "非常",
        "研究",
        "廣告",
        "首頁",
        "方法",
        "希望",
        "地方",
        "也是",
        "單位",
        "怎麼",
        "應該",
        "今天",
        "以上",
        "更新",
        "帖子",
        "顯示",
        "能力",
        "電腦",
        "記者",
        "查看",
        "位置",
        "不要",
        "由於",
        "無法",
        "詳細",
        "投資",
        "是一",
        "一般",
        "進入",
        "發生",
        "這裡",
        "感覺",
        "更多",
        "你們",
        "的話",
        "起來",
        "標准",
        "一樣",
        "認為",
        "女人",
        "那個",
        "設備",
        "搜索",
        "之后",
        "然后",
        "學校",
        "銷售",
        "組織",
        "說明",
        "提高",
        "為什",
        "作品",
        "或者",
        "喜歡",
        "東西",
        "方面",
        "簡介",
        "必須",
        "經營",
        "科技",
        "作為",
        "其中",
        "運行",
        "工程",
        "解決",
        "操作",
        "經驗",
        "地區",
        "重要",
        "直接",
        "登錄",
        "合作",
        "結果",
        "影響",
        "這是",
        "行業",
        "對於",
        "表示",
        "程序",
        "包括",
        "留言",
        "規定",
        "處理",
        "男人",
        "各種",
        "部門",
        "數據",
        "具有",
        "商品",
        "系列",
        "大小",
        "因此",
        "關系",
        "可是",
        "比較",
        "文化",
        "一直",
        "法律",
        "這麼",
        "您的",
        "城市",
        "分析",
        "基本",
        "最大",
        "類別",
        "兩個",
        "日本",
        "得到",
        "一次",
        "繼續",
        "成功",
        "她的",
        "責任",
        "深圳",
        "業務",
        "歡迎",
        "加入",
        "能夠",
        "覺得",
        "部分",
        "中文",
        "根據",
        "人民",
        "政府",
        "控制",
        "其實",
        "之間",
        "一種",
        "威望",
        "實現",
        "語言",
        "出來",
        "謝謝",
        "社區",
        "品牌",
        "是否",
        "工具",
        "完全",
        "決定",
        "很多",
        "網上",
        "事情",
        "今年",
        "國內",
        "以后",
        "制作",
        "瀏覽",
        "過程",
        "完成",
        "類型",
        "來源",
        "質量",
        "有些",
        "一起",
        "當然",
        "汽車",
        "一點",
        "幫助",
        "增加",
        "歷史",
        "以下",
        "不斷",
        "應用",
        "那些",
        "密碼",
        "計劃",
        "如此",
        "次數",
        "到了",
        "擁有",
        "孩子",
        "原因",
        "參加",
        "隻要",
        "報告",
        "當前",
        "客戶",
        "正在",
        "注意",
        "標題",
        "空間",
        "一定",
        "一切",
        "特別",
        "全部",
        "准備",
    )

    parts_of_speech: Dict[str, tuple] = {}
