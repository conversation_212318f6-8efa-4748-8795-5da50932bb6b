from collections import OrderedDict
from itertools import zip_longest
from typing import Dict

from ..es import Provider as PersonProvider


class Provider(PersonProvider):
    formats_male = OrderedDict(
        [
            ("{{given_name_male}} {{last_name}} {{last_name}}", 0.55),
            ("{{first_name_male}} {{last_name}} {{last_name}}", 0.25),
            ("{{first_name_male}} {{last_name}}", 0.17),
            ("{{given_name_male}} {{last_name}}-{{last_name}} {{last_name}}", 0.01),
            ("{{first_name_male}} {{last_name}}-{{last_name}} {{last_name}}", 0.01),
            ("{{first_name_male}} {{last_name}}-{{last_name}}", 0.01),
        ]
    )

    formats_female = OrderedDict(
        [
            ("{{given_name_female}} {{last_name}} {{last_name}}", 0.55),
            ("{{first_name_female}} {{last_name}} {{last_name}}", 0.25),
            ("{{first_name_female}} {{last_name}}", 0.17),
            ("{{given_name_female}} {{last_name}}-{{last_name}} {{last_name}}", 0.01),
            ("{{first_name_female}} {{last_name}}-{{last_name}} {{last_name}}", 0.01),
            ("{{first_name_female}} {{last_name}}-{{last_name}}", 0.01),
        ]
    )

    formats = OrderedDict(
        [
            ("formats_male", 0.48),
            ("formats_female", 0.52),
        ]
    )

    # Sources for names data:
    # Servicio de Registro Civil e Identificación
    # Inquiry under Law of Transparency #AK002T0020771 for names and last names
    # https://docs.google.com/spreadsheets/d/1yJ2wVnlttoBaCMS-xWyw7fbUqe6xdYpg/edit?usp=sharing&ouid=105306283136031380407
    # Data was truncated to 500 items for each category

    # 500 male first names, weighted
    first_names_male: Dict[str, float] = OrderedDict(
        [
            ("José", 0.05357441),
            ("Juan", 0.05188295),
            ("Luis", 0.04369026),
            ("Carlos", 0.02752294),
            ("Jorge", 0.02148181),
            ("Manuel", 0.01846196),
            ("Francisco", 0.01755076),
            ("Víctor", 0.01596373),
            ("Cristian", 0.01564751),
            ("Pedro", 0.01471588),
            ("Sebastián", 0.01369752),
            ("Matías", 0.01313522),
            ("Felipe", 0.01255329),
            ("Benjamín", 0.01251180),
            ("Miguel", 0.01246343),
            ("Diego", 0.01215592),
            ("Rodrigo", 0.01212607),
            ("Héctor", 0.01203257),
            ("Sergio", 0.01171687),
            ("Daniel", 0.01127892),
            ("Eduardo", 0.01096382),
            ("Pablo", 0.01053620),
            ("Patricio", 0.01010251),
            ("Claudio", 0.00996611),
            ("Ricardo", 0.00938327),
            ("Vicente", 0.00932498),
            ("Fernando", 0.00900068),
            ("Mario", 0.00887763),
            ("Alejandro", 0.00886620),
            ("Javier", 0.00854411),
            ("Oscar", 0.00834245),
            ("Jaime", 0.00819461),
            ("Roberto", 0.00812813),
            ("Mauricio", 0.00789297),
            ("Martín", 0.00782052),
            ("Joaquín", 0.00753223),
            ("David", 0.00750623),
            ("Gabriel", 0.00734777),
            ("Marcelo", 0.00727643),
            ("Ignacio", 0.00682999),
            ("Raúl", 0.00676584),
            ("Guillermo", 0.00675521),
            ("Cristóbal", 0.00644608),
            ("Julio", 0.00639650),
            ("Tomás", 0.00638588),
            ("Marco", 0.00621497),
            ("Andrés", 0.00616670),
            ("Maximiliano", 0.00584847),
            ("César", 0.00522869),
            ("Hugo", 0.00493089),
            ("Bastián", 0.00487160),
            ("Nelson", 0.00476677),
            ("Gonzalo", 0.00475513),
            ("Lucas", 0.00456965),
            ("Mateo", 0.00453424),
            ("Iván", 0.00451005),
            ("Álvaro", 0.00443902),
            ("Fabián", 0.00422673),
            ("Jonathan", 0.00415701),
            ("Esteban", 0.00414517),
            ("Hernán", 0.00410914),
            ("Alonso", 0.00409913),
            ("Ángel", 0.00405187),
            ("Leonardo", 0.00399470),
            ("Gustavo", 0.00399227),
            ("Ramón", 0.00398701),
            ("Santiago", 0.00396991),
            ("Rafael", 0.00378140),
            ("Enrique", 0.00360958),
            ("Camilo", 0.00356728),
            ("Alex", 0.00356607),
            ("Alexis", 0.00356172),
            ("Antonio", 0.00353500),
            ("Christian", 0.00353399),
            ("Franco", 0.00352286),
            ("René", 0.00335560),
            ("Rubén", 0.00330075),
            ("Alberto", 0.00309433),
            ("Samuel", 0.00303513),
            ("Emilio", 0.00299425),
            ("Marcos", 0.00285198),
            ("Renato", 0.00282477),
            ("Máximo", 0.00270364),
            ("Luciano", 0.00268897),
            ("Alfredo", 0.00264688),
            ("Jean", 0.00260822),
            ("Arturo", 0.00260529),
            ("Osvaldo", 0.00252191),
            ("Germán", 0.00252150),
            ("Kevin", 0.00250956),
            ("Gaspar", 0.00245138),
            ("Segundo", 0.00244025),
            ("Bruno", 0.00235060),
            ("Ernesto", 0.00232257),
            ("Elías", 0.00225062),
            ("Domingo", 0.00223383),
            ("Rodolfo", 0.00223342),
            ("Humberto", 0.00222290),
            ("Ariel", 0.00221673),
            ("Julián", 0.00219548),
            ("Gerardo", 0.00219072),
            ("Alexander", 0.00217655),
            ("Richard", 0.00216310),
            ("Omar", 0.00213557),
            ("Cristopher", 0.00212606),
            ("Alfonso", 0.00196720),
            ("Simón", 0.00195900),
            ("Moisés", 0.00194736),
            ("Bernardo", 0.00194210),
            ("Orlando", 0.00188382),
            ("John", 0.00173183),
            ("Jesús", 0.00171827),
            ("Michael", 0.00170411),
            ("Emiliano", 0.00156255),
            ("Damián", 0.00155516),
            ("Rolando", 0.00154747),
            ("Armando", 0.00154524),
            ("Alan", 0.00153917),
            ("Angelo", 0.00147067),
            ("Ismael", 0.00143454),
            ("Danilo", 0.00142665),
            ("Isaac", 0.00140581),
            ("Leandro", 0.00140439),
            ("Joel", 0.00140378),
            ("Dante", 0.00139913),
            ("Adolfo", 0.00137201),
            ("Amaro", 0.00136118),
            ("Félix", 0.00135673),
            ("Ian", 0.00134115),
            ("Lorenzo", 0.00133740),
            ("Abraham", 0.00133123),
            ("Bryan", 0.00132516),
            ("Thomas", 0.00131969),
            ("Christopher", 0.00128317),
            ("Facundo", 0.00127446),
            ("Erick", 0.00125453),
            ("Freddy", 0.00125392),
            ("Leonel", 0.00123996),
            ("Walter", 0.00121962),
            ("Eugenio", 0.00120859),
            ("Wilson", 0.00119958),
            ("Aníbal", 0.00119230),
            ("Nicolás", 0.00119088),
            ("León", 0.00117166),
            ("Salvador", 0.00116255),
            ("Edgardo", 0.00115972),
            ("Boris", 0.00114120),
            ("Adrián", 0.00112126),
            ("Robinson", 0.00112066),
            ("Brayan", 0.00108676),
            ("William", 0.00108544),
            ("Reinaldo", 0.00106288),
            ("Jesus", 0.00106187),
            ("Isaías", 0.00104578),
            ("Dylan", 0.00103870),
            ("Aldo", 0.00102959),
            ("Gastón", 0.00101087),
            ("Benjamin", 0.00100581),
            ("Eric", 0.00100409),
            ("Ronald", 0.00098709),
            ("Aarón", 0.00098254),
            ("Paulo", 0.00097039),
            ("Octavio", 0.00092577),
            ("Mariano", 0.00092243),
            ("Erwin", 0.00091636),
            ("Hans", 0.00090816),
            ("Enzo", 0.00090715),
            ("Abel", 0.00089723),
            ("Valentín", 0.00089400),
            ("Guido", 0.00089126),
            ("Augusto", 0.00086516),
            ("Heriberto", 0.00084694),
            ("Axel", 0.00084563),
            ("Cristofer", 0.00084350),
            ("Jordán", 0.00083177),
            ("Darío", 0.00082074),
            ("Israel", 0.00081760),
            ("Clemente", 0.00081163),
            ("Giovanni", 0.00079473),
            ("Johan", 0.00078937),
            ("Josue", 0.00078927),
            ("Jhon", 0.00078643),
            ("Rigoberto", 0.00077662),
            ("Néstor", 0.00076640),
            ("Edgar", 0.00075314),
            ("Yerko", 0.00074808),
            ("Robert", 0.00074596),
            ("Exequiel", 0.00074444),
            ("Waldo", 0.00073958),
            ("Brian", 0.00073260),
            ("Lukas", 0.00072683),
            ("Henry", 0.00069354),
            ("Brandon", 0.00069243),
            ("Fredy", 0.00068656),
            ("Williams", 0.00067968),
            ("Paul", 0.00067907),
            ("Cesar", 0.00067047),
            ("Gregorio", 0.00066066),
            ("Jairo", 0.00065823),
            ("Raimundo", 0.00063212),
            ("Liam", 0.00062231),
            ("Mathias", 0.00062008),
            ("Martin", 0.00061401),
            ("Jimmy", 0.00060774),
            ("Gilberto", 0.00060763),
            ("Federico", 0.00060237),
            ("Dagoberto", 0.00059782),
            ("Max", 0.00058922),
            ("Wladimir", 0.00058851),
            ("Milton", 0.00058001),
            ("Braulio", 0.00057586),
            ("Michel", 0.00057566),
            ("Edwin", 0.00057424),
            ("Edison", 0.00056089),
            ("Fidel", 0.00055360),
            ("Jeremy", 0.00055147),
            ("Benito", 0.00054975),
            ("Efraín", 0.00054814),
            ("Horacio", 0.00054743),
            ("Erik", 0.00054358),
            ("Mauro", 0.00054085),
            ("Ramiro", 0.00053164),
            ("Leopoldo", 0.00052931),
            ("Ítalo", 0.00052830),
            ("Joseph", 0.00051272),
            ("Pascual", 0.00051120),
            ("Marcelino", 0.00050877),
            ("Eliseo", 0.00050705),
            ("Byron", 0.00049845),
            ("Santino", 0.00049653),
            ("Oliver", 0.00049056),
            ("Gael", 0.00048894),
            ("Darwin", 0.00048074),
            ("Misael", 0.00047933),
            ("Adán", 0.00047781),
            ("Baltazar", 0.00047528),
            ("Edmundo", 0.00047326),
            ("Bayron", 0.00046840),
            ("Anthony", 0.00046759),
            ("Emanuel", 0.00046374),
            ("Paolo", 0.00046101),
            ("Arnoldo", 0.00045919),
            ("Emmanuel", 0.00045727),
            ("Ulises", 0.00044978),
            ("Dilan", 0.00044523),
            ("Rogelio", 0.00044442),
            ("Nibaldo", 0.00043531),
            ("Cristhian", 0.00043147),
            ("Jeremías", 0.00042732),
            ("Johnny", 0.00042469),
            ("Sandro", 0.00042297),
            ("Thiago", 0.00042256),
            ("Flavio", 0.00042206),
            ("Elvis", 0.00041882),
            ("James", 0.00041700),
            ("Jacob", 0.00041528),
            ("Vladimir", 0.00040576),
            ("Marcial", 0.00040222),
            ("Herman", 0.00039838),
            ("Aurelio", 0.00039342),
            ("Arnaldo", 0.00038532),
            ("Saúl", 0.00038330),
            ("Edward", 0.00038269),
            ("Franklin", 0.00037359),
            ("Santos", 0.00036913),
            ("Florencio", 0.00036579),
            ("Erasmo", 0.00036013),
            ("Roger", 0.00035446),
            ("Cristobal", 0.00035426),
            ("Juvenal", 0.00035315),
            ("Fermín", 0.00034819),
            ("Joshua", 0.00034697),
            ("Frank", 0.00034627),
            ("Ezequiel", 0.00034596),
            ("Benedicto", 0.00034535),
            ("Gerald", 0.00034455),
            ("Lautaro", 0.00033979),
            ("Wilfredo", 0.00033949),
            ("Abelardo", 0.00033797),
            ("Gerson", 0.00033665),
            ("Joan", 0.00033341),
            ("Leónidas", 0.00033271),
            ("Patrick", 0.00033038),
            ("Matteo", 0.00032916),
            ("Ruperto", 0.00032765),
            ("Emerson", 0.00032016),
            ("Danny", 0.00031773),
            ("Nolberto", 0.00031712),
            ("Gino", 0.00031611),
            ("Amador", 0.00031571),
            ("Bernardino", 0.00031378),
            ("Andy", 0.00031125),
            ("Demian", 0.00031055),
            ("Eladio", 0.00030994),
            ("Piero", 0.00030559),
            ("Yonathan", 0.00029274),
            ("Agustin", 0.00028990),
            ("Peter", 0.00028828),
            ("Tomas", 0.00028798),
            ("Borja", 0.00028748),
            ("Jonatan", 0.00028748),
            ("Jhonny", 0.00028059),
            ("Nicanor", 0.00028039),
            ("Genaro", 0.00028009),
            ("Jason", 0.00027948),
            ("Celso", 0.00027857),
            ("Sixto", 0.00027756),
            ("Eleodoro", 0.00027645),
            ("Evaristo", 0.00027604),
            ("Teodoro", 0.00027594),
            ("Maicol", 0.00027554),
            ("Washington", 0.00027493),
            ("Aquiles", 0.00027260),
            ("Román", 0.00026876),
            ("Rosendo", 0.00026532),
            ("Aliro", 0.00026461),
            ("Rosamel", 0.00026349),
            ("Harold", 0.00026279),
            ("Justo", 0.00025843),
            ("Florentino", 0.00024690),
            ("Anselmo", 0.00024488),
            ("Hipólito", 0.00024467),
            ("Allan", 0.00024245),
            ("Edgard", 0.00024214),
            ("Eusebio", 0.00024184),
            ("Eliecer", 0.00023810),
            ("Jacinto", 0.00023698),
            ("Froilán", 0.00023678),
            ("Steven", 0.00023668),
            ("George", 0.00023526),
            ("Charles", 0.00023162),
            ("Belisario", 0.00023121),
            ("Valentino", 0.00023071),
            ("Pierre", 0.00022858),
            ("Fabio", 0.00022636),
            ("Junior", 0.00022605),
            ("Tito", 0.00022605),
            ("Salomón", 0.00022494),
            ("Clodomiro", 0.00022393),
            ("Gary", 0.00022312),
            ("Dionisio", 0.00022282),
            ("Alamiro", 0.00022150),
            ("Edson", 0.00021938),
            ("Renzo", 0.00021927),
            ("Denis", 0.00021887),
            ("Noah", 0.00021877),
            ("Anderson", 0.00021836),
            ("Amaru", 0.00021614),
            ("Edinson", 0.00021371),
            ("Delfín", 0.00021361),
            ("Bernabé", 0.00021098),
            ("Iker", 0.00020956),
            ("Matheo", 0.00020865),
            ("Belarmino", 0.00020845),
            ("Douglas", 0.00020511),
            ("Desiderio", 0.00020450),
            ("Alexi", 0.00020308),
            ("Isidro", 0.00020288),
            ("Ethan", 0.00020268),
            ("Elian", 0.00019964),
            ("Mirko", 0.00019772),
            ("Américo", 0.00019701),
            ("Demetrio", 0.00019600),
            ("Gumercindo", 0.00019408),
            ("Andrew", 0.00019327),
            ("Ciro", 0.00019286),
            ("Milán", 0.00019256),
            ("Stefano", 0.00019256),
            ("Remigio", 0.00019226),
            ("Thomás", 0.00019216),
            ("Leoncio", 0.00018973),
            ("Neftalí", 0.00018770),
            ("Wilmer", 0.00018760),
            ("Heraldo", 0.00018669),
            ("Josué", 0.00018608),
            ("Eleazar", 0.00018568),
            ("Ronny", 0.00018447),
            ("Justin", 0.00018366),
            ("Nahuel", 0.00018204),
            ("Yordan", 0.00018163),
            ("Jhonatan", 0.00018113),
            ("Tránsito", 0.00017991),
            ("Silvio", 0.00017870),
            ("Artemio", 0.00017688),
            ("Lucio", 0.00017637),
            ("Galvarino", 0.00017576),
            ("Narciso", 0.00017516),
            ("Eloy", 0.00017435),
            ("Aladino", 0.00017303),
            ("Wenceslao", 0.00017232),
            ("Nestor", 0.00017202),
            ("Feliciano", 0.00017182),
            ("Lisandro", 0.00017091),
            ("Yonatan", 0.00017081),
            ("Ramon", 0.00017040),
            ("Rudy", 0.00017040),
            ("Yeison", 0.00017000),
            ("Maikol", 0.00016939),
            ("Bairon", 0.00016868),
            ("Albert", 0.00016858),
            ("Avelino", 0.00016706),
            ("Jerson", 0.00016625),
            ("Herminio", 0.00016473),
            ("Andre", 0.00016362),
            ("Modesto", 0.00016352),
            ("Armin", 0.00016342),
            ("Cristián", 0.00016210),
            ("Atilio", 0.00016200),
            ("Custodio", 0.00016200),
            ("Dennis", 0.00016190),
            ("Gregory", 0.00016129),
            ("Jefferson", 0.00016099),
            ("Teófilo", 0.00016079),
            ("Lionel", 0.00015978),
            ("Willy", 0.00015978),
            ("Rómulo", 0.00015967),
            ("Carlo", 0.00015765),
            ("Igor", 0.00015664),
            ("Reynaldo", 0.00015563),
            ("Lino", 0.00015522),
            ("Basilio", 0.00015492),
            ("Marcel", 0.00015431),
            ("Blas", 0.00015381),
            ("Johann", 0.00015330),
            ("Eulogio", 0.00015310),
            ("Eleuterio", 0.00015209),
            ("Lian", 0.00015148),
            ("Isidoro", 0.00015117),
            ("Xavier", 0.00014986),
            ("Ivo", 0.00014976),
            ("Abdón", 0.00014935),
            ("Harry", 0.00014885),
            ("Alessandro", 0.00014753),
            ("Simon", 0.00014662),
            ("Arsenio", 0.00014601),
            ("Bladimir", 0.00014359),
            ("Jonas", 0.00014318),
            ("Cristhofer", 0.00014257),
            ("Joao", 0.00014237),
            ("Franz", 0.00014207),
            ("Jeison", 0.00014197),
            ("Milovan", 0.00014176),
            ("Floridor", 0.00014136),
            ("Jerónimo", 0.00013944),
            ("Tulio", 0.00013893),
            ("Jair", 0.00013782),
            ("Marlon", 0.00013772),
            ("Samir", 0.00013772),
            ("Onofre", 0.00013660),
            ("Percy", 0.00013509),
            ("Rony", 0.00013438),
            ("Yuri", 0.00013418),
            ("Jerman", 0.00013367),
            ("Giovanny", 0.00013286),
            ("Matthew", 0.00013205),
            ("Gian", 0.00013134),
            ("Jordan", 0.00013094),
            ("Abner", 0.00013013),
            ("Alain", 0.00012942),
            ("Ceferino", 0.00012912),
            ("Yohan", 0.00012912),
            ("Roque", 0.00012891),
            ("Eithan", 0.00012770),
            ("Paulino", 0.00012760),
            ("Rudecindo", 0.00012750),
            ("Mark", 0.00012740),
            ("Norman", 0.00012568),
            ("Fabrizio", 0.00012446),
            ("Norberto", 0.00012244),
            ("Kurt", 0.00012203),
            ("Gianfranco", 0.00012193),
            ("Johans", 0.00012102),
            ("Olegario", 0.00012041),
            ("Christofer", 0.00011981),
            ("Maykol", 0.00011839),
            ("Hermes", 0.00011829),
            ("Celestino", 0.00011788),
            ("Albino", 0.00011768),
            ("Fabricio", 0.00011738),
            ("Giancarlo", 0.00011738),
            ("Derek", 0.00011718),
            ("Iñaki", 0.00011687),
            ("Jan", 0.00011687),
            ("Zacarías", 0.00011596),
            ("Said", 0.00011586),
            ("Hardy", 0.00011566),
            ("Ronaldo", 0.00011556),
            ("Aron", 0.00011414),
            ("Eydan", 0.00011323),
            ("Elio", 0.00011313),
            ("Lenin", 0.00011262),
            ("Victoriano", 0.00011232),
            ("Jhoan", 0.00011110),
            ("Dany", 0.00011070),
            ("Eduard", 0.00011040),
            ("Gerónimo", 0.00010989),
            ("Cipriano", 0.00010979),
            ("Victorino", 0.00010908),
            ("Cornelio", 0.00010807),
            ("Anyelo", 0.00010797),
        ]
    )

    # 500 female first names, weighted
    first_names_female: Dict[str, float] = OrderedDict(
        [
            ("María", 0.09500510),
            ("Ana", 0.02063161),
            ("Rosa", 0.01863127),
            ("Claudia", 0.01307437),
            ("Carolina", 0.01284289),
            ("Camila", 0.01283978),
            ("Patricia", 0.01267301),
            ("Catalina", 0.01188959),
            ("Javiera", 0.01138562),
            ("Sofía", 0.01127980),
            ("Daniela", 0.01091069),
            ("Constanza", 0.01049726),
            ("Francisca", 0.01047776),
            ("Valentina", 0.01038257),
            ("Carmen", 0.00923868),
            ("Margarita", 0.00852030),
            ("Juana", 0.00831674),
            ("Sandra", 0.00805135),
            ("Marcela", 0.00804935),
            ("Fernanda", 0.00779061),
            ("Elizabeth", 0.00749475),
            ("Verónica", 0.00723435),
            ("Martina", 0.00696652),
            ("Isidora", 0.00684806),
            ("Alejandra", 0.00682778),
            ("Cecilia", 0.00669337),
            ("Antonia", 0.00647906),
            ("Emilia", 0.00646743),
            ("Paola", 0.00644926),
            ("Marta", 0.00641635),
            ("Mónica", 0.00632094),
            ("Andrea", 0.00620359),
            ("Paula", 0.00598596),
            ("Gloria", 0.00587238),
            ("Isabel", 0.00583215),
            ("Pamela", 0.00573874),
            ("Florencia", 0.00561851),
            ("Katherine", 0.00555291),
            ("Laura", 0.00550238),
            ("Paulina", 0.00547535),
            ("Teresa", 0.00543800),
            ("Natalia", 0.00532886),
            ("Silvia", 0.00527810),
            ("Jessica", 0.00525306),
            ("Gabriela", 0.00523566),
            ("Gladys", 0.00515411),
            ("Bárbara", 0.00513106),
            ("Josefa", 0.00509771),
            ("Alicia", 0.00499510),
            ("Antonella", 0.00498789),
            ("Nicole", 0.00473403),
            ("Victoria", 0.00468760),
            ("Anahí", 0.00467751),
            ("Carla", 0.00463840),
            ("Agustina", 0.00455208),
            ("Karen", 0.00454133),
            ("Jacqueline", 0.00452925),
            ("Sara", 0.00451917),
            ("Luz", 0.00446099),
            ("Nancy", 0.00444426),
            ("Lorena", 0.00440536),
            ("Viviana", 0.00438287),
            ("Sonia", 0.00437256),
            ("Ximena", 0.00432957),
            ("Olga", 0.00431705),
            ("Amanda", 0.00416989),
            ("Elena", 0.00416524),
            ("Maite", 0.00408014),
            ("Luisa", 0.00407449),
            ("Susana", 0.00390373),
            ("Blanca", 0.00381785),
            ("Karina", 0.00380766),
            ("Macarena", 0.00380378),
            ("Ruth", 0.00376111),
            ("Marisol", 0.00360221),
            ("Eliana", 0.00359900),
            ("Ángela", 0.00356044),
            ("Angélica", 0.00356022),
            ("Cristina", 0.00355102),
            ("Julia", 0.00347921),
            ("Trinidad", 0.00343445),
            ("Valeria", 0.00338414),
            ("Evelyn", 0.00333128),
            ("Isabella", 0.00325449),
            ("Norma", 0.00320319),
            ("Tamara", 0.00317216),
            ("Adriana", 0.00311011),
            ("Ingrid", 0.00307764),
            ("Lucía", 0.00300461),
            ("Fabiola", 0.00299597),
            ("Lidia", 0.00294179),
            ("Belén", 0.00293359),
            ("Magdalena", 0.00291375),
            ("Romina", 0.00289048),
            ("Ignacia", 0.00286256),
            ("Erika", 0.00278266),
            ("Rocío", 0.00277291),
            ("Miriam", 0.00270354),
            ("Edith", 0.00266919),
            ("Elsa", 0.00266343),
            ("Graciela", 0.00265867),
            ("Karla", 0.00263407),
            ("Julieta", 0.00261091),
            ("Irma", 0.00259816),
            ("Berta", 0.00258276),
            ("Raquel", 0.00255539),
            ("Inés", 0.00255317),
            ("Mercedes", 0.00253755),
            ("Hilda", 0.00251306),
            ("Maritza", 0.00246818),
            ("Mariana", 0.00246364),
            ("Beatriz", 0.00236591),
            ("Roxana", 0.00232612),
            ("Vanessa", 0.00232081),
            ("Josefina", 0.00229687),
            ("Emma", 0.00227183),
            ("Renata", 0.00225942),
            ("Yolanda", 0.00224435),
            ("Clara", 0.00222451),
            ("Pía", 0.00218019),
            ("Flor", 0.00215260),
            ("Mariela", 0.00212600),
            ("Myriam", 0.00203758),
            ("Yasna", 0.00200090),
            ("Marcia", 0.00199669),
            ("Elisa", 0.00198904),
            ("Paz", 0.00194017),
            ("Emily", 0.00193962),
            ("Nelly", 0.00192488),
            ("Monserrat", 0.00192222),
            ("Leonor", 0.00191879),
            ("Jeannette", 0.00191757),
            ("Jocelyn", 0.00191502),
            ("Ema", 0.00191380),
            ("Soledad", 0.00191236),
            ("Elba", 0.00189751),
            ("Anaís", 0.00184055),
            ("Violeta", 0.00179800),
            ("Iris", 0.00178692),
            ("Génesis", 0.00177296),
            ("Fresia", 0.00176886),
            ("Diana", 0.00176775),
            ("Matilde", 0.00176520),
            ("Liliana", 0.00176066),
            ("Alexandra", 0.00174559),
            ("Jennifer", 0.00173451),
            ("Solange", 0.00170714),
            ("Aurora", 0.00170326),
            ("Loreto", 0.00169617),
            ("Amelia", 0.00168398),
            ("Johanna", 0.00166415),
            ("Mia", 0.00161240),
            ("Bernardita", 0.00160320),
            ("Denisse", 0.00159733),
            ("Rosario", 0.00159101),
            ("Amalia", 0.00158392),
            ("Eva", 0.00156874),
            ("Ester", 0.00154159),
            ("Nataly", 0.00152530),
            ("Ivonne", 0.00149826),
            ("Nora", 0.00149317),
            ("Lilian", 0.00149294),
            ("Irene", 0.00147322),
            ("Marina", 0.00147156),
            ("Valeska", 0.00145039),
            ("Maribel", 0.00143433),
            ("Sylvia", 0.00141926),
            ("Millaray", 0.00139299),
            ("Michelle", 0.00138103),
            ("Bernarda", 0.00137715),
            ("Pilar", 0.00135809),
            ("Virginia", 0.00135443),
            ("Marianela", 0.00133482),
            ("Noemí", 0.00131133),
            ("Aída", 0.00130257),
            ("Tania", 0.00129448),
            ("Eugenia", 0.00129304),
            ("Doris", 0.00129249),
            ("Catherine", 0.00129072),
            ("Consuelo", 0.00128385),
            ("Estefanía", 0.00128218),
            ("Matilda", 0.00128130),
            ("Dominga", 0.00128119),
            ("Judith", 0.00126933),
            ("Rebeca", 0.00126235),
            ("Carol", 0.00125082),
            ("Mirta", 0.00124949),
            ("Tatiana", 0.00120462),
            ("Amparo", 0.00119276),
            ("Cynthia", 0.00119165),
            ("Guillermina", 0.00118877),
            ("Olivia", 0.00118301),
            ("Rafaela", 0.00117791),
            ("Jenny", 0.00116251),
            ("Silvana", 0.00116007),
            ("Marjorie", 0.00114821),
            ("Paloma", 0.00114245),
            ("Magaly", 0.00113879),
            ("Marlene", 0.00113181),
            ("Mireya", 0.00113059),
            ("Krishna", 0.00110544),
            ("Nicol", 0.00110045),
            ("Leslie", 0.00109081),
            ("Yesenia", 0.00108915),
            ("Ámbar", 0.00107386),
            ("Elvira", 0.00106732),
            ("Georgina", 0.00106178),
            ("Leticia", 0.00106145),
            ("Jimena", 0.00103064),
            ("Noelia", 0.00102544),
            ("Adela", 0.00100870),
            ("Dominique", 0.00100760),
            ("Colomba", 0.00100649),
            ("Nadia", 0.00098277),
            ("Pascal", 0.00095119),
            ("Stephanie", 0.00094787),
            ("Erica", 0.00094111),
            ("Luciana", 0.00092726),
            ("Yessica", 0.00092682),
            ("Johana", 0.00092405),
            ("Melissa", 0.00092050),
            ("Lissette", 0.00091972),
            ("Celia", 0.00090355),
            ("Alondra", 0.00090199),
            ("Priscila", 0.00090199),
            ("Abigail", 0.00089667),
            ("Mabel", 0.00089656),
            ("Rita", 0.00089158),
            ("Karin", 0.00089113),
            ("Angelina", 0.00088980),
            ("Lucila", 0.00088172),
            ("Geraldine", 0.00087795),
            ("Priscilla", 0.00087562),
            ("Delia", 0.00086022),
            ("Carola", 0.00085324),
            ("Mayra", 0.00084072),
            ("Danitza", 0.00083916),
            ("Rossana", 0.00083861),
            ("Samantha", 0.00083673),
            ("Filomena", 0.00082819),
            ("Brenda", 0.00082387),
            ("Jazmín", 0.00081756),
            ("Scarlett", 0.00081745),
            ("Damaris", 0.00081257),
            ("Esperanza", 0.00080792),
            ("Lucy", 0.00079429),
            ("Vania", 0.00079074),
            ("Oriana", 0.00077456),
            ("Zoila", 0.00076891),
            ("Yessenia", 0.00076381),
            ("Rayén", 0.00076282),
            ("Tiare", 0.00074564),
            ("Danae", 0.00074121),
            ("Dayana", 0.00073966),
            ("Katalina", 0.00073766),
            ("Sophia", 0.00072658),
            ("Thiare", 0.00072459),
            ("Francesca", 0.00072248),
            ("Manuela", 0.00072104),
            ("Fanny", 0.00071672),
            ("Anita", 0.00071594),
            ("Mary", 0.00070520),
            ("Joselyn", 0.00069655),
            ("Marie", 0.00069001),
            ("Vilma", 0.00068846),
            ("Eloísa", 0.00068026),
            ("Jeanette", 0.00067882),
            ("Hortensia", 0.00067749),
            ("Ernestina", 0.00067727),
            ("Alba", 0.00067428),
            ("Dina", 0.00066896),
            ("Haydée", 0.00066342),
            ("Lía", 0.00066187),
            ("Montserrat", 0.00065433),
            ("Debora", 0.00064480),
            ("Dafne", 0.00064414),
            ("Herminia", 0.00064104),
            ("Corina", 0.00062464),
            ("Giovanna", 0.00062397),
            ("Rosalía", 0.00062187),
            ("Yaritza", 0.00061965),
            ("Guadalupe", 0.00061522),
            ("Alison", 0.00060480),
            ("Celeste", 0.00060214),
            ("Aylin", 0.00059970),
            ("Carmela", 0.00058619),
            ("Cindy", 0.00058441),
            ("Susan", 0.00058064),
            ("Zunilda", 0.00058031),
            ("Mirtha", 0.00057943),
            ("Almendra", 0.00057920),
            ("Kimberly", 0.00057776),
            ("Regina", 0.00057577),
            ("Martha", 0.00057444),
            ("Kiara", 0.00057355),
            ("Estela", 0.00056990),
            ("Maira", 0.00056923),
            ("Zulema", 0.00056868),
            ("Estrella", 0.00054895),
            ("Gisela", 0.00054873),
            ("Ida", 0.00054840),
            ("Pascuala", 0.00054541),
            ("Petronila", 0.00054053),
            ("Uberlinda", 0.00053998),
            ("Ayleen", 0.00053588),
            ("Allison", 0.00053111),
            ("Franchesca", 0.00053023),
            ("Mayte", 0.00052934),
            ("Aracely", 0.00052890),
            ("Gilda", 0.00052723),
            ("Pascale", 0.00052602),
            ("Clementina", 0.00052457),
            ("Luzmira", 0.00052336),
            ("Yenny", 0.00052302),
            ("Margot", 0.00051859),
            ("Natalie", 0.00051505),
            ("Mía", 0.00051482),
            ("Yenifer", 0.00051416),
            ("Bianca", 0.00050441),
            ("Cinthia", 0.00050341),
            ("Rafaella", 0.00050053),
            ("Maura", 0.00049898),
            ("Claudina", 0.00049599),
            ("Melanie", 0.00049222),
            ("Daisy", 0.00049100),
            ("Erna", 0.00048114),
            ("Sabina", 0.00047803),
            ("Scarlet", 0.00047205),
            ("Nathaly", 0.00046850),
            ("Mirna", 0.00046773),
            ("Nilda", 0.00046751),
            ("Lina", 0.00046673),
            ("Ada", 0.00046596),
            ("Makarena", 0.00045909),
            ("Astrid", 0.00045753),
            ("Gina", 0.00045720),
            ("Celinda", 0.00045676),
            ("Leontina", 0.00045388),
            ("Jenifer", 0.00045078),
            ("Marilyn", 0.00044834),
            ("Yohana", 0.00044701),
            ("Grace", 0.00044668),
            ("Ashley", 0.00044479),
            ("Janet", 0.00044479),
            ("Ninoska", 0.00044379),
            ("Anahis", 0.00044280),
            ("Teresita", 0.00044280),
            ("Adelina", 0.00044246),
            ("Elcira", 0.00044246),
            ("Pabla", 0.00044158),
            ("Maricel", 0.00044058),
            ("Elisabeth", 0.00043981),
            ("Jovita", 0.00043881),
            ("Caroline", 0.00043859),
            ("Nathalie", 0.00043792),
            ("Isolina", 0.00043061),
            ("Delfina", 0.00043016),
            ("Angie", 0.00042850),
            ("Fiorella", 0.00042130),
            ("Dora", 0.00041975),
            ("Giselle", 0.00041676),
            ("Yanet", 0.00041310),
            ("Yoselin", 0.00041299),
            ("Alice", 0.00041077),
            ("Edita", 0.00041044),
            ("Fabiana", 0.00041000),
            ("Nayareth", 0.00040933),
            ("Genoveva", 0.00040678),
            ("Helen", 0.00040590),
            ("Vivian", 0.00040390),
            ("Lucrecia", 0.00040246),
            ("Herminda", 0.00040213),
            ("Luna", 0.00040113),
            ("Scarleth", 0.00040113),
            ("Monica", 0.00040036),
            ("Marion", 0.00039969),
            ("Orfelina", 0.00039659),
            ("Digna", 0.00039426),
            ("Yasmín", 0.00039382),
            ("Marcelina", 0.00039127),
            ("Lisette", 0.00039061),
            ("Linda", 0.00038939),
            ("Katherinne", 0.00038928),
            ("Amy", 0.00038894),
            ("Nidia", 0.00038551),
            ("Ivette", 0.00038418),
            ("Yanira", 0.00038407),
            ("Milena", 0.00038096),
            ("Emelina", 0.00037897),
            ("Flora", 0.00037831),
            ("Estefany", 0.00037786),
            ("Esmeralda", 0.00037509),
            ("Francia", 0.00037487),
            ("Vanesa", 0.00036423),
            ("Araceli", 0.00036346),
            ("Edelmira", 0.00036335),
            ("Yanina", 0.00036324),
            ("Helena", 0.00036091),
            ("Darling", 0.00035936),
            ("Clorinda", 0.00035814),
            ("Betty", 0.00035747),
            ("Veronica", 0.00035747),
            ("Juliana", 0.00035603),
            ("Tabita", 0.00035348),
            ("Jeniffer", 0.00035171),
            ("Otilia", 0.00035094),
            ("Nieves", 0.00034938),
            ("Amaya", 0.00034916),
            ("Esther", 0.00034839),
            ("Leyla", 0.00034828),
            ("Maricela", 0.00034794),
            ("Alejandrina", 0.00034761),
            ("Jenniffer", 0.00034728),
            ("Rose", 0.00034584),
            ("Jacinta", 0.00034362),
            ("Albertina", 0.00033997),
            ("Lucinda", 0.00033808),
            ("Aurelia", 0.00033708),
            ("Juanita", 0.00033697),
            ("Rosalba", 0.00033664),
            ("Adelaida", 0.00033199),
            ("Denise", 0.00033154),
            ("Mery", 0.00033121),
            ("Alexia", 0.00033066),
            ("Enriqueta", 0.00032955),
            ("Katia", 0.00032933),
            ("Nélida", 0.00032922),
            ("Evelin", 0.00032722),
            ("Brígida", 0.00032645),
            ("Dolores", 0.00032545),
            ("Anna", 0.00032467),
            ("Florinda", 0.00032013),
            ("Gricelda", 0.00031836),
            ("América", 0.00031736),
            ("Doralisa", 0.00031703),
            ("Ramona", 0.00031603),
            ("Cinthya", 0.00031470),
            ("Gisselle", 0.00031381),
            ("Yesica", 0.00031381),
            ("Scarlette", 0.00031370),
            ("Úrsula", 0.00031326),
            ("Daniella", 0.00031248),
            ("Alma", 0.00031204),
            ("Clarisa", 0.00030916),
            ("Deyanira", 0.00030905),
            ("Amada", 0.00030872),
            ("Karol", 0.00030816),
            ("Kelly", 0.00030761),
            ("Leidy", 0.00030683),
            ("Yuliana", 0.00030650),
            ("Lourdes", 0.00030440),
            ("Flavia", 0.00030318),
            ("Natacha", 0.00030185),
            ("Lorenza", 0.00029830),
            ("Marisel", 0.00029819),
            ("Rocio", 0.00029764),
            ("Clotilde", 0.00029675),
            ("Ariela", 0.00029664),
            ("Marisa", 0.00029631),
            ("Nayaret", 0.00029608),
            ("Soraya", 0.00029608),
            ("Antonieta", 0.00029431),
            ("Ruby", 0.00029110),
            ("Melany", 0.00029065),
            ("Magali", 0.00028977),
            ("Barbara", 0.00028777),
            ("Yamilet", 0.00028556),
            ("Anastasia", 0.00028511),
            ("Elia", 0.00028434),
            ("Lesly", 0.00028412),
            ("Deisy", 0.00028367),
            ("Milagros", 0.00028013),
            ("Jael", 0.00027924),
            ("Florentina", 0.00027880),
            ("Katerine", 0.00027791),
            ("Madeleine", 0.00027758),
            ("Ayelén", 0.00027658),
            ("Francis", 0.00027547),
            ("Wilma", 0.00027525),
            ("Mariluz", 0.00027492),
            ("Natali", 0.00027381),
            ("Nury", 0.00027359),
            ("Giuliana", 0.00027337),
            ("Gema", 0.00027315),
            ("Massiel", 0.00027293),
            ("Rachel", 0.00027270),
            ("Paulette", 0.00027248),
            ("Micaela", 0.00027137),
            ("Dania", 0.00026905),
            ("Natividad", 0.00026849),
            ("Yocelyn", 0.00026783),
            ("Yanara", 0.00026528),
            ("Katherin", 0.00026473),
            ("Sarah", 0.00026461),
            ("Melania", 0.00026439),
            ("Sarai", 0.00026384),
            ("Perla", 0.00026207),
            ("Sabrina", 0.00026118),
            ("Muriel", 0.00026007),
            ("Cintia", 0.00025985),
        ]
    )

    @property
    def first_names(self):
        """Returns a list of weighted first names, male and female."""
        if not hasattr(self, "_first_names"):
            self._first_names = OrderedDict()
            for a, b in zip_longest(self.first_names_male.items(), self.first_names_female.items()):
                if a is not None:
                    name, weight = a
                    self._first_names[name] = weight / 2
                if b is not None:
                    name, weight = b
                    self._first_names[name] = weight / 2
        return self._first_names

    # 500 last names, weighted
    last_names = OrderedDict(
        [
            ("González", 0.02683604),
            ("Muñoz", 0.02047480),
            ("Rojas", 0.01508949),
            ("Díaz", 0.01491392),
            ("Pérez", 0.01227842),
            ("Soto", 0.01044305),
            ("Rodríguez", 0.00997861),
            ("Contreras", 0.00993588),
            ("Silva", 0.00932900),
            ("López", 0.00920382),
            ("Morales", 0.00901722),
            ("Sepúlveda", 0.00880392),
            ("Martínez", 0.00870346),
            ("Hernández", 0.00867623),
            ("Torres", 0.00844247),
            ("Flores", 0.00836659),
            ("Ramírez", 0.00809392),
            ("Fuentes", 0.00808812),
            ("Castillo", 0.00801363),
            ("Espinoza", 0.00788287),
            ("Araya", 0.00787643),
            ("Reyes", 0.00758987),
            ("Gutiérrez", 0.00753243),
            ("Valenzuela", 0.00751303),
            ("Castro", 0.00732126),
            ("Vargas", 0.00724265),
            ("Sánchez", 0.00722920),
            ("Vásquez", 0.00699836),
            ("Fernández", 0.00677539),
            ("Álvarez", 0.00659731),
            ("Gómez", 0.00658808),
            ("Tapia", 0.00631937),
            ("Herrera", 0.00623804),
            ("Cortés", 0.00613157),
            ("García", 0.00612128),
            ("Carrasco", 0.00605067),
            ("Núñez", 0.00597788),
            ("Jara", 0.00568990),
            ("Vergara", 0.00543105),
            ("Rivera", 0.00538544),
            ("Figueroa", 0.00513368),
            ("Riquelme", 0.00501507),
            ("Bravo", 0.00496506),
            ("Miranda", 0.00492273),
            ("Vera", 0.00488902),
            ("Molina", 0.00478491),
            ("Vega", 0.00463878),
            ("Sandoval", 0.00456813),
            ("Campos", 0.00453386),
            ("Ortiz", 0.00437677),
            ("Orellana", 0.00435350),
            ("Salazar", 0.00429255),
            ("Zúñiga", 0.00426568),
            ("Olivares", 0.00425670),
            ("Romero", 0.00414512),
            ("Gallardo", 0.00413093),
            ("Garrido", 0.00407209),
            ("Alarcón", 0.00407085),
            ("Guzmán", 0.00403413),
            ("Parra", 0.00390092),
            ("Saavedra", 0.00387443),
            ("Peña", 0.00387328),
            ("Aguilera", 0.00384177),
            ("Navarro", 0.00382743),
            ("Henríquez", 0.00381134),
            ("Cáceres", 0.00371244),
            ("Pizarro", 0.00370441),
            ("Godoy", 0.00367051),
            ("Aravena", 0.00365821),
            ("Jiménez", 0.00359039),
            ("Escobar", 0.00355175),
            ("Ruiz", 0.00353889),
            ("Leiva", 0.00348804),
            ("Medina", 0.00344091),
            ("Vidal", 0.00337984),
            ("Cárdenas", 0.00335514),
            ("Yáñez", 0.00334424),
            ("Salinas", 0.00333792),
            ("Valdés", 0.00333438),
            ("Moreno", 0.00325766),
            ("Lagos", 0.00318407),
            ("Maldonado", 0.00318255),
            ("Bustos", 0.00308706),
            ("Pino", 0.00302189),
            ("Carvajal", 0.00294762),
            ("Palma", 0.00294040),
            ("Alvarado", 0.00291871),
            ("Ortega", 0.00289513),
            ("Sanhueza", 0.00287199),
            ("Navarrete", 0.00286994),
            ("Guerrero", 0.00285879),
            ("Ramos", 0.00285476),
            ("Paredes", 0.00283341),
            ("Sáez", 0.00282436),
            ("Bustamante", 0.00280019),
            ("Toro", 0.00279548),
            ("Poblete", 0.00277637),
            ("Mora", 0.00274113),
            ("Donoso", 0.00272059),
            ("Velásquez", 0.00271278),
            ("Venegas", 0.00270150),
            ("Acuña", 0.00267882),
            ("Pinto", 0.00267108),
            ("Acevedo", 0.00266916),
            ("Toledo", 0.00262872),
            ("Quezada", 0.00261595),
            ("Farías", 0.00260009),
            ("Aguilar", 0.00259665),
            ("San Martín", 0.00259182),
            ("Arriagada", 0.00259178),
            ("Rivas", 0.00255249),
            ("Cerda", 0.00253610),
            ("Salas", 0.00250877),
            ("Cornejo", 0.00250865),
            ("Arias", 0.00247106),
            ("Cabrera", 0.00245006),
            ("Durán", 0.00244504),
            ("Hidalgo", 0.00242676),
            ("Arancibia", 0.00242276),
            ("Marín", 0.00240593),
            ("Méndez", 0.00239469),
            ("Troncoso", 0.00234412),
            ("Osorio", 0.00234024),
            ("Ulloa", 0.00232537),
            ("Inostroza", 0.00231406),
            ("Villarroel", 0.00231381),
            ("Delgado", 0.00228236),
            ("Cuevas", 0.00227765),
            ("Ríos", 0.00226799),
            ("Pacheco", 0.00225965),
            ("Calderón", 0.00225919),
            ("Lara", 0.00224862),
            ("Ojeda", 0.00223799),
            ("León", 0.00220174),
            ("Correa", 0.00219774),
            ("Villalobos", 0.00215563),
            ("Ponce", 0.00212502),
            ("Barrera", 0.00209673),
            ("Burgos", 0.00209540),
            ("Chávez", 0.00209403),
            ("Cifuentes", 0.00208313),
            ("Catalán", 0.00208213),
            ("Moya", 0.00206590),
            ("Concha", 0.00201908),
            ("Ávila", 0.00200483),
            ("Zapata", 0.00199565),
            ("Guerra", 0.00197511),
            ("Salgado", 0.00195438),
            ("Barría", 0.00193901),
            ("Alfaro", 0.00191432),
            ("Gajardo", 0.00189681),
            ("Uribe", 0.00188327),
            ("Meza", 0.00185182),
            ("Astudillo", 0.00183289),
            ("Aguirre", 0.00182031),
            ("Cruz", 0.00181786),
            ("Becerra", 0.00180856),
            ("Retamal", 0.00180751),
            ("Mendoza", 0.00179192),
            ("Neira", 0.00178706),
            ("Pereira", 0.00178309),
            ("Ahumada", 0.00176419),
            ("Villegas", 0.00175511),
            ("Valdebenito", 0.00173854),
            ("Pavez", 0.00173026),
            ("Barrientos", 0.00170380),
            ("Jorquera", 0.00169141),
            ("Moraga", 0.00168413),
            ("Cárcamo", 0.00167957),
            ("Valencia", 0.00167161),
            ("Gálvez", 0.00166746),
            ("Lobos", 0.00166690),
            ("Barraza", 0.00165862),
            ("Canales", 0.00165701),
            ("Guajardo", 0.00165624),
            ("Araneda", 0.00164477),
            ("Mansilla", 0.00162051),
            ("Urrutia", 0.00160508),
            ("Mancilla", 0.00159963),
            ("Abarca", 0.00159944),
            ("Andrade", 0.00158767),
            ("Quiroz", 0.00158624),
            ("Valdivia", 0.00158485),
            ("Ibarra", 0.00158271),
            ("Mella", 0.00157726),
            ("Gatica", 0.00157255),
            ("Leal", 0.00156976),
            ("Cid", 0.00154797),
            ("Mardones", 0.00152328),
            ("Riveros", 0.00152269),
            ("Albornoz", 0.00151925),
            ("Cisternas", 0.00151761),
            ("Vallejos", 0.00151693),
            ("Solís", 0.00150807),
            ("Baeza", 0.00150525),
            ("Gaete", 0.00147643),
            ("Fuentealba", 0.00147544),
            ("Manríquez", 0.00147026),
            ("Córdova", 0.00146422),
            ("Rebolledo", 0.00145805),
            ("Caro", 0.00145344),
            ("Suárez", 0.00143779),
            ("Carrillo", 0.00142716),
            ("Carreño", 0.00140997),
            ("Cofré", 0.00140222),
            ("Oyarzún", 0.00140036),
            ("Varas", 0.00138394),
            ("Santibáñez", 0.00136064),
            ("Barra", 0.00136061),
            ("Márquez", 0.00135707),
            ("Fuenzalida", 0.00131692),
            ("Zamora", 0.00131596),
            ("Arenas", 0.00131267),
            ("Opazo", 0.00130920),
            ("Cabezas", 0.00130372),
            ("Pardo", 0.00127540),
            ("Vilches", 0.00126641),
            ("Santander", 0.00126170),
            ("Berríos", 0.00124955),
            ("Roa", 0.00124847),
            ("Véliz", 0.00123772),
            ("Arévalo", 0.00122129),
            ("Rubio", 0.00120847),
            ("Montecinos", 0.00120057),
            ("Robles", 0.00119641),
            ("Plaza", 0.00119366),
            ("Ibáñez", 0.00119093),
            ("Parada", 0.00117860),
            ("Meneses", 0.00117822),
            ("Briones", 0.00117429),
            ("Mena", 0.00117398),
            ("Huerta", 0.00116162),
            ("Román", 0.00115523),
            ("Zamorano", 0.00114932),
            ("Mamani", 0.00113704),
            ("Rosales", 0.00113646),
            ("Peralta", 0.00112319),
            ("Cancino", 0.00111678),
            ("Faúndez", 0.00111285),
            ("Maturana", 0.00111164),
            ("Beltrán", 0.00110835),
            ("Oyarzo", 0.00110764),
            ("Jaramillo", 0.00110631),
            ("Jofré", 0.00110141),
            ("Tobar", 0.00109837),
            ("Aguayo", 0.00109791),
            ("Palacios", 0.00109289),
            ("Avendaño", 0.00108908),
            ("Galaz", 0.00108412),
            ("Gallegos", 0.00107582),
            ("Urra", 0.00107492),
            ("Zambrano", 0.00106761),
            ("Ayala", 0.00106246),
            ("Cortez", 0.00105490),
            ("Santana", 0.00105177),
            ("Olguín", 0.00104610),
            ("Riffo", 0.00104121),
            ("Astorga", 0.00103681),
            ("Garcés", 0.00103603),
            ("Villanueva", 0.00103454),
            ("Hermosilla", 0.00102636),
            ("Marchant", 0.00102556),
            ("Arce", 0.00101592),
            ("Bastías", 0.00101118),
            ("Galleguillos", 0.00100511),
            ("Suazo", 0.00100378),
            ("Monsalve", 0.00099612),
            ("Rubilar", 0.00098757),
            ("Lillo", 0.00098546),
            ("Padilla", 0.00098472),
            ("Candia", 0.00098237),
            ("Quintana", 0.00098128),
            ("Almonacid", 0.00097657),
            ("Lizama", 0.00096650),
            ("Cabello", 0.00096566),
            ("Espinosa", 0.00096337),
            ("Duarte", 0.00095256),
            ("Osses", 0.00094444),
            ("Cartes", 0.00094150),
            ("Barrios", 0.00093806),
            ("Loyola", 0.00093697),
            ("Novoa", 0.00093524),
            ("Seguel", 0.00093452),
            ("Norambuena", 0.00093397),
            ("Mellado", 0.00093307),
            ("Serrano", 0.00092513),
            ("Leyton", 0.00091829),
            ("Carmona", 0.00091801),
            ("Montenegro", 0.00091004),
            ("Segovia", 0.00090726),
            ("Cea", 0.00088448),
            ("Benavides", 0.00088352),
            ("Hormazábal", 0.00088324),
            ("Verdugo", 0.00088157),
            ("Jerez", 0.00087726),
            ("Martinez", 0.00087525),
            ("Mondaca", 0.00087385),
            ("Segura", 0.00087376),
            ("Pastén", 0.00086416),
            ("Oliva", 0.00085762),
            ("Cordero", 0.00085374),
            ("Aranda", 0.00084897),
            ("Céspedes", 0.00084814),
            ("Urbina", 0.00084485),
            ("Briceño", 0.00084439),
            ("Luna", 0.00083924),
            ("Matus", 0.00083599),
            ("Cisterna", 0.00083484),
            ("Varela", 0.00083373),
            ("Echeverría", 0.00083342),
            ("Aedo", 0.00082765),
            ("Bahamondes", 0.00082669),
            ("Altamirano", 0.00082598),
            ("Merino", 0.00082487),
            ("Arellano", 0.00082462),
            ("Matamala", 0.00082121),
            ("Elgueta", 0.00081083),
            ("Hurtado", 0.00081043),
            ("Brito", 0.00080209),
            ("Barahona", 0.00079001),
            ("Valderrama", 0.00078669),
            ("Madrid", 0.00078592),
            ("Estay", 0.00078471),
            ("Aburto", 0.00078080),
            ("Bórquez", 0.00077910),
            ("Acosta", 0.00077774),
            ("Órdenes", 0.00077433),
            ("Fierro", 0.00077414),
            ("Domínguez", 0.00077262),
            ("Lizana", 0.00076764),
            ("Villagra", 0.00076584),
            ("Alegría", 0.00076534),
            ("Maureira", 0.00075208),
            ("Urzúa", 0.00075118),
            ("Oyarce", 0.00074914),
            ("Trujillo", 0.00074390),
            ("Olave", 0.00074362),
            ("Ferrada", 0.00074062),
            ("Rosas", 0.00073020),
            ("Bugueño", 0.00072636),
            ("Vivanco", 0.00072540),
            ("Lorca", 0.00072113),
            ("Rozas", 0.00072075),
            ("Montero", 0.00072035),
            ("Águila", 0.00071803),
            ("Montoya", 0.00071493),
            ("Zepeda", 0.00071261),
            ("Vicencio", 0.00071137),
            ("Garay", 0.00069454),
            ("Gamboa", 0.00069389),
            ("Lazo", 0.00069274),
            ("Aliaga", 0.00069215),
            ("Villagrán", 0.00068574),
            ("Aros", 0.00068193),
            ("Aránguiz", 0.00068044),
            ("Baez", 0.00067759),
            ("Pozo", 0.00067759),
            ("Belmar", 0.00067734),
            ("Casanova", 0.00066929),
            ("Bernal", 0.00066644),
            ("Machuca", 0.00066572),
            ("Escalona", 0.00066507),
            ("Ávalos", 0.00066461),
            ("Quinteros", 0.00066039),
            ("Collao", 0.00065640),
            ("Letelier", 0.00064540),
            ("Quispe", 0.00064078),
            ("Marambio", 0.00063951),
            ("Mejías", 0.00063561),
            ("Saldivia", 0.00063496),
            ("Armijo", 0.00063393),
            ("Orrego", 0.00063127),
            ("Piña", 0.00062780),
            ("Chacón", 0.00062674),
            ("Bello", 0.00062597),
            ("Rocha", 0.00062355),
            ("Pinilla", 0.00062318),
            ("Parraguez", 0.00061441),
            ("Oñate", 0.00060908),
            ("Iturra", 0.00060459),
            ("Arredondo", 0.00060270),
            ("Fredes", 0.00060217),
            ("Jaque", 0.00059945),
            ("Blanco", 0.00059935),
            ("Chamorro", 0.00059864),
            ("Quiroga", 0.00059483),
            ("Chandía", 0.00059424),
            ("Ceballos", 0.00059158),
            ("Saldías", 0.00059148),
            ("Barros", 0.00058888),
            ("Llanos", 0.00058866),
            ("Benítez", 0.00058522),
            ("Peñaloza", 0.00058491),
            ("Páez", 0.00058426),
            ("Pulgar", 0.00058302),
            ("Melo", 0.00058290),
            ("Ruz", 0.00057822),
            ("Medel", 0.00057689),
            ("Ampuero", 0.00057673),
            ("Avilés", 0.00057590),
            ("Pincheira", 0.00057351),
            ("Bascuñán", 0.00057302),
            ("Azócar", 0.00057168),
            ("Villa", 0.00057078),
            ("Tello", 0.00057047),
            ("Luengo", 0.00056787),
            ("Ovalle", 0.00056645),
            ("Madariaga", 0.00056164),
            ("Celis", 0.00056130),
            ("Cubillos", 0.00055932),
            ("Prado", 0.00055635),
            ("Angulo", 0.00055579),
            ("Estrada", 0.00055418),
            ("Arroyo", 0.00055303),
            ("Mercado", 0.00054947),
            ("Castañeda", 0.00054829),
            ("Barriga", 0.00054575),
            ("Lucero", 0.00054559),
            ("Valladares", 0.00054274),
            ("Coronado", 0.00053983),
            ("Pineda", 0.00053896),
            ("Rojo", 0.00053760),
            ("Ibacache", 0.00053747),
            ("Quijada", 0.00053639),
            ("Bahamonde", 0.00052744),
            ("Zurita", 0.00052424),
            ("Salamanca", 0.00051517),
            ("Galdames", 0.00051507),
            ("Ferreira", 0.00051433),
            ("Santos", 0.00051231),
            ("Labra", 0.00051173),
            ("Naranjo", 0.00051021),
            ("Badilla", 0.00051011),
            ("Veloso", 0.00050866),
            ("Prieto", 0.00050785),
            ("Villar", 0.00050785),
            ("Ormeño", 0.00050776),
            ("Ossandón", 0.00050754),
            ("Lira", 0.00050624),
            ("Bobadilla", 0.00050571),
            ("Apablaza", 0.00050395),
            ("Cepeda", 0.00050252),
            ("Paz", 0.00050252),
            ("Sierra", 0.00049617),
            ("Esparza", 0.00049574),
            ("Zavala", 0.00049530),
            ("Quintanilla", 0.00049459),
            ("Veas", 0.00049134),
            ("Sobarzo", 0.00048920),
            ("Videla", 0.00048811),
            ("Fonseca", 0.00047584),
            ("Toloza", 0.00047113),
            ("Agüero", 0.00046766),
            ("Olmos", 0.00046568),
            ("Arteaga", 0.00046562),
            ("Allende", 0.00046472),
            ("Montecino", 0.00046395),
            ("Quiñones", 0.00045976),
            ("Agurto", 0.00045958),
            ("Zárate", 0.00045933),
            ("Villablanca", 0.00045911),
            ("Guevara", 0.00045679),
            ("Solar", 0.00045577),
            ("Cruces", 0.00045391),
            ("Retamales", 0.00045140),
            ("Alvarez", 0.00045037),
            ("Astete", 0.00044954),
            ("De La Fuente", 0.00044650),
            ("Aracena", 0.00043996),
            ("Alvear", 0.00043910),
            ("Millán", 0.00043160),
            ("Zenteno", 0.00043135),
            ("Erices", 0.00043101),
            ("Meléndez", 0.00043064),
            ("Carrera", 0.00042884),
            ("Olea", 0.00042800),
            ("Cavieres", 0.00042779),
            ("Moncada", 0.00042583),
            ("Cares", 0.00042565),
            ("Vejar", 0.00042546),
            ("Arcos", 0.00042432),
            ("Montes", 0.00042150),
            ("Encina", 0.00041985),
            ("Fica", 0.00041784),
            ("Inzunza", 0.00041641),
            ("Droguett", 0.00041195),
            ("Caballero", 0.00041127),
            ("Lazcano", 0.00040950),
            ("Bruna", 0.00040805),
            ("Olmedo", 0.00040802),
            ("Corvalán", 0.00040634),
            ("Morán", 0.00040365),
            ("Olate", 0.00040114),
            ("Allendes", 0.00039928),
            ("Saldaña", 0.00039903),
            ("Viveros", 0.00039723),
            ("Moyano", 0.00039609),
            ("Choque", 0.00039550),
            ("Dinamarca", 0.00039107),
            ("Adasme", 0.00039098),
        ]
    )

    prefixes_male = ("Sr.", "Dr.", "Don")
    prefixes_female = ("Srta.", "Sra.", "Dra.", "Doña")

    def name(self) -> str:
        # Select format, then generate name
        format: str = self.random_element(self.formats)
        pattern: str = self.random_element(getattr(self, format))
        return self.generator.parse(pattern)

    def given_name(self) -> str:
        """Generates a composite given name with two unique names"""
        if self.random_int(0, 1) == 1:
            source = self.first_names_female
        else:
            source = self.first_names_male
        names = self.random_elements(source, length=2, unique=True)  # type: ignore[var-annotated]
        return " ".join(names)

    def given_name_male(self) -> str:
        """Generates a composite male given name with two unique names"""
        names = self.random_elements(self.first_names_male, length=2, unique=True)  # type: ignore[var-annotated]
        return " ".join(names)

    def given_name_female(self) -> str:
        """Generates a composite female given name with two unique names"""
        names = self.random_elements(self.first_names_female, length=2, unique=True)  # type: ignore[var-annotated]
        return " ".join(names)
