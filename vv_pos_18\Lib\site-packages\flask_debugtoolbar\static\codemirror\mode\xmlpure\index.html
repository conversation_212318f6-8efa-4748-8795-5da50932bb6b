<!doctype html>
<html>
  <head>
    <title>CodeMirror: Pure XML mode</title>
    <link rel="stylesheet" href="../../lib/codemirror.css">
    <script src="../../lib/codemirror.js"></script>
    <script src="xmlpure.js"></script>
    <style type="text/css">.CodeMirror {border-top: 1px solid black; border-bottom: 1px solid black;}</style>
    <link rel="stylesheet" href="../../doc/docs.css">
  </head>
  <body>
    <h1>CodeMirror: XML mode</h1>
    <form><textarea id="code" name="code">
&lt;?xml version="1.0" encoding="UTF-8" standalone="no" ?&gt;

&lt;!-- This is the pure XML mode,
and we're inside a comment! --&gt;

&lt;catalog&gt;
  &lt;books&gt;
    &lt;book id="bk01"&gt;
      &lt;title&gt;Lord of Light&lt;/title&gt;
      &lt;author&gt;<PERSON>&lt;/author&gt;
      &lt;year&gt;1967&lt;/year&gt;
      &lt;description&gt;&lt;![CDATA[This is a great book, really!!]]&gt;&lt;/description&gt;
    &lt;/book&gt;
  &lt;/books&gt;
&lt;/catalog&gt;
</textarea></form>
    <script>
      var editor = CodeMirror.fromTextArea(document.getElementById("code"), {mode: {name: "xmlpure"}});
    </script>

    <p>This is my XML parser, based on the original:</p> 
    <ul> 
    	<li>No html mode - this is pure xml</li> 
    	<li>Illegal attributes and element names are errors</li> 
    	<li>Attributes must have a value</li> 
    	<li>XML declaration supported (e.g.: <b>&lt;?xml version="1.0" encoding="utf-8" standalone="no" ?&gt;</b>)</li> 
    	<li>CDATA and comment blocks are not indented (except for their start-tag)</li> 
    	<li>Better handling of errors per line with the state object - provides good infrastructure for extending it</li> 
    </ul> 
 
    <p>What's missing:</p> 
    <ul> 
    	<li>Make sure only a single root element exists at the document level</li> 
    	<li>Multi-line attributes should NOT indent</li>
    	<li>Start tags are not painted red when they have no matching end tags (is this really wrong?)</li> 
    </ul> 
 
    <p><strong>MIME types defined:</strong> <code>application/xml</code>, <code>text/xml</code>.</p> 
 
    <p><b>@author</b>: Dror BG (<i>deebug.dev[at]gmail.com</i>)<br/> 
    <p><b>@date</b>: August, 2011<br/> 
    <p><b>@github</b>: <a href='https://github.com/deebugger/CodeMirror2' target='blank'>https://github.com/deebugger/CodeMirror2</a></p>

  </body>
</html>
