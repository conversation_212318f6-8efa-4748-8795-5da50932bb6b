{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-mortar-pestle"></i>
            Ingrédients
        </h1>
        <div>
            <button type="button" class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#stockUpdateModal">
                <i class="fas fa-boxes"></i>
                Mise à jour stock
            </button>
            <a href="{{ url_for('inventory.add_ingredient') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i>
                Nouvel ingrédient
            </a>
        </div>
    </div>

    <!-- Filters -->
    <div class="card shadow mb-4">
        <div class="card-body">
            <div class="row align-items-center">
                <div class="col-md-3">
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-search"></i>
                        </span>
                        <input type="text" class="form-control" id="search-input" placeholder="Rechercher...">
                    </div>
                </div>
                <div class="col-md-3">
                    <select class="form-select" id="category-filter">
                        <option value="">Toutes les catégories</option>
                        {% for category in categories %}
                        <option value="{{ category.id }}">{{ category.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <select class="form-select" id="stock-filter">
                        <option value="">Tous les stocks</option>
                        <option value="low">Stock bas</option>
                        <option value="out">Rupture de stock</option>
                        <option value="expiring">Bientôt périmé</option>
                    </select>
                </div>
            </div>
        </div>
    </div>

    <!-- Ingredients Table -->
    <div class="card shadow">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover" style="background-color: #F3D77F;">
                    <thead>
                        <tr>
                            <th style="width: 80px"></th>
                            <th>Ingrédient</th>
                            <th>Catégorie</th>
                            <th>Prix référentiel</th>
                            <th>Coût moyen</th>
                            <th>Stock</th>
                            <th>Stock min.</th>
                            <th>Expiration</th>
                            <th style="width: 100px">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for ingredient in ingredients %}
                        <tr>
                            <td>
                                {% if ingredient.image_path %}
                                <img src="{{ url_for('static', filename=ingredient.image_path) }}" class="img-thumbnail" alt="{{ ingredient.name }}" style="width: 50px; height: 50px; object-fit: cover;">
                                {% else %}
                                <div class="bg-light text-center rounded" style="width: 50px; height: 50px; line-height: 50px;">
                                    <i class="fas fa-mortar-pestle text-muted"></i>
                                </div>
                                {% endif %}
                            </td>
                            <td>
                                <strong>{{ ingredient.name }}</strong>
                                {% if ingredient.description %}
                                <br>
                                <small class="text-muted">{{ ingredient.description|truncate(50) }}</small>
                                {% endif %}
                            </td>
                            <td>{{ ingredient.category.name }}</td>
                            <td>
                                <span class="text-info">{{ "%.2f"|format(ingredient.price_per_unit) }} €/{{ ingredient.unit }}</span>
                            </td>
                            <td>
                                {% if ingredient.average_cost and (ingredient.average_cost or 0) > 0 %}
                                    <span class="text-primary">{{ "%.2f"|format(ingredient.average_cost) }} €/{{ ingredient.unit }}</span>
                                    {% if ingredient.price_per_unit and ingredient.average_cost != ingredient.price_per_unit %}
                                        <br><small class="text-warning">
                                            <i class="fas fa-chart-line"></i> Moyen
                                        </small>
                                    {% endif %}
                                {% elif ingredient.price_per_unit %}
                                    <span class="text-muted">{{ "%.2f"|format(ingredient.price_per_unit) }} €/{{ ingredient.unit }}</span>
                                    <br><small class="text-muted">Référentiel</small>
                                {% else %}
                                    <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td>
                                {{ ingredient.stock_quantity }} {{ ingredient.unit }}
                                {% if ingredient.stock_quantity <= ingredient.minimum_stock %}
                                <br>
                                <span class="badge bg-warning">Stock bas</span>
                                {% endif %}
                            </td>
                            <td>{{ ingredient.minimum_stock }} {{ ingredient.unit }}</td>
                            <td>
                                {% if ingredient.expiry_date %}
                                {{ ingredient.expiry_date.strftime('%d/%m/%Y') }}
                                {% if ingredient.expiry_date <= today %}
                                <br>
                                <span class="badge bg-danger">Périmé</span>
                                {% elif ingredient.expiry_date <= expiring_soon %}
                                <br>
                                <span class="badge bg-warning">Bientôt périmé</span>
                                {% endif %}
                                {% else %}
                                -
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group">
                                    <button type="button" class="btn btn-outline-primary btn-sm" 
                                            data-bs-toggle="modal" 
                                            data-bs-target="#quickStockModal"
                                            data-ingredient-id="{{ ingredient.id }}"
                                            data-ingredient-name="{{ ingredient.name }}"
                                            data-ingredient-unit="{{ ingredient.unit }}"
                                            title="Stock rapide">
                                        <i class="fas fa-boxes"></i>
                                    </button>
                                    <a href="{{ url_for('inventory.edit_ingredient', id=ingredient.id) }}" class="btn btn-outline-primary btn-sm" title="Modifier">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button type="button" class="btn btn-outline-danger btn-sm" data-bs-toggle="modal" data-bs-target="#deleteModal{{ ingredient.id }}" title="Supprimer">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>

                        <!-- Delete Modal -->
                        <div class="modal fade" id="deleteModal{{ ingredient.id }}" tabindex="-1">
                            <div class="modal-dialog">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title">Confirmer la suppression</h5>
                                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                    </div>
                                    <div class="modal-body">
                                        <p>Êtes-vous sûr de vouloir supprimer l'ingrédient <strong>{{ ingredient.name }}</strong> ?</p>
                                        {% if ingredient.recipe_items.count() > 0 %}
                                        <div class="alert alert-warning">
                                            <i class="fas fa-exclamation-triangle"></i>
                                            Cet ingrédient est utilisé dans {{ ingredient.recipe_items.count() }} recette(s).
                                            La suppression affectera ces recettes.
                                        </div>
                                        {% endif %}
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                                        <form action="{{ url_for('inventory.delete_ingredient', id=ingredient.id) }}" method="POST" class="d-inline">
                                            <button type="submit" class="btn btn-danger">
                                                <i class="fas fa-trash"></i>
                                                Supprimer définitivement
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% else %}
                        <tr>
                            <td colspan="8" class="text-center py-4">
                                <div class="text-muted">
                                    <i class="fas fa-mortar-pestle fa-2x mb-3"></i>
                                    <p>Aucun ingrédient n'a été créé.</p>
                                    <a href="{{ url_for('inventory.add_ingredient') }}" class="btn btn-primary">
                                        <i class="fas fa-plus"></i>
                                        Ajouter un ingrédient
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Quick Stock Modal -->
<div class="modal fade" id="quickStockModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Mise à jour rapide du stock</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="quick-stock-form" method="POST" action="{{ url_for('reports.quick_stock_update') }}">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <input type="hidden" name="item_id" id="quick-stock-ingredient-id">
                    <input type="hidden" name="item_type" value="ingredient">
                    <p>Ingrédient: <strong id="quick-stock-ingredient-name"></strong></p>

                    <div class="mb-3">
                        <label class="form-label">Opération</label>
                        <div class="btn-group w-100" role="group">
                            <input type="radio" class="btn-check" name="movement_type" value="IN" id="add-op" checked>
                            <label class="btn btn-outline-success" for="add-op">
                                <i class="fas fa-plus"></i> Ajouter
                            </label>

                            <input type="radio" class="btn-check" name="movement_type" value="OUT" id="subtract-op">
                            <label class="btn btn-outline-danger" for="subtract-op">
                                <i class="fas fa-minus"></i> Retirer
                            </label>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Quantité</label>
                        <div class="input-group">
                            <input type="number" class="form-control" name="quantity" min="1" step="0.1" required>
                            <span class="input-group-text" id="quick-stock-unit"></span>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Note (optionnel)</label>
                        <textarea class="form-control" name="note" rows="2" placeholder="Motif de la modification..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="submit" form="quick-stock-form" class="btn btn-primary">
                    <i class="fas fa-save"></i>
                    Mettre à jour
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Batch Stock Update Modal -->
<div class="modal fade" id="stockUpdateModal" tabindex="-1" aria-labelledby="stockUpdateModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Mise à jour des stocks</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="batch-stock-form" method="POST" action="{{ url_for('inventory.update_batch_stock') }}">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Ingrédient</th>
                                    <th>Stock actuel</th>
                                    <th>Nouveau stock</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for ingredient in ingredients %}
                                <tr>
                                    <td>{{ ingredient.name }}</td>
                                    <td>{{ ingredient.stock_quantity }} {{ ingredient.unit }}</td>
                                    <td>
                                        <div class="input-group">
                                            <input type="number" class="form-control" 
                                                   name="stock_{{ ingredient.id }}" 
                                                   value="{{ ingredient.stock_quantity }}"
                                                   min="0" step="0.1">
                                            <span class="input-group-text">{{ ingredient.unit }}</span>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="submit" form="batch-stock-form" class="btn btn-primary">
                    <i class="fas fa-save"></i>
                    Enregistrer les modifications
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Pagination -->
<div class="row mt-4">
    <div class="col-12">
        <nav aria-label="Navigation des ingrédients">
            <ul class="pagination justify-content-center">
                {% if pagination.has_prev %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('inventory.ingredients', page=pagination.prev_num, per_page=per_page, search=search, category_id=selected_category, sort=sort_by, order=sort_order) }}">Précédent</a>
                </li>
                {% else %}
                <li class="page-item disabled">
                    <span class="page-link">Précédent</span>
                </li>
                {% endif %}
                
                {% for page_num in pagination.iter_pages(left_edge=2, right_edge=2, left_current=1, right_current=2) %}
                    {% if page_num %}
                        {% if page_num == pagination.page %}
                        <li class="page-item active">
                            <span class="page-link">{{ page_num }}</span>
                        </li>
                        {% else %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('inventory.ingredients', page=page_num, per_page=per_page, search=search, category_id=selected_category, sort=sort_by, order=sort_order) }}">{{ page_num }}</a>
                        </li>
                        {% endif %}
                    {% else %}
                    <li class="page-item disabled">
                        <span class="page-link">...</span>
                    </li>
                    {% endif %}
                {% endfor %}
                
                {% if pagination.has_next %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('inventory.ingredients', page=pagination.next_num, per_page=per_page, search=search, category_id=selected_category, sort=sort_by, order=sort_order) }}">Suivant</a>
                </li>
                {% else %}
                <li class="page-item disabled">
                    <span class="page-link">Suivant</span>
                </li>
                {% endif %}
            </ul>
        </nav>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Quick stock modal
    const quickStockModal = document.getElementById('quickStockModal');
    quickStockModal.addEventListener('show.bs.modal', function(event) {
        const button = event.relatedTarget;
        const ingredientId = button.dataset.ingredientId;
        const ingredientName = button.dataset.ingredientName;
        const ingredientUnit = button.dataset.ingredientUnit;
        
        document.getElementById('quick-stock-ingredient-id').value = ingredientId;
        document.getElementById('quick-stock-ingredient-name').textContent = ingredientName;
        document.getElementById('quick-stock-unit').textContent = ingredientUnit;
    });

    // Filters
    const searchInput = document.getElementById('search-input');
    const categoryFilter = document.getElementById('category-filter');
    const stockFilter = document.getElementById('stock-filter');
    const rows = document.querySelectorAll('tbody tr');

    function filterTable() {
        const searchTerm = searchInput.value.toLowerCase();
        const categoryId = categoryFilter.value;
        const stockStatus = stockFilter.value;

        rows.forEach(row => {
            let show = true;

            // Search filter
            if (searchTerm) {
                const text = row.textContent.toLowerCase();
                show = show && text.includes(searchTerm);
            }

            // Category filter
            if (categoryId) {
                const category = row.querySelector('td:nth-child(3)').textContent;
                show = show && category === categoryFilter.options[categoryFilter.selectedIndex].text;
            }

            // Stock filter
            if (stockStatus) {
                const hasLowStock = row.querySelector('.badge.bg-warning') !== null;
                const hasExpired = row.querySelector('.badge.bg-danger') !== null;
                const isExpiringSoon = row.querySelector('.badge.bg-warning') !== null;
                const stockCell = row.querySelector('td:nth-child(5)').textContent;
                const isOutOfStock = stockCell.includes('0');

                if (stockStatus === 'low') {
                    show = show && hasLowStock;
                } else if (stockStatus === 'out') {
                    show = show && isOutOfStock;
                } else if (stockStatus === 'expiring') {
                    show = show && (hasExpired || isExpiringSoon);
                }
            }

            row.style.display = show ? '' : 'none';
        });
    }

    searchInput.addEventListener('input', filterTable);
    categoryFilter.addEventListener('change', filterTable);
    stockFilter.addEventListener('change', filterTable);
});
</script>
{% endblock %} 