from typing import Tuple

from .. import Provider as PersonProvider


class Provider(PersonProvider):
    formats_female: Tuple[str, ...] = (
        "{{first_name_female}} {{last_name}}",
        "{{prefix_female}} {{first_name_female}} {{last_name}}",
    )

    formats_male: Tuple[str, ...] = (
        "{{first_name_male}} {{last_name}}",
        "{{prefix_male}} {{first_name_male}} {{last_name}}",
    )

    formats = formats_male + formats_female

    first_names_female: Tuple[str, ...] = (
        "آلاء",
        "آيات",
        "أجوان",
        "أحلام",
        "أروى",
        "أريج",
        "أزهار",
        "أسرار",
        "أسيل",
        "أغاريد",
        "أفراح",
        "أفنان",
        "ألين",
        "أناهيد",
        "إباء",
        "إخلاص",
        "إلينا",
        "ابتسام",
        "ابتكار",
        "ابتهاج",
        "ابتهال",
        "اصيل",
        "اعتكاف",
        "اعتماد",
        "افتكار",
        "ايمان",
        "بارعة",
        "باسمة",
        "باهرة",
        "بتلاء",
        "بتول",
        "بثينة",
        "بدرالدّجى",
        "بشرى",
        "بلسم",
        "بلقيس",
        "بلماء",
        "بلند",
        "بنان",
        "بنفسج",
        "بهاء",
        "بهجة",
        "بهية",
        "بوران",
        "بيسان",
        "بيلسان",
        "تالا",
        "تاليا",
        "ترانيم",
        "ترف",
        "تمام",
        "تولين",
        "جالا",
        "جلنار",
        "جمان",
        "جميلة",
        "جنى",
        "جهراء",
        "جوان",
        "جوانا",
        "جواهر",
        "جود",
        "جودي",
        "جوريّة",
        "جوليا",
        "جوين",
        "جيلان",
        "حلا",
        "حياة",
        "خاشعة",
        "دارين",
        "دانة",
        "دانية",
        "دعاء",
        "ديمه",
        "راما",
        "ربى",
        "رواء",
        "روبا",
        "روبين",
        "روعة",
        "روفيدا",
        "ريان",
        "ريتاج",
        "ريتال",
        "ريف",
        "ريفال",
        "ريم",
        "ريما",
        "ريمان",
        "ريناد",
        "زكية",
        "زهرة",
        "سبأ",
        "سجا",
        "سجى",
        "سديم",
        "سلاف",
        "سلسبيل",
        "شادن",
        "شهد",
        "ضحى",
        "ضياء",
        "عالية",
        "عتاب",
        "غوى",
        "غيداء",
        "فداء",
        "فرات",
        "فردوس",
        "كاملة",
        "كرمة",
        "كوثر",
        "لارا",
        "لاما",
        "لانا",
        "لتين",
        "لوجين",
        "لورا",
        "لورين",
        "لوليا",
        "ليان",
        "ليساء",
        "ليم",
        "لينا",
        "مادلين",
        "ماذى",
        "مايا",
        "مريم",
        "ميار",
        "ميرا",
        "ميرال",
        "ميسون",
        "ميلاء",
        "ناديه",
        "ناردين",
        "ناهد",
        "نشوة",
        "نغم",
        "نوال",
        "نوره",
        "نوف",
        "هاجر",
        "هايدي",
        "هدى",
        "هناء",
        "هنادي",
        "هند",
        "هيا",
        "هيام",
        "وصاف",
        "وفاء",
        "يارا",
        "ياسمين",
        "يسرى",
    )

    first_names_male: Tuple[str, ...] = (
        "أحمد",
        "تاج",
        "تاج الدّين",
        "تامر",
        "تحسين",
        "ترف",
        "تقي",
        "تقيّ الدّين",
        "تميم",
        "تمّام",
        "توفيق",
        "ثائر",
        "ثابت",
        "ثاقب",
        "ثامر",
        "ثروت",
        "ثقيف",
        "جابر",
        "جاد",
        "جاسم",
        "جدير",
        "جرير",
        "جرّاح",
        "جسور",
        "جعفر",
        "جلاء",
        "جلال",
        "جلال الدّين",
        "جليل",
        "جمال",
        "جمال الدّين",
        "جميل",
        "جهاد",
        "حاتم",
        "حارث",
        "حازم",
        "حافظ",
        "حامد",
        "حبّاب",
        "حسام",
        "حسن",
        "حسني",
        "حسنين",
        "حسيب",
        "حسين",
        "حفيظ",
        "حقّي",
        "حكيم",
        "حليم",
        "حمدان",
        "حمدي",
        "حمزة",
        "حمود",
        "حميد",
        "حمّاد",
        "حنبل",
        "حنفي",
        "حيدر",
        "حيّان",
        "خاطر",
        "خافق",
        "خالد",
        "خالدي",
        "خضر",
        "خطيب",
        "خلدون",
        "خلف",
        "خلوصي",
        "خليفة",
        "خليل",
        "خميس",
        "خيري",
        "دؤوب",
        "داني",
        "داهي",
        "داوود",
        "دريد",
        "دليل",
        "دهمان",
        "ديسم",
        "ذريع",
        "ذكي",
        "ذيب",
        "رؤوف",
        "رئيس",
        "رائد",
        "رائف",
        "رابح",
        "راتب",
        "راجح",
        "راجي",
        "رازي",
        "راسم",
        "راشد",
        "راضي",
        "راغب",
        "رافع",
        "رامح",
        "رامز",
        "رامي",
        "راني",
        "راوي",
        "رباح",
        "ربيع",
        "رجاء",
        "رجائي",
        "رجب",
        "رحيب",
        "رخاء",
        "رزين",
        "رستم",
        "رسمي",
        "رشاد",
        "رشدي",
        "رشيد",
        "رضوان",
        "رضي",
        "رفيق",
        "رمحي",
        "رمزي",
        "رمضان",
        "رهيف",
        "روحي",
        "ريّان",
        "زاخر",
        "زاكي",
        "زاهر",
        "زاهي",
        "زايد",
        "زبير",
        "زغلول",
        "زكريا",
        "زكي",
        "زهدي",
        "زهران",
        "زهير",
        "زياد",
        "زيد",
        "زيدان",
        "زين",
        "سائد",
        "ساجد",
        "ساجي",
        "ساطع",
        "سالم",
        "سامح",
        "سامر",
        "سامي",
        "ساهد",
        "ساهر",
        "سخاء",
        "سراج",
        "سراج الدّين",
        "سرحان",
        "سرور",
        "سعد",
        "سعدون",
        "سعدي",
        "سعود",
        "سعيد",
        "سفيان",
        "سفير",
        "سلام",
        "سلطان",
        "سلمان",
        "سليم",
        "سليمان",
        "سموح",
        "سمير",
        "سنام",
        "سنان",
        "سهل",
        "سهوان",
        "سهيل",
        "سيف الدّين",
        "سيّد",
        "شادي",
        "شاطر",
        "شافع",
        "شاكر",
        "شامخ",
        "شامل",
        "شبلي",
        "شبيب",
        "شجاع",
        "شدّاد",
        "شريف",
        "شعبان",
        "شعلان",
        "شعيب",
        "شفيع",
        "شكري",
        "شكيب",
        "شهاب",
        "شهب",
        "شهم",
        "شهير",
        "شوقي",
        "شيّق",
        "صائب",
        "صابر",
        "صاحب",
        "صادح",
        "صادق",
        "صارم",
        "صافي",
        "صالح",
        "صامد",
        "صباح",
        "صبحي",
        "صبري",
        "صبور",
        "صبيح",
        "صخر",
        "صدر الدّين",
        "صدقي",
        "صدّاح",
        "صدّام",
        "صعب",
        "صقر",
        "صلاح",
        "صلاح الدّين",
        "صنديد",
        "صهيب",
        "ضاحك",
        "ضاحي",
        "ضحّاك",
        "ضرغام",
        "ضياء",
        "ضياء الدّين",
        "ضيائي",
        "طائع",
        "طائف",
        "طائل",
        "طارق",
        "طالب",
        "طامح",
        "طاهر",
        "طبّاع",
        "طريف",
        "طلال",
        "طلعت",
        "طموح",
        "طه",
        "طيّب",
        "طيّع",
        "ظاعن",
        "ظافر",
        "ظاهر",
        "ظبي",
        "ظريف",
        "ظهير",
        "عائد",
        "عابد",
        "عاتب",
        "عادل",
        "عارف",
        "عاصم",
        "عاطف",
        "عاقل",
        "عاكف",
        "عالم",
        "عامر",
        "عبد الإله",
        "عبد الباري",
        "عبد الباقي",
        "عبد التّواب",
        "عبد الجبّار",
        "عبد الجليل",
        "عبد الحفيظ",
        "عبد الحقّ",
        "عبد الحكيم",
        "عبد الحليم",
        "عبد الحيّ",
        "عبد الخالق",
        "عبد الرّؤوف",
        "عبد الرّحمن",
        "عبد الرّحيم",
        "عبد الرّزاق",
        "عبد الرّشيد",
        "عبد السّلام",
        "عبد السّميع",
        "عبد الشّكور",
        "عبد الصّمد",
        "عبد العزيز",
        "عبد العليم",
        "عبد الغفور",
        "عبد الغفّار",
        "عبد الغني",
        "عبد القادر",
        "عبد القدّوس",
        "عبد القهّار",
        "عبد الكريم",
        "عبد اللطيف",
        "عبد المجيد",
        "عبد المحيي",
        "عبد الملك",
        "عبد المولى",
        "عبد الواحد",
        "عبدالرّحمن",
        "عبدالله",
        "عبّاس",
        "عبّود",
        "عتريس",
        "عتيد",
        "عتيق",
        "عثمان",
        "عدلي",
        "عدنان",
        "عدوي",
        "عذب",
        "عربي",
        "عرفات",
        "عرفان",
        "عرفه",
        "عزاز",
        "عزمي",
        "عزيز",
        "عزّ الدّين",
        "عزّت",
        "عصام",
        "عصمت",
        "عطاء",
        "عفيف",
        "عقيل",
        "علاء",
        "علاء الدّين",
        "علم الدّين",
        "علوان",
        "علي",
        "علّام",
        "عماد",
        "عمر",
        "عمران",
        "عمرو",
        "عمير",
        "عمّار",
        "غازي",
        "غالب",
        "غالي",
        "غامد",
        "غانم",
        "غزوان",
        "غزير",
        "غسّان",
        "غطفان",
        "فؤاد",
        "فائق",
        "فاتح",
        "فاخر",
        "فادي",
        "فارس",
        "فارع",
        "فاروق",
        "فاضل",
        "فالح",
        "فايد",
        "فتحي",
        "فتوح",
        "فخر",
        "فخر الدّين",
        "فخري",
        "فداء",
        "فدائي",
        "فراس",
        "فرج",
        "فرحان",
        "فرزدق",
        "فضل",
        "فطين",
        "فكري",
        "فلاح",
        "فهد",
        "فهمي",
        "فوزي",
        "فوّاز",
        "فيصل",
        "فيّاض",
        "قائد",
        "قاسم",
        "قاصد",
        "قانت",
        "قبس",
        "قحطان",
        "قدري",
        "قصي",
        "قصيد",
        "قطب",
        "قطز",
        "قنوع",
        "قيس",
        "كارم",
        "كاسر",
        "كاشف",
        "كاظم",
        "كافور",
        "كامل",
        "كايد",
        "كبير",
        "كتوم",
        "كرم",
        "كريم",
        "كسّاب",
        "كليم",
        "كمال",
        "كنار",
        "كنان",
        "كنعان",
        "لؤي",
        "لبيب",
        "لبيد",
        "لطفي",
        "لطوف",
        "لفيف",
        "لقاء",
        "لقمان",
        "لمّاح",
        "لهفان",
        "ليث",
        "مأمون",
        "مؤمن",
        "مؤنس",
        "مؤيّد",
        "ماجد",
        "مازن",
        "مالك",
        "ماهر",
        "مجاهد",
        "مجد",
        "محجوب",
        "محسن",
        "محفوظ",
        "محمود",
        "محمّد",
        "محيي الدّين",
        "مختار",
        "مخلص",
        "مدحت",
        "مراد",
        "مرادي",
        "مرتجي",
        "مرتضي",
        "مرتقي",
        "مرزوق",
        "مرسال",
        "مرشد",
        "مرعي",
        "مروان",
        "مزهر",
        "مسرور",
        "مسعود",
        "مسلم",
        "مشاري",
        "مشرف",
        "مشرق",
        "مشفق",
        "مصباح",
        "مصطفى",
        "مصعب",
        "مطاوع",
        "مظهر",
        "معارف",
        "معتوق",
        "معزّ",
        "معمّر",
        "معن",
        "معين",
        "مفيد",
        "مقداد",
        "مقدام",
        "مكرّم",
        "مكّي",
        "ملهم",
        "ممتاز",
        "ممدوح",
        "مناف",
        "منتصر",
        "منسي",
        "منصور",
        "منيب",
        "منيع",
        "منيف",
        "مهدي",
        "مهران",
        "مهنّد",
        "مهيب",
        "موسى",
        "موفّق",
        "مياس",
        "ميثاق",
        "ميسور",
        "ميمون",
        "ميّاد",
        "مَجدي",
        "مَسعد",
        "مُتعب",
        "مُتوكّل",
        "مُتولي",
        "مُتيّم",
        "مُخيمر",
        "مُرسي",
        "مُرضي",
        "مُسعف",
        "مُصلح",
        "مُعتز",
        "مُناضل",
        "مُنجد",
        "مُنذر",
        "مُنير",
        "نائل",
        "ناجح",
        "ناجي",
        "نادر",
        "نادي",
        "ناصر",
        "ناصر الدّين",
        "ناصيف",
        "ناضر",
        "ناظم",
        "ناعم",
        "نافذ",
        "نافع",
        "نبراس",
        "نبهان",
        "نبيل",
        "نبيه",
        "نجدت",
        "نجم الدّين",
        "نجوان",
        "نجيب",
        "نديم",
        "نذير",
        "نزار",
        "نزيه",
        "نسيب",
        "نشأت",
        "نشوان",
        "نصر",
        "نصر الدّين",
        "نصري",
        "نصوح",
        "نصور",
        "نضال",
        "نظام",
        "نظمي",
        "نعمان",
        "نعيم",
        "نمر",
        "نوح",
        "نور",
        "نور الحقّ",
        "نور الدّين",
        "نورس",
        "نوري",
        "نوّار",
        "نوّاف",
        "نيازي",
        "هادي",
        "هاشم",
        "هاني",
        "هايل",
        "هزار",
        "هلال",
        "هلالي",
        "همام",
        "هيثم",
        "هيكل",
        "هيمان",
        "وائل",
        "واثق",
        "وادع",
        "واصف",
        "واصل",
        "وثّاب",
        "وجدي",
        "وجيه",
        "وحيد",
        "ودود",
        "وديع",
        "وريد",
        "وسام",
        "وسيل",
        "وسيم",
        "وصفي",
        "وضّاح",
        "وفائي",
        "وفيق",
        "وليد",
        "وليف",
        "ياسر",
        "يافع",
        "ياقوت",
        "يانع",
        "يحيى",
        "يزيد",
        "يسار",
        "يسري",
        "يعرب",
        "يعقوب",
        "يقين",
        "يمام",
        "يوسف",
        "يونس",
    )

    first_names = first_names_male + first_names_female

    last_names: Tuple[str, ...] = (
        "أشجع",
        "أفغاني",
        "أكلب",
        "ألمع",
        "أنمار",
        "أولاد بوعزيز",
        "أولاد زيان",
        "إياد",
        "ابو الحاج",
        "ابو السعود",
        "ابو عيد",
        "ارناؤوط",
        "ازحيمان",
        "اسطمبولي",
        "الأزد",
        "الأشراف",
        "الألجاوي",
        "الأنصاري",
        "الأوس",
        "الأيوبي",
        "الامام",
        "البامية",
        "البخاري",
        "البديري",
        "البشيتي",
        "البغدادي",
        "البقوم",
        "البيسار القعقور",
        "البيطار",
        "الترجمان الصالح",
        "الترهي",
        "التوتنجي",
        "الجاعوني",
        "الجبشة",
        "الجعليين",
        "الحجر بن الهنوء بن الأزد",
        "الحداء",
        "الحسيني",
        "الحكم بن سعد العشيرة",
        "الحلاق",
        "الحلواني",
        "الحواش",
        "الحويطات",
        "الخالدي",
        "الخزرج",
        "الخطيب بني جماعة الكناني",
        "الخلفاوي",
        "الداودي",
        "الدجاني",
        "الدسوقي",
        "الدقاق",
        "الدليم",
        "الدواسر",
        "الديسي",
        "الرباب",
        "الرباطاب",
        "الزرقان",
        "الزماميري",
        "الساحلي",
        "السادة",
        "السادة الراويون",
        "السروري",
        "السمان",
        "السهول",
        "السيفي",
        "الشامي",
        "الشاويش",
        "الشايقية",
        "الشحوح",
        "الشرفاء",
        "الشعباني",
        "الشهابي",
        "الطحان",
        "الظفير",
        "العارف",
        "العجمان",
        "العسلي",
        "العفيفي",
        "العقيدات",
        "العلمي",
        "العوازم",
        "العوالق",
        "الغوانمة",
        "الفتياني",
        "القاعي",
        "القباني",
        "القرجولي",
        "القزاز",
        "القضماني",
        "القطب",
        "القلموني",
        "القواسم",
        "الكالوتي",
        "الكبابيش",
        "الكثيري",
        "الكلغاصي",
        "الكواهلة",
        "المؤقت",
        "الماني",
        "المتولي",
        "المرازيق",
        "المظفر",
        "المغربي",
        "المفتي",
        "المملوك",
        "المنتفق",
        "المهرة",
        "الموركة",
        "الموسوس",
        "النجار",
        "النشاشيبي",
        "النقيب",
        "النمر",
        "النمري",
        "الهدمي",
        "الوعري",
        "اليوزباشي",
        "اميوني",
        "اهرام",
        "بارق",
        "باهلة",
        "بتروني",
        "بجيلة",
        "بحمدوني",
        "بدرية",
        "بديرية",
        "بعلبكي",
        "بكر بن عبد مناة",
        "بكر بن وائل",
        "بكيل",
        "بلغازي",
        "بلقرن",
        "بلي",
        "بنو أسد",
        "بنو أمية",
        "بنو الأحمر",
        "بنو الأحمر بن الحارث",
        "بنو الأسمر",
        "بنو الحارث بن كعب",
        "بنو الدئل",
        "بنو العريج",
        "بنو النجار",
        "بنو حنيفة",
        "بنو خالد",
        "بنو ذي أصبح",
        "بنو زيد",
        "بنو سعد بن بكر",
        "بنو سعد بن ليث بن بكر",
        "بنو شعبة",
        "بنو شهر",
        "بنو شيبان",
        "بنو شيبة",
        "بنو صخر",
        "بنو ضمرة",
        "بنو عبس",
        "بنو عجل",
        "بنو عدي",
        "بنو عمرو",
        "بنو فراس",
        "بنو كلب",
        "بنو كنز",
        "بنو لام",
        "بنو ليث",
        "بنو مالك",
        "بنو معقل",
        "بنو مهدي",
        "بنو هاشم",
        "بنو هلال",
        "بنو ياس",
        "بنو يعلى",
        "بني بيات",
        "بني رشيد",
        "بني عطية",
        "بني هاجر",
        "بو مدين",
        "بيرقدار",
        "بيروتي",
        "ترابين",
        "تغلب بن وائل",
        "تميم",
        "تنوخ",
        "ثقيف",
        "جار الله",
        "جبيلي",
        "جديس",
        "جذام",
        "جرهم",
        "جزار",
        "جزيني",
        "جعفر",
        "جهينة",
        "جودة",
        "حاشد",
        "حب رمان",
        "حجازي",
        "حرب",
        "حمير",
        "حميضة",
        "حوالة",
        "خثعم",
        "خزاعة",
        "خندف",
        "خولان",
        "درويش",
        "دوبلال",
        "راجح",
        "ربيعة",
        "رصاص",
        "زبيد",
        "زحلاوي",
        "زحيكة",
        "زلاطيمو",
        "زهران",
        "سبيع",
        "سرندح",
        "سليم",
        "سموم",
        "سوميرة",
        "شتية",
        "شرف",
        "شمر",
        "شمران",
        "شهران",
        "شويفاتي",
        "صيام",
        "صيداني",
        "صيداوي",
        "ضبيعة",
        "طرابلسي",
        "طزيز",
        "طسم",
        "طقش",
        "طه",
        "طوطح",
        "طيء",
        "عامر بن صعصعة",
        "عاملة",
        "عبد القيس",
        "عبد اللطيف",
        "عبده",
        "عبيد",
        "عتيبة",
        "عجرمة (العجارمة)",
        "عدوان",
        "عذرة",
        "عرموني",
        "عسير",
        "عضل",
        "عكاوي",
        "عليان",
        "عنز بن وائل",
        "عنزة",
        "عنس",
        "عويضة",
        "غامد",
        "غطفان",
        "غنيم",
        "غوشة",
        "فراهيد",
        "فهم",
        "قبيلة هذيل البقوم",
        "قحطان",
        "قرش",
        "قريش",
        "قضاعة",
        "قطينة",
        "قليبو",
        "قيس عيلان",
        "كمال",
        "كنانة",
        "كندة",
        "كهلان",
        "لخم",
        "متني",
        "مذحج",
        "مراد",
        "مرازيق البقوم",
        "مزرعاني",
        "مزينة",
        "مشعشع",
        "مضر",
        "مطير",
        "معتوق",
        "ميرفاب",
        "نجم",
        "نجيب",
        "نسيبة",
        "نهد",
        "نور الدين",
        "هذيل",
        "همدان",
        "هندية",
        "هوازن",
        "وهبة",
        "يافع",
        "يشكر",
    )

    prefixes_female: Tuple[str, ...] = (
        "الآنسة",
        "الأستاذة",
        "الدكتورة",
        "السيدة",
        "المهندسة",
    )
    prefixes_male: Tuple[str, ...] = (
        "الأستاذ",
        "الدكتور",
        "السيد",
        "المهندس",
    )
