# Fichier d'importation de tous les modèles pour Flask-Migrate
# Ce fichier assure que tous les modèles sont découverts par Alembic

# Modèles d'authentification
from app.modules.auth.models import User, UserRole

# Modèles d'inventaire existants
from app.modules.inventory.models_product import Product, ProductCategory, ProductVariant
from app.modules.inventory.models_ingredient import Ingredient, IngredientCategory
from app.modules.inventory.models_recipe import Recipe, RecipeItem
from app.modules.inventory.models_supplier import Supplier
from app.modules.inventory.models_supplier_category import SupplierCategory
from app.modules.inventory.models_supplier_contact import SupplierContact
from app.modules.inventory.models_stock_movement import StockMovement
from app.modules.inventory.models_cost_history import CostHistory

# Nouveaux modèles d'approvisionnement
from app.modules.inventory.models_purchase_order import PurchaseOrder, PurchaseOrderItem
from app.modules.inventory.models_supplier_invoice import SupplierInvoice, SupplierPayment
from app.modules.inventory.models_bank_account import BankAccount, BankOperation

# Modèles de caisse
from app.modules.cash_register.models_cash_register import CashRegister, CashOperation

# Modèles POS
from app.modules.pos.models_sale import Sale, SaleItem, Payment

# Modèles de clients
from app.modules.customers.models import Customer

# Modèles de tables
from app.modules.tables.models_table import Table, Room

# Modèles de promotions
from app.modules.promotions.models_promotion import Promotion

# Modèles d'employés
from app.modules.employees.models import Employee, EmployeeProfile, WorkSchedule, Attendance, Payroll, Performance, EmployeeDocument

# Modèles de dépenses
from app.modules.expenses.models_expense import Expense, ExpenseCategory

# Modèles de paramètres
from app.modules.settings.models_settings import Settings

# Modèles de notifications
from app.modules.notifications.models import Notification

# Modèles de commandes en ligne
from app.modules.online_ordering_sites.models import OnlineOrderingSite, OnlineOrder, OnlineOrderItem

# Modèles d'IA
from app.modules.ai_support.models import SupportTicket, SupportMessage, SupportConversation, SupportChatMessage, SupportKnowledgeBase, SupportAnalytics

__all__ = [
    # Auth
    'User', 'UserRole',
    
    # Inventory existant
    'Product', 'ProductCategory', 'ProductVariant',
    'Ingredient', 'IngredientCategory',
    'Recipe', 'RecipeItem',
    'Supplier', 'SupplierCategory', 'SupplierContact',
    'StockMovement', 'CostHistory',
    
    # Nouveaux modèles d'approvisionnement
    'PurchaseOrder', 'PurchaseOrderItem',
    'SupplierInvoice', 'SupplierPayment',
    'BankAccount', 'BankOperation',
    
    # Autres modèles
    'CashRegister', 'CashOperation',
    'Sale', 'SaleItem', 'Payment',
    'Customer',
    'Table', 'Room',
    'Promotion',
    'Employee', 'EmployeeProfile', 'WorkSchedule', 'Attendance', 'Payroll', 'Performance', 'EmployeeDocument',
    'Expense', 'ExpenseCategory',
    'Settings',
    'Notification',
    'OnlineOrderingSite', 'OnlineOrder', 'OnlineOrderItem',
    'SupportTicket', 'SupportMessage', 'SupportConversation', 'SupportChatMessage', 'SupportKnowledgeBase', 'SupportAnalytics'
]
