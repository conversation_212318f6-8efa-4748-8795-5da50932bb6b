{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/cloud-platform": {"description": "See, edit, configure, and delete your Google Cloud data and see the email address for your Google Account."}}}}, "basePath": "", "baseUrl": "https://run.googleapis.com/", "batchPath": "batch", "canonicalName": "Cloud Run", "description": "Deploy and manage user provided container images that scale automatically based on incoming requests. The Cloud Run Admin API v1 follows the Knative Serving API specification, while v2 is aligned with Google Cloud AIP-based API standards, as described in https://google.aip.dev/.", "discoveryVersion": "v1", "documentationLink": "https://cloud.google.com/run/", "endpoints": [{"description": "Locational Endpoint", "endpointUrl": "https://africa-south1-run.googleapis.com/", "location": "africa-south1"}, {"description": "Locational Endpoint", "endpointUrl": "https://asia-east1-run.googleapis.com/", "location": "asia-east1"}, {"description": "Locational Endpoint", "endpointUrl": "https://asia-east2-run.googleapis.com/", "location": "asia-east2"}, {"description": "Locational Endpoint", "endpointUrl": "https://asia-northeast1-run.googleapis.com/", "location": "asia-northeast1"}, {"description": "Locational Endpoint", "endpointUrl": "https://asia-northeast2-run.googleapis.com/", "location": "asia-northeast2"}, {"description": "Locational Endpoint", "endpointUrl": "https://asia-northeast3-run.googleapis.com/", "location": "asia-northeast3"}, {"description": "Locational Endpoint", "endpointUrl": "https://asia-south1-run.googleapis.com/", "location": "asia-south1"}, {"description": "Locational Endpoint", "endpointUrl": "https://asia-south2-run.googleapis.com/", "location": "asia-south2"}, {"description": "Locational Endpoint", "endpointUrl": "https://asia-southeast1-run.googleapis.com/", "location": "asia-southeast1"}, {"description": "Locational Endpoint", "endpointUrl": "https://asia-southeast2-run.googleapis.com/", "location": "asia-southeast2"}, {"description": "Locational Endpoint", "endpointUrl": "https://australia-southeast1-run.googleapis.com/", "location": "australia-southeast1"}, {"description": "Locational Endpoint", "endpointUrl": "https://australia-southeast2-run.googleapis.com/", "location": "australia-southeast2"}, {"description": "Locational Endpoint", "endpointUrl": "https://europe-central2-run.googleapis.com/", "location": "europe-central2"}, {"description": "Locational Endpoint", "endpointUrl": "https://europe-north1-run.googleapis.com/", "location": "europe-north1"}, {"description": "Locational Endpoint", "endpointUrl": "https://europe-north2-run.googleapis.com/", "location": "europe-north2"}, {"description": "Locational Endpoint", "endpointUrl": "https://europe-southwest1-run.googleapis.com/", "location": "europe-southwest1"}, {"description": "Locational Endpoint", "endpointUrl": "https://europe-west1-run.googleapis.com/", "location": "europe-west1"}, {"description": "Locational Endpoint", "endpointUrl": "https://europe-west10-run.googleapis.com/", "location": "europe-west10"}, {"description": "Locational Endpoint", "endpointUrl": "https://europe-west12-run.googleapis.com/", "location": "europe-west12"}, {"description": "Locational Endpoint", "endpointUrl": "https://europe-west2-run.googleapis.com/", "location": "europe-west2"}, {"description": "Locational Endpoint", "endpointUrl": "https://europe-west3-run.googleapis.com/", "location": "europe-west3"}, {"description": "Locational Endpoint", "endpointUrl": "https://europe-west4-run.googleapis.com/", "location": "europe-west4"}, {"description": "Locational Endpoint", "endpointUrl": "https://europe-west6-run.googleapis.com/", "location": "europe-west6"}, {"description": "Locational Endpoint", "endpointUrl": "https://europe-west8-run.googleapis.com/", "location": "europe-west8"}, {"description": "Locational Endpoint", "endpointUrl": "https://europe-west9-run.googleapis.com/", "location": "europe-west9"}, {"description": "Locational Endpoint", "endpointUrl": "https://me-central1-run.googleapis.com/", "location": "me-central1"}, {"description": "Locational Endpoint", "endpointUrl": "https://me-central2-run.googleapis.com/", "location": "me-central2"}, {"description": "Locational Endpoint", "endpointUrl": "https://me-west1-run.googleapis.com/", "location": "me-west1"}, {"description": "Locational Endpoint", "endpointUrl": "https://northamerica-northeast1-run.googleapis.com/", "location": "northamerica-northeast1"}, {"description": "Locational Endpoint", "endpointUrl": "https://northamerica-northeast2-run.googleapis.com/", "location": "northamerica-northeast2"}, {"description": "Locational Endpoint", "endpointUrl": "https://northamerica-south1-run.googleapis.com/", "location": "northamerica-south1"}, {"description": "Locational Endpoint", "endpointUrl": "https://southamerica-east1-run.googleapis.com/", "location": "southamerica-east1"}, {"description": "Locational Endpoint", "endpointUrl": "https://southamerica-west1-run.googleapis.com/", "location": "southamerica-west1"}, {"description": "Locational Endpoint", "endpointUrl": "https://us-central1-run.googleapis.com/", "location": "us-central1"}, {"description": "Locational Endpoint", "endpointUrl": "https://us-east1-run.googleapis.com/", "location": "us-east1"}, {"description": "Locational Endpoint", "endpointUrl": "https://us-east4-run.googleapis.com/", "location": "us-east4"}, {"description": "Locational Endpoint", "endpointUrl": "https://us-east5-run.googleapis.com/", "location": "us-east5"}, {"description": "Locational Endpoint", "endpointUrl": "https://us-south1-run.googleapis.com/", "location": "us-south1"}, {"description": "Locational Endpoint", "endpointUrl": "https://us-west1-run.googleapis.com/", "location": "us-west1"}, {"description": "Locational Endpoint", "endpointUrl": "https://us-west2-run.googleapis.com/", "location": "us-west2"}, {"description": "Locational Endpoint", "endpointUrl": "https://us-west3-run.googleapis.com/", "location": "us-west3"}, {"description": "Locational Endpoint", "endpointUrl": "https://us-west4-run.googleapis.com/", "location": "us-west4"}], "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "run:v2", "kind": "discovery#restDescription", "mtlsRootUrl": "https://run.mtls.googleapis.com/", "name": "run", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"projects": {"resources": {"locations": {"methods": {"exportImage": {"description": "Export image for a given resource.", "flatPath": "v2/projects/{projectsId}/locations/{locationsId}/{locationsId1}:exportImage", "httpMethod": "POST", "id": "run.projects.locations.exportImage", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the resource of which image metadata should be exported. Format: `projects/{project_id_or_number}/locations/{location}/services/{service}/revisions/{revision}` for Revision `projects/{project_id_or_number}/locations/{location}/jobs/{job}/executions/{execution}` for Execution", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/.*$", "required": true, "type": "string"}}, "path": "v2/{+name}:exportImage", "request": {"$ref": "GoogleCloudRunV2ExportImageRequest"}, "response": {"$ref": "GoogleCloudRunV2ExportImageResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "exportImageMetadata": {"description": "Export image metadata for a given resource.", "flatPath": "v2/projects/{projectsId}/locations/{locationsId}/{locationsId1}:exportImageMetadata", "httpMethod": "GET", "id": "run.projects.locations.exportImageMetadata", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the resource of which image metadata should be exported. Format: `projects/{project_id_or_number}/locations/{location}/services/{service}/revisions/{revision}` for Revision `projects/{project_id_or_number}/locations/{location}/jobs/{job}/executions/{execution}` for Execution", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/.*$", "required": true, "type": "string"}}, "path": "v2/{+name}:exportImageMetadata", "response": {"$ref": "GoogleCloudRunV2Metadata"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "exportMetadata": {"description": "Export generated customer metadata for a given resource.", "flatPath": "v2/projects/{projectsId}/locations/{locationsId}/{locationsId1}:exportMetadata", "httpMethod": "GET", "id": "run.projects.locations.exportMetadata", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the resource of which metadata should be exported. Format: `projects/{project_id_or_number}/locations/{location}/services/{service}` for Service `projects/{project_id_or_number}/locations/{location}/services/{service}/revisions/{revision}` for Revision `projects/{project_id_or_number}/locations/{location}/jobs/{job}/executions/{execution}` for Execution {project_id_or_number} may contains domain-scoped project IDs", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/.*$", "required": true, "type": "string"}}, "path": "v2/{+name}:exportMetadata", "response": {"$ref": "GoogleCloudRunV2Metadata"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "exportProjectMetadata": {"description": "Export generated customer metadata for a given project.", "flatPath": "v2/projects/{projectsId}/locations/{locationsId}:exportProjectMetadata", "httpMethod": "GET", "id": "run.projects.locations.exportProjectMetadata", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the project of which metadata should be exported. Format: `projects/{project_id_or_number}/locations/{location}` for Project in a given location.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v2/{+name}:exportProjectMetadata", "response": {"$ref": "GoogleCloudRunV2Metadata"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"builds": {"methods": {"submit": {"description": "Submits a build in a given project.", "flatPath": "v2/projects/{projectsId}/locations/{locationsId}/builds:submit", "httpMethod": "POST", "id": "run.projects.locations.builds.submit", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The project and location to build in. Location must be a region, e.g., 'us-central1' or 'global' if the global builder is to be used. Format: `projects/{project}/locations/{location}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v2/{+parent}/builds:submit", "request": {"$ref": "GoogleCloudRunV2SubmitBuildRequest"}, "response": {"$ref": "GoogleCloudRunV2SubmitBuildResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "jobs": {"methods": {"create": {"description": "Creates a Job.", "flatPath": "v2/projects/{projectsId}/locations/{locationsId}/jobs", "httpMethod": "POST", "id": "run.projects.locations.jobs.create", "parameterOrder": ["parent"], "parameters": {"jobId": {"description": "Required. The unique identifier for the Job. The name of the job becomes {parent}/jobs/{job_id}.", "location": "query", "type": "string"}, "parent": {"description": "Required. The location and project in which this Job should be created. Format: projects/{project}/locations/{location}, where {project} can be project id or number.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "validateOnly": {"description": "Indicates that the request should be validated and default values populated, without persisting the request or creating any resources.", "location": "query", "type": "boolean"}}, "path": "v2/{+parent}/jobs", "request": {"$ref": "GoogleCloudRunV2Job"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a Job.", "flatPath": "v2/projects/{projectsId}/locations/{locationsId}/jobs/{jobsId}", "httpMethod": "DELETE", "id": "run.projects.locations.jobs.delete", "parameterOrder": ["name"], "parameters": {"etag": {"description": "A system-generated fingerprint for this version of the resource. May be used to detect modification conflict during updates.", "location": "query", "type": "string"}, "name": {"description": "Required. The full name of the Job. Format: projects/{project}/locations/{location}/jobs/{job}, where {project} can be project id or number.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/jobs/[^/]+$", "required": true, "type": "string"}, "validateOnly": {"description": "Indicates that the request should be validated without actually deleting any resources.", "location": "query", "type": "boolean"}}, "path": "v2/{+name}", "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets information about a Job.", "flatPath": "v2/projects/{projectsId}/locations/{locationsId}/jobs/{jobsId}", "httpMethod": "GET", "id": "run.projects.locations.jobs.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The full name of the Job. Format: projects/{project}/locations/{location}/jobs/{job}, where {project} can be project id or number.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/jobs/[^/]+$", "required": true, "type": "string"}}, "path": "v2/{+name}", "response": {"$ref": "GoogleCloudRunV2Job"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getIamPolicy": {"description": "Gets the IAM Access Control policy currently in effect for the given Job. This result does not include any inherited policies.", "flatPath": "v2/projects/{projectsId}/locations/{locationsId}/jobs/{jobsId}:getIamPolicy", "httpMethod": "GET", "id": "run.projects.locations.jobs.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"options.requestedPolicyVersion": {"description": "Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "location": "query", "type": "integer"}, "resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/jobs/[^/]+$", "required": true, "type": "string"}}, "path": "v2/{+resource}:getIamPolicy", "response": {"$ref": "GoogleIamV1Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists Jobs. Results are sorted by creation time, descending.", "flatPath": "v2/projects/{projectsId}/locations/{locationsId}/jobs", "httpMethod": "GET", "id": "run.projects.locations.jobs.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Maximum number of Jobs to return in this call.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token received from a previous call to ListJobs. All other parameters must match.", "location": "query", "type": "string"}, "parent": {"description": "Required. The location and project to list resources on. Format: projects/{project}/locations/{location}, where {project} can be project id or number.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "showDeleted": {"description": "If true, returns deleted (but unexpired) resources along with active ones.", "location": "query", "type": "boolean"}}, "path": "v2/{+parent}/jobs", "response": {"$ref": "GoogleCloudRunV2ListJobsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates a Job.", "flatPath": "v2/projects/{projectsId}/locations/{locationsId}/jobs/{jobsId}", "httpMethod": "PATCH", "id": "run.projects.locations.jobs.patch", "parameterOrder": ["name"], "parameters": {"allowMissing": {"description": "Optional. If set to true, and if the Job does not exist, it will create a new one. Caller must have both create and update permissions for this call if this is set to true.", "location": "query", "type": "boolean"}, "name": {"description": "The fully qualified name of this Job. Format: projects/{project}/locations/{location}/jobs/{job}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/jobs/[^/]+$", "required": true, "type": "string"}, "validateOnly": {"description": "Indicates that the request should be validated and default values populated, without persisting the request or updating any resources.", "location": "query", "type": "boolean"}}, "path": "v2/{+name}", "request": {"$ref": "GoogleCloudRunV2Job"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "run": {"description": "Triggers creation of a new Execution of this Job.", "flatPath": "v2/projects/{projectsId}/locations/{locationsId}/jobs/{jobsId}:run", "httpMethod": "POST", "id": "run.projects.locations.jobs.run", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The full name of the Job. Format: projects/{project}/locations/{location}/jobs/{job}, where {project} can be project id or number.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/jobs/[^/]+$", "required": true, "type": "string"}}, "path": "v2/{+name}:run", "request": {"$ref": "GoogleCloudRunV2RunJobRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets the IAM Access control policy for the specified Job. Overwrites any existing policy.", "flatPath": "v2/projects/{projectsId}/locations/{locationsId}/jobs/{jobsId}:setIamPolicy", "httpMethod": "POST", "id": "run.projects.locations.jobs.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/jobs/[^/]+$", "required": true, "type": "string"}}, "path": "v2/{+resource}:setIamPolicy", "request": {"$ref": "GoogleIamV1SetIamPolicyRequest"}, "response": {"$ref": "GoogleIamV1Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"description": "Returns permissions that a caller has on the specified Project. There are no permissions required for making this API call.", "flatPath": "v2/projects/{projectsId}/locations/{locationsId}/jobs/{jobsId}:testIamPermissions", "httpMethod": "POST", "id": "run.projects.locations.jobs.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/jobs/[^/]+$", "required": true, "type": "string"}}, "path": "v2/{+resource}:testIamPermissions", "request": {"$ref": "GoogleIamV1TestIamPermissionsRequest"}, "response": {"$ref": "GoogleIamV1TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"executions": {"methods": {"cancel": {"description": "Cancels an Execution.", "flatPath": "v2/projects/{projectsId}/locations/{locationsId}/jobs/{jobsId}/executions/{executionsId}:cancel", "httpMethod": "POST", "id": "run.projects.locations.jobs.executions.cancel", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the Execution to cancel. Format: `projects/{project}/locations/{location}/jobs/{job}/executions/{execution}`, where `{project}` can be project id or number.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/jobs/[^/]+/executions/[^/]+$", "required": true, "type": "string"}}, "path": "v2/{+name}:cancel", "request": {"$ref": "GoogleCloudRunV2CancelExecutionRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes an Execution.", "flatPath": "v2/projects/{projectsId}/locations/{locationsId}/jobs/{jobsId}/executions/{executionsId}", "httpMethod": "DELETE", "id": "run.projects.locations.jobs.executions.delete", "parameterOrder": ["name"], "parameters": {"etag": {"description": "A system-generated fingerprint for this version of the resource. This may be used to detect modification conflict during updates.", "location": "query", "type": "string"}, "name": {"description": "Required. The name of the Execution to delete. Format: `projects/{project}/locations/{location}/jobs/{job}/executions/{execution}`, where `{project}` can be project id or number.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/jobs/[^/]+/executions/[^/]+$", "required": true, "type": "string"}, "validateOnly": {"description": "Indicates that the request should be validated without actually deleting any resources.", "location": "query", "type": "boolean"}}, "path": "v2/{+name}", "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "exportStatus": {"description": "Read the status of an image export operation.", "flatPath": "v2/projects/{projectsId}/locations/{locationsId}/jobs/{jobsId}/executions/{executionsId}/{executionsId1}:exportStatus", "httpMethod": "GET", "id": "run.projects.locations.jobs.executions.exportStatus", "parameterOrder": ["name", "operationId"], "parameters": {"name": {"description": "Required. The name of the resource of which image export operation status has to be fetched. Format: `projects/{project_id_or_number}/locations/{location}/services/{service}/revisions/{revision}` for Revision `projects/{project_id_or_number}/locations/{location}/jobs/{job}/executions/{execution}` for Execution", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/jobs/[^/]+/executions/[^/]+$", "required": true, "type": "string"}, "operationId": {"description": "Required. The operation id returned from ExportImage.", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}}, "path": "v2/{+name}/{+operationId}:exportStatus", "response": {"$ref": "GoogleCloudRunV2ExportStatusResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets information about an Execution.", "flatPath": "v2/projects/{projectsId}/locations/{locationsId}/jobs/{jobsId}/executions/{executionsId}", "httpMethod": "GET", "id": "run.projects.locations.jobs.executions.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The full name of the Execution. Format: `projects/{project}/locations/{location}/jobs/{job}/executions/{execution}`, where `{project}` can be project id or number.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/jobs/[^/]+/executions/[^/]+$", "required": true, "type": "string"}}, "path": "v2/{+name}", "response": {"$ref": "GoogleCloudRunV2Execution"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists Executions from a Job. Results are sorted by creation time, descending.", "flatPath": "v2/projects/{projectsId}/locations/{locationsId}/jobs/{jobsId}/executions", "httpMethod": "GET", "id": "run.projects.locations.jobs.executions.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Maximum number of Executions to return in this call.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token received from a previous call to ListExecutions. All other parameters must match.", "location": "query", "type": "string"}, "parent": {"description": "Required. The Execution from which the Executions should be listed. To list all Executions across Jobs, use \"-\" instead of Job name. Format: `projects/{project}/locations/{location}/jobs/{job}`, where `{project}` can be project id or number.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/jobs/[^/]+$", "required": true, "type": "string"}, "showDeleted": {"description": "If true, returns deleted (but unexpired) resources along with active ones.", "location": "query", "type": "boolean"}}, "path": "v2/{+parent}/executions", "response": {"$ref": "GoogleCloudRunV2ListExecutionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"tasks": {"methods": {"get": {"description": "Gets information about a Task.", "flatPath": "v2/projects/{projectsId}/locations/{locationsId}/jobs/{jobsId}/executions/{executionsId}/tasks/{tasksId}", "httpMethod": "GET", "id": "run.projects.locations.jobs.executions.tasks.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The full name of the Task. Format: projects/{project}/locations/{location}/jobs/{job}/executions/{execution}/tasks/{task}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/jobs/[^/]+/executions/[^/]+/tasks/[^/]+$", "required": true, "type": "string"}}, "path": "v2/{+name}", "response": {"$ref": "GoogleCloudRunV2Task"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists Tasks from an Execution of a Job.", "flatPath": "v2/projects/{projectsId}/locations/{locationsId}/jobs/{jobsId}/executions/{executionsId}/tasks", "httpMethod": "GET", "id": "run.projects.locations.jobs.executions.tasks.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Maximum number of Tasks to return in this call.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token received from a previous call to ListTasks. All other parameters must match.", "location": "query", "type": "string"}, "parent": {"description": "Required. The Execution from which the Tasks should be listed. To list all Tasks across Executions of a Job, use \"-\" instead of Execution name. To list all Tasks across Jobs, use \"-\" instead of Job name. Format: projects/{project}/locations/{location}/jobs/{job}/executions/{execution}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/jobs/[^/]+/executions/[^/]+$", "required": true, "type": "string"}, "showDeleted": {"description": "If true, returns deleted (but unexpired) resources along with active ones.", "location": "query", "type": "boolean"}}, "path": "v2/{+parent}/tasks", "response": {"$ref": "GoogleCloudRunV2ListTasksResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}}}, "operations": {"methods": {"delete": {"description": "Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.", "flatPath": "v2/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}", "httpMethod": "DELETE", "id": "run.projects.locations.operations.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource to be deleted.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v2/{+name}", "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v2/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}", "httpMethod": "GET", "id": "run.projects.locations.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v2/{+name}", "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.", "flatPath": "v2/projects/{projectsId}/locations/{locationsId}/operations", "httpMethod": "GET", "id": "run.projects.locations.operations.list", "parameterOrder": ["name"], "parameters": {"filter": {"description": "Optional. A filter for matching the completed or in-progress operations. The supported formats of *filter* are: To query for only completed operations: done:true To query for only ongoing operations: done:false Must be empty to query for all of the latest operations for the given parent project.", "location": "query", "type": "string"}, "name": {"description": "Required. To query for all of the operations for a project.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "The maximum number of records that should be returned. Requested page size cannot exceed 100. If not set or set to less than or equal to 0, the default page size is 100. .", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Token identifying which result to start with, which is returned by a previous list call.", "location": "query", "type": "string"}}, "path": "v2/{+name}/operations", "response": {"$ref": "GoogleLongrunningListOperationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "wait": {"description": "Waits until the specified long-running operation is done or reaches at most a specified timeout, returning the latest state. If the operation is already done, the latest state is immediately returned. If the timeout specified is greater than the default HTTP/RPC timeout, the HTTP/RPC timeout is used. If the server does not support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Note that this method is on a best-effort basis. It may return the latest state before the specified timeout (including immediately), meaning even an immediate response is no guarantee that the operation is done.", "flatPath": "v2/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}:wait", "httpMethod": "POST", "id": "run.projects.locations.operations.wait", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource to wait on.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v2/{+name}:wait", "request": {"$ref": "GoogleLongrunningWaitOperationRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "services": {"methods": {"create": {"description": "Creates a new Service in a given project and location.", "flatPath": "v2/projects/{projectsId}/locations/{locationsId}/services", "httpMethod": "POST", "id": "run.projects.locations.services.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The location and project in which this service should be created. Format: projects/{project}/locations/{location}, where {project} can be project id or number. Only lowercase characters, digits, and hyphens.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "serviceId": {"description": "Required. The unique identifier for the Service. It must begin with letter, and cannot end with hyphen; must contain fewer than 50 characters. The name of the service becomes {parent}/services/{service_id}.", "location": "query", "type": "string"}, "validateOnly": {"description": "Indicates that the request should be validated and default values populated, without persisting the request or creating any resources.", "location": "query", "type": "boolean"}}, "path": "v2/{+parent}/services", "request": {"$ref": "GoogleCloudRunV2Service"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a Service. This will cause the Service to stop serving traffic and will delete all revisions.", "flatPath": "v2/projects/{projectsId}/locations/{locationsId}/services/{servicesId}", "httpMethod": "DELETE", "id": "run.projects.locations.services.delete", "parameterOrder": ["name"], "parameters": {"etag": {"description": "A system-generated fingerprint for this version of the resource. May be used to detect modification conflict during updates.", "location": "query", "type": "string"}, "name": {"description": "Required. The full name of the Service. Format: projects/{project}/locations/{location}/services/{service}, where {project} can be project id or number.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/services/[^/]+$", "required": true, "type": "string"}, "validateOnly": {"description": "Indicates that the request should be validated without actually deleting any resources.", "location": "query", "type": "boolean"}}, "path": "v2/{+name}", "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets information about a Service.", "flatPath": "v2/projects/{projectsId}/locations/{locationsId}/services/{servicesId}", "httpMethod": "GET", "id": "run.projects.locations.services.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The full name of the Service. Format: projects/{project}/locations/{location}/services/{service}, where {project} can be project id or number.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/services/[^/]+$", "required": true, "type": "string"}}, "path": "v2/{+name}", "response": {"$ref": "GoogleCloudRunV2Service"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getIamPolicy": {"description": "Gets the IAM Access Control policy currently in effect for the given Cloud Run Service. This result does not include any inherited policies.", "flatPath": "v2/projects/{projectsId}/locations/{locationsId}/services/{servicesId}:getIamPolicy", "httpMethod": "GET", "id": "run.projects.locations.services.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"options.requestedPolicyVersion": {"description": "Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "location": "query", "type": "integer"}, "resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/services/[^/]+$", "required": true, "type": "string"}}, "path": "v2/{+resource}:getIamPolicy", "response": {"$ref": "GoogleIamV1Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists Services. Results are sorted by creation time, descending.", "flatPath": "v2/projects/{projectsId}/locations/{locationsId}/services", "httpMethod": "GET", "id": "run.projects.locations.services.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Maximum number of Services to return in this call.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token received from a previous call to ListServices. All other parameters must match.", "location": "query", "type": "string"}, "parent": {"description": "Required. The location and project to list resources on. Location must be a valid Google Cloud region, and cannot be the \"-\" wildcard. Format: projects/{project}/locations/{location}, where {project} can be project id or number.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "showDeleted": {"description": "If true, returns deleted (but unexpired) resources along with active ones.", "location": "query", "type": "boolean"}}, "path": "v2/{+parent}/services", "response": {"$ref": "GoogleCloudRunV2ListServicesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates a Service.", "flatPath": "v2/projects/{projectsId}/locations/{locationsId}/services/{servicesId}", "httpMethod": "PATCH", "id": "run.projects.locations.services.patch", "parameterOrder": ["name"], "parameters": {"allowMissing": {"description": "Optional. If set to true, and if the Service does not exist, it will create a new one. The caller must have 'run.services.create' permissions if this is set to true and the Service does not exist.", "location": "query", "type": "boolean"}, "name": {"description": "The fully qualified name of this Service. In CreateServiceRequest, this field is ignored, and instead composed from CreateServiceRequest.parent and CreateServiceRequest.service_id. Format: projects/{project}/locations/{location}/services/{service_id}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/services/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Optional. The list of fields to be updated.", "format": "google-fieldmask", "location": "query", "type": "string"}, "validateOnly": {"description": "Indicates that the request should be validated and default values populated, without persisting the request or updating any resources.", "location": "query", "type": "boolean"}}, "path": "v2/{+name}", "request": {"$ref": "GoogleCloudRunV2Service"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets the IAM Access control policy for the specified Service. Overwrites any existing policy.", "flatPath": "v2/projects/{projectsId}/locations/{locationsId}/services/{servicesId}:setIamPolicy", "httpMethod": "POST", "id": "run.projects.locations.services.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/services/[^/]+$", "required": true, "type": "string"}}, "path": "v2/{+resource}:setIamPolicy", "request": {"$ref": "GoogleIamV1SetIamPolicyRequest"}, "response": {"$ref": "GoogleIamV1Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"description": "Returns permissions that a caller has on the specified Project. There are no permissions required for making this API call.", "flatPath": "v2/projects/{projectsId}/locations/{locationsId}/services/{servicesId}:testIamPermissions", "httpMethod": "POST", "id": "run.projects.locations.services.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/services/[^/]+$", "required": true, "type": "string"}}, "path": "v2/{+resource}:testIamPermissions", "request": {"$ref": "GoogleIamV1TestIamPermissionsRequest"}, "response": {"$ref": "GoogleIamV1TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"revisions": {"methods": {"delete": {"description": "Deletes a Revision.", "flatPath": "v2/projects/{projectsId}/locations/{locationsId}/services/{servicesId}/revisions/{revisionsId}", "httpMethod": "DELETE", "id": "run.projects.locations.services.revisions.delete", "parameterOrder": ["name"], "parameters": {"etag": {"description": "A system-generated fingerprint for this version of the resource. This may be used to detect modification conflict during updates.", "location": "query", "type": "string"}, "name": {"description": "Required. The name of the Revision to delete. Format: projects/{project}/locations/{location}/services/{service}/revisions/{revision}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/services/[^/]+/revisions/[^/]+$", "required": true, "type": "string"}, "validateOnly": {"description": "Indicates that the request should be validated without actually deleting any resources.", "location": "query", "type": "boolean"}}, "path": "v2/{+name}", "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "exportStatus": {"description": "Read the status of an image export operation.", "flatPath": "v2/projects/{projectsId}/locations/{locationsId}/services/{servicesId}/revisions/{revisionsId}/{revisionsId1}:exportStatus", "httpMethod": "GET", "id": "run.projects.locations.services.revisions.exportStatus", "parameterOrder": ["name", "operationId"], "parameters": {"name": {"description": "Required. The name of the resource of which image export operation status has to be fetched. Format: `projects/{project_id_or_number}/locations/{location}/services/{service}/revisions/{revision}` for Revision `projects/{project_id_or_number}/locations/{location}/jobs/{job}/executions/{execution}` for Execution", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/services/[^/]+/revisions/[^/]+$", "required": true, "type": "string"}, "operationId": {"description": "Required. The operation id returned from ExportImage.", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}}, "path": "v2/{+name}/{+operationId}:exportStatus", "response": {"$ref": "GoogleCloudRunV2ExportStatusResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets information about a Revision.", "flatPath": "v2/projects/{projectsId}/locations/{locationsId}/services/{servicesId}/revisions/{revisionsId}", "httpMethod": "GET", "id": "run.projects.locations.services.revisions.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The full name of the Revision. Format: projects/{project}/locations/{location}/services/{service}/revisions/{revision}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/services/[^/]+/revisions/[^/]+$", "required": true, "type": "string"}}, "path": "v2/{+name}", "response": {"$ref": "GoogleCloudRunV2Revision"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists Revisions from a given Service, or from a given location. Results are sorted by creation time, descending.", "flatPath": "v2/projects/{projectsId}/locations/{locationsId}/services/{servicesId}/revisions", "httpMethod": "GET", "id": "run.projects.locations.services.revisions.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Maximum number of revisions to return in this call.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token received from a previous call to ListRevisions. All other parameters must match.", "location": "query", "type": "string"}, "parent": {"description": "Required. The Service from which the Revisions should be listed. To list all Revisions across Services, use \"-\" instead of Service name. Format: projects/{project}/locations/{location}/services/{service}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/services/[^/]+$", "required": true, "type": "string"}, "showDeleted": {"description": "If true, returns deleted (but unexpired) resources along with active ones.", "location": "query", "type": "boolean"}}, "path": "v2/{+parent}/revisions", "response": {"$ref": "GoogleCloudRunV2ListRevisionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}, "workerPools": {"methods": {"create": {"description": "Creates a new WorkerPool in a given project and location.", "flatPath": "v2/projects/{projectsId}/locations/{locationsId}/workerPools", "httpMethod": "POST", "id": "run.projects.locations.workerPools.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The location and project in which this worker pool should be created. Format: `projects/{project}/locations/{location}`, where `{project}` can be project id or number. Only lowercase characters, digits, and hyphens.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "validateOnly": {"description": "Optional. Indicates that the request should be validated and default values populated, without persisting the request or creating any resources.", "location": "query", "type": "boolean"}, "workerPoolId": {"description": "Required. The unique identifier for the WorkerPool. It must begin with letter, and cannot end with hyphen; must contain fewer than 50 characters. The name of the worker pool becomes `{parent}/workerPools/{worker_pool_id}`.", "location": "query", "type": "string"}}, "path": "v2/{+parent}/workerPools", "request": {"$ref": "GoogleCloudRunV2WorkerPool"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a WorkerPool.", "flatPath": "v2/projects/{projectsId}/locations/{locationsId}/workerPools/{workerPoolsId}", "httpMethod": "DELETE", "id": "run.projects.locations.workerPools.delete", "parameterOrder": ["name"], "parameters": {"etag": {"description": "A system-generated fingerprint for this version of the resource. May be used to detect modification conflict during updates.", "location": "query", "type": "string"}, "name": {"description": "Required. The full name of the WorkerPool. Format: `projects/{project}/locations/{location}/workerPools/{worker_pool}`, where `{project}` can be project id or number.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/workerPools/[^/]+$", "required": true, "type": "string"}, "validateOnly": {"description": "Optional. Indicates that the request should be validated without actually deleting any resources.", "location": "query", "type": "boolean"}}, "path": "v2/{+name}", "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets information about a WorkerPool.", "flatPath": "v2/projects/{projectsId}/locations/{locationsId}/workerPools/{workerPoolsId}", "httpMethod": "GET", "id": "run.projects.locations.workerPools.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The full name of the WorkerPool. Format: `projects/{project}/locations/{location}/workerPools/{worker_pool}`, where `{project}` can be project id or number.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/workerPools/[^/]+$", "required": true, "type": "string"}}, "path": "v2/{+name}", "response": {"$ref": "GoogleCloudRunV2WorkerPool"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getIamPolicy": {"description": "Gets the IAM Access Control policy currently in effect for the given Cloud Run WorkerPool. This result does not include any inherited policies.", "flatPath": "v2/projects/{projectsId}/locations/{locationsId}/workerPools/{workerPoolsId}:getIamPolicy", "httpMethod": "GET", "id": "run.projects.locations.workerPools.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"options.requestedPolicyVersion": {"description": "Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "location": "query", "type": "integer"}, "resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/workerPools/[^/]+$", "required": true, "type": "string"}}, "path": "v2/{+resource}:getIamPolicy", "response": {"$ref": "GoogleIamV1Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists WorkerPools. Results are sorted by creation time, descending.", "flatPath": "v2/projects/{projectsId}/locations/{locationsId}/workerPools", "httpMethod": "GET", "id": "run.projects.locations.workerPools.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Maximum number of WorkerPools to return in this call.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token received from a previous call to ListWorkerPools. All other parameters must match.", "location": "query", "type": "string"}, "parent": {"description": "Required. The location and project to list resources on. Location must be a valid Google Cloud region, and cannot be the \"-\" wildcard. Format: `projects/{project}/locations/{location}`, where `{project}` can be project id or number.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "showDeleted": {"description": "If true, returns deleted (but unexpired) resources along with active ones.", "location": "query", "type": "boolean"}}, "path": "v2/{+parent}/workerPools", "response": {"$ref": "GoogleCloudRunV2ListWorkerPoolsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates a WorkerPool.", "flatPath": "v2/projects/{projectsId}/locations/{locationsId}/workerPools/{workerPoolsId}", "httpMethod": "PATCH", "id": "run.projects.locations.workerPools.patch", "parameterOrder": ["name"], "parameters": {"allowMissing": {"description": "Optional. If set to true, and if the WorkerPool does not exist, it will create a new one. The caller must have 'run.workerpools.create' permissions if this is set to true and the WorkerPool does not exist.", "location": "query", "type": "boolean"}, "forceNewRevision": {"description": "Optional. If set to true, a new revision will be created from the template even if the system doesn't detect any changes from the previously deployed revision. This may be useful for cases where the underlying resources need to be recreated or reinitialized. For example if the image is specified by label, but the underlying image digest has changed) or if the container performs deployment initialization work that needs to be performed again.", "location": "query", "type": "boolean"}, "name": {"description": "The fully qualified name of this WorkerPool. In CreateWorkerPoolRequest, this field is ignored, and instead composed from CreateWorkerPoolRequest.parent and CreateWorkerPoolRequest.worker_id. Format: `projects/{project}/locations/{location}/workerPools/{worker_id}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/workerPools/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Optional. The list of fields to be updated.", "format": "google-fieldmask", "location": "query", "type": "string"}, "validateOnly": {"description": "Optional. Indicates that the request should be validated and default values populated, without persisting the request or updating any resources.", "location": "query", "type": "boolean"}}, "path": "v2/{+name}", "request": {"$ref": "GoogleCloudRunV2WorkerPool"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets the IAM Access control policy for the specified WorkerPool. Overwrites any existing policy.", "flatPath": "v2/projects/{projectsId}/locations/{locationsId}/workerPools/{workerPoolsId}:setIamPolicy", "httpMethod": "POST", "id": "run.projects.locations.workerPools.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/workerPools/[^/]+$", "required": true, "type": "string"}}, "path": "v2/{+resource}:setIamPolicy", "request": {"$ref": "GoogleIamV1SetIamPolicyRequest"}, "response": {"$ref": "GoogleIamV1Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"description": "Returns permissions that a caller has on the specified Project. There are no permissions required for making this API call.", "flatPath": "v2/projects/{projectsId}/locations/{locationsId}/workerPools/{workerPoolsId}:testIamPermissions", "httpMethod": "POST", "id": "run.projects.locations.workerPools.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/workerPools/[^/]+$", "required": true, "type": "string"}}, "path": "v2/{+resource}:testIamPermissions", "request": {"$ref": "GoogleIamV1TestIamPermissionsRequest"}, "response": {"$ref": "GoogleIamV1TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"revisions": {"methods": {"delete": {"description": "Deletes a Revision.", "flatPath": "v2/projects/{projectsId}/locations/{locationsId}/workerPools/{workerPoolsId}/revisions/{revisionsId}", "httpMethod": "DELETE", "id": "run.projects.locations.workerPools.revisions.delete", "parameterOrder": ["name"], "parameters": {"etag": {"description": "A system-generated fingerprint for this version of the resource. This may be used to detect modification conflict during updates.", "location": "query", "type": "string"}, "name": {"description": "Required. The name of the Revision to delete. Format: projects/{project}/locations/{location}/services/{service}/revisions/{revision}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/workerPools/[^/]+/revisions/[^/]+$", "required": true, "type": "string"}, "validateOnly": {"description": "Indicates that the request should be validated without actually deleting any resources.", "location": "query", "type": "boolean"}}, "path": "v2/{+name}", "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets information about a Revision.", "flatPath": "v2/projects/{projectsId}/locations/{locationsId}/workerPools/{workerPoolsId}/revisions/{revisionsId}", "httpMethod": "GET", "id": "run.projects.locations.workerPools.revisions.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The full name of the Revision. Format: projects/{project}/locations/{location}/services/{service}/revisions/{revision}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/workerPools/[^/]+/revisions/[^/]+$", "required": true, "type": "string"}}, "path": "v2/{+name}", "response": {"$ref": "GoogleCloudRunV2Revision"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists Revisions from a given Service, or from a given location. Results are sorted by creation time, descending.", "flatPath": "v2/projects/{projectsId}/locations/{locationsId}/workerPools/{workerPoolsId}/revisions", "httpMethod": "GET", "id": "run.projects.locations.workerPools.revisions.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Maximum number of revisions to return in this call.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token received from a previous call to ListRevisions. All other parameters must match.", "location": "query", "type": "string"}, "parent": {"description": "Required. The Service from which the Revisions should be listed. To list all Revisions across Services, use \"-\" instead of Service name. Format: projects/{project}/locations/{location}/services/{service}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/workerPools/[^/]+$", "required": true, "type": "string"}, "showDeleted": {"description": "If true, returns deleted (but unexpired) resources along with active ones.", "location": "query", "type": "boolean"}}, "path": "v2/{+parent}/revisions", "response": {"$ref": "GoogleCloudRunV2ListRevisionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}}}}}}, "revision": "20250718", "rootUrl": "https://run.googleapis.com/", "schemas": {"GoogleCloudRunV2BinaryAuthorization": {"description": "Settings for Binary Authorization feature.", "id": "GoogleCloudRunV2BinaryAuthorization", "properties": {"breakglassJustification": {"description": "Optional. If present, indicates to use Breakglass using this justification. If use_default is False, then it must be empty. For more information on breakglass, see https://cloud.google.com/binary-authorization/docs/using-breakglass", "type": "string"}, "policy": {"description": "Optional. The path to a binary authorization policy. Format: `projects/{project}/platforms/cloudRun/{policy-name}`", "type": "string"}, "useDefault": {"description": "Optional. If True, indicates to use the default project's binary authorization policy. If False, binary authorization will be disabled.", "type": "boolean"}}, "type": "object"}, "GoogleCloudRunV2BuildConfig": {"description": "Describes the Build step of the function that builds a container from the given source.", "id": "GoogleCloudRunV2BuildConfig", "properties": {"baseImage": {"description": "Optional. The base image used to build the function.", "type": "string"}, "enableAutomaticUpdates": {"description": "Optional. Sets whether the function will receive automatic base image updates.", "type": "boolean"}, "environmentVariables": {"additionalProperties": {"type": "string"}, "description": "Optional. User-provided build-time environment variables for the function", "type": "object"}, "functionTarget": {"description": "Optional. The name of the function (as defined in source code) that will be executed. Defaults to the resource name suffix, if not specified. For backward compatibility, if function with given name is not found, then the system will try to use function named \"function\".", "type": "string"}, "imageUri": {"description": "Optional. Artifact Registry URI to store the built image.", "type": "string"}, "name": {"description": "Output only. The Cloud Build name of the latest successful deployment of the function.", "readOnly": true, "type": "string"}, "serviceAccount": {"description": "Optional. Service account to be used for building the container. The format of this field is `projects/{projectId}/serviceAccounts/{serviceAccountEmail}`.", "type": "string"}, "sourceLocation": {"description": "The Cloud Storage bucket URI where the function source code is located.", "type": "string"}, "workerPool": {"description": "Optional. Name of the Cloud Build Custom Worker Pool that should be used to build the Cloud Run function. The format of this field is `projects/{project}/locations/{region}/workerPools/{workerPool}` where `{project}` and `{region}` are the project id and region respectively where the worker pool is defined and `{workerPool}` is the short name of the worker pool.", "type": "string"}}, "type": "object"}, "GoogleCloudRunV2BuildInfo": {"description": "Build information of the image.", "id": "GoogleCloudRunV2BuildInfo", "properties": {"functionTarget": {"description": "Output only. Entry point of the function when the image is a Cloud Run function.", "readOnly": true, "type": "string"}, "sourceLocation": {"description": "Output only. Source code location of the image.", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudRunV2BuildpacksBuild": {"description": "Build the source using Buildpacks.", "id": "GoogleCloudRunV2BuildpacksBuild", "properties": {"baseImage": {"description": "Optional. The base image to use for the build.", "type": "string"}, "cacheImageUri": {"description": "Optional. cache_image_uri is the GCR/AR URL where the cache image will be stored. cache_image_uri is optional and omitting it will disable caching. This URL must be stable across builds. It is used to derive a build-specific temporary URL by substituting the tag with the build ID. The build will clean up the temporary image on a best-effort basis.", "type": "string"}, "enableAutomaticUpdates": {"description": "Optional. Whether or not the application container will be enrolled in automatic base image updates. When true, the application will be built on a scratch base image, so the base layers can be appended at run time.", "type": "boolean"}, "environmentVariables": {"additionalProperties": {"type": "string"}, "description": "Optional. User-provided build-time environment variables.", "type": "object"}, "functionTarget": {"description": "Optional. Name of the function target if the source is a function source. Required for function builds.", "type": "string"}, "projectDescriptor": {"description": "Optional. project_descriptor stores the path to the project descriptor file. When empty, it means that there is no project descriptor file in the source.", "type": "string"}, "runtime": {"deprecated": true, "description": "The runtime name, e.g. 'go113'. Leave blank for generic builds.", "type": "string"}}, "type": "object"}, "GoogleCloudRunV2CancelExecutionRequest": {"description": "Request message for deleting an Execution.", "id": "GoogleCloudRunV2CancelExecutionRequest", "properties": {"etag": {"description": "A system-generated fingerprint for this version of the resource. This may be used to detect modification conflict during updates.", "type": "string"}, "validateOnly": {"description": "Indicates that the request should be validated without actually cancelling any resources.", "type": "boolean"}}, "type": "object"}, "GoogleCloudRunV2CloudSqlInstance": {"description": "Represents a set of Cloud SQL instances. Each one will be available under /cloudsql/[instance]. Visit https://cloud.google.com/sql/docs/mysql/connect-run for more information on how to connect Cloud SQL and Cloud Run.", "id": "GoogleCloudRunV2CloudSqlInstance", "properties": {"instances": {"description": "The Cloud SQL instance connection names, as can be found in https://console.cloud.google.com/sql/instances. Visit https://cloud.google.com/sql/docs/mysql/connect-run for more information on how to connect Cloud SQL and Cloud Run. Format: {project}:{location}:{instance}", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleCloudRunV2Condition": {"description": "Defines a status condition for a resource.", "id": "GoogleCloudRunV2Condition", "properties": {"executionReason": {"description": "Output only. A reason for the execution condition.", "enum": ["EXECUTION_REASON_UNDEFINED", "JOB_STATUS_SERVICE_POLLING_ERROR", "NON_ZERO_EXIT_CODE", "CANCELLED", "CANCELLING", "DELETED"], "enumDescriptions": ["Default value.", "Internal system error getting execution status. System will retry.", "A task reached its retry limit and the last attempt failed due to the user container exiting with a non-zero exit code.", "The execution was cancelled by users.", "The execution is in the process of being cancelled.", "The execution was deleted."], "readOnly": true, "type": "string"}, "lastTransitionTime": {"description": "Last time the condition transitioned from one status to another.", "format": "google-datetime", "type": "string"}, "message": {"description": "Human readable message indicating details about the current status.", "type": "string"}, "reason": {"description": "Output only. A common (service-level) reason for this condition.", "enum": ["COMMON_REASON_UNDEFINED", "UNKNOWN", "REVISION_FAILED", "PROGRESS_DEADLINE_EXCEEDED", "CONTAINER_MISSING", "CONTAINER_PERMISSION_DENIED", "CONTAINER_IMAGE_UNAUTHORIZED", "CONTAINER_IMAGE_AUTHORIZATION_CHECK_FAILED", "ENCRYPTION_KEY_PERMISSION_DENIED", "ENCRYPTION_KEY_CHECK_FAILED", "SECRETS_ACCESS_CHECK_FAILED", "WAITING_FOR_OPERATION", "IMMEDIATE_RETRY", "POSTPONED_RETRY", "INTERNAL", "VPC_NETWORK_NOT_FOUND"], "enumDescriptions": ["Default value.", "Reason unknown. Further details will be in message.", "Revision creation process failed.", "Timed out waiting for completion.", "The container image path is incorrect.", "Insufficient permissions on the container image.", "Container image is not authorized by policy.", "Container image policy authorization check failed.", "Insufficient permissions on encryption key.", "Permission check on encryption key failed.", "At least one Access check on secrets failed.", "Waiting for operation to complete.", "System will retry immediately.", "System will retry later; current attempt failed.", "An internal error occurred. Further information may be in the message.", "User-provided VPC network was not found."], "readOnly": true, "type": "string"}, "revisionReason": {"description": "Output only. A reason for the revision condition.", "enum": ["REVISION_REASON_UNDEFINED", "PENDING", "RESERVE", "RETIRED", "RETIRING", "RECREATING", "HEALTH_CHECK_CONTAINER_ERROR", "CUSTOMIZED_PATH_RESPONSE_PENDING", "MIN_INSTANCES_NOT_PROVISIONED", "ACTIVE_REVISION_LIMIT_REACHED", "NO_DEPLOYMENT", "HEALTH_CHECK_SKIPPED", "MIN_INSTANCES_WARMING"], "enumDescriptions": ["Default value.", "Revision in Pending state.", "Revision is in Reserve state.", "Revision is Retired.", "Revision is being retired.", "Revision is being recreated.", "There was a health check error.", "Health check failed due to user error from customized path of the container. System will retry.", "A revision with min_instance_count > 0 was created and is reserved, but it was not configured to serve traffic, so it's not live. This can also happen momentarily during traffic migration.", "The maximum allowed number of active revisions has been reached.", "There was no deployment defined. This value is no longer used, but Services created in older versions of the API might contain this value.", "A revision's container has no port specified since the revision is of a manually scaled service with 0 instance count", "A revision with min_instance_count > 0 was created and is waiting for enough instances to begin a traffic migration."], "readOnly": true, "type": "string"}, "severity": {"description": "How to interpret failures of this condition, one of Error, Warning, Info", "enum": ["SEVERITY_UNSPECIFIED", "ERROR", "WARNING", "INFO"], "enumDescriptions": ["Unspecified severity", "Error severity.", "Warning severity.", "Info severity."], "type": "string"}, "state": {"description": "State of the condition.", "enum": ["STATE_UNSPECIFIED", "CONDITION_PENDING", "CONDITION_RECONCILING", "CONDITION_FAILED", "CONDITION_SUCCEEDED"], "enumDescriptions": ["The default value. This value is used if the state is omitted.", "Transient state: Reconciliation has not started yet.", "Transient state: reconciliation is still in progress.", "Terminal state: Reconciliation did not succeed.", "Terminal state: Reconciliation completed successfully."], "type": "string"}, "type": {"description": "type is used to communicate the status of the reconciliation process. See also: https://github.com/knative/serving/blob/main/docs/spec/errors.md#error-conditions-and-reporting Types common to all resources include: * \"Ready\": True when the Resource is ready.", "type": "string"}}, "type": "object"}, "GoogleCloudRunV2Container": {"description": "A single application container. This specifies both the container to run, the command to run in the container and the arguments to supply to it. Note that additional arguments can be supplied by the system to the container at runtime.", "id": "GoogleCloudRunV2Container", "properties": {"args": {"description": "Arguments to the entrypoint. The docker image's CMD is used if this is not provided.", "items": {"type": "string"}, "type": "array"}, "baseImageUri": {"description": "Base image for this container. Only supported for services. If set, it indicates that the service is enrolled into automatic base image update.", "type": "string"}, "buildInfo": {"$ref": "GoogleCloudRunV2BuildInfo", "description": "Output only. The build info of the container image.", "readOnly": true}, "command": {"description": "Entrypoint array. Not executed within a shell. The docker image's ENTRYPOINT is used if this is not provided.", "items": {"type": "string"}, "type": "array"}, "dependsOn": {"description": "Names of the containers that must start before this container.", "items": {"type": "string"}, "type": "array"}, "env": {"description": "List of environment variables to set in the container.", "items": {"$ref": "GoogleCloudRunV2EnvVar"}, "type": "array"}, "image": {"description": "Required. Name of the container image in Dockerhub, Google Artifact Registry, or Google Container Registry. If the host is not provided, Dockerhub is assumed.", "type": "string"}, "livenessProbe": {"$ref": "GoogleCloudRunV2Probe", "description": "Periodic probe of container liveness. Container will be restarted if the probe fails."}, "name": {"description": "Name of the container specified as a DNS_LABEL (RFC 1123).", "type": "string"}, "ports": {"description": "List of ports to expose from the container. Only a single port can be specified. The specified ports must be listening on all interfaces (0.0.0.0) within the container to be accessible. If omitted, a port number will be chosen and passed to the container through the PORT environment variable for the container to listen on.", "items": {"$ref": "GoogleCloudRunV2ContainerPort"}, "type": "array"}, "resources": {"$ref": "GoogleCloudRunV2ResourceRequirements", "description": "Compute Resource requirements by this container."}, "startupProbe": {"$ref": "GoogleCloudRunV2Probe", "description": "Startup probe of application within the container. All other probes are disabled if a startup probe is provided, until it succeeds. Container will not be added to service endpoints if the probe fails."}, "volumeMounts": {"description": "Volume to mount into the container's filesystem.", "items": {"$ref": "GoogleCloudRunV2VolumeMount"}, "type": "array"}, "workingDir": {"description": "Container's working directory. If not specified, the container runtime's default will be used, which might be configured in the container image.", "type": "string"}}, "type": "object"}, "GoogleCloudRunV2ContainerOverride": {"description": "Per-container override specification.", "id": "GoogleCloudRunV2ContainerOverride", "properties": {"args": {"description": "Optional. Arguments to the entrypoint. Will replace existing args for override.", "items": {"type": "string"}, "type": "array"}, "clearArgs": {"description": "Optional. True if the intention is to clear out existing args list.", "type": "boolean"}, "env": {"description": "List of environment variables to set in the container. Will be merged with existing env for override.", "items": {"$ref": "GoogleCloudRunV2EnvVar"}, "type": "array"}, "name": {"description": "The name of the container specified as a DNS_LABEL.", "type": "string"}}, "type": "object"}, "GoogleCloudRunV2ContainerPort": {"description": "ContainerPort represents a network port in a single container.", "id": "GoogleCloudRunV2ContainerPort", "properties": {"containerPort": {"description": "Port number the container listens on. This must be a valid TCP port number, 0 < container_port < 65536.", "format": "int32", "type": "integer"}, "name": {"description": "If specified, used to specify which protocol to use. Allowed values are \"http1\" and \"h2c\".", "type": "string"}}, "type": "object"}, "GoogleCloudRunV2DockerBuild": {"description": "Build the source using Docker. This means the source has a Dockerfile.", "id": "GoogleCloudRunV2DockerBuild", "properties": {}, "type": "object"}, "GoogleCloudRunV2EmptyDirVolumeSource": {"description": "In memory (tmpfs) ephemeral storage. It is ephemeral in the sense that when the sandbox is taken down, the data is destroyed with it (it does not persist across sandbox runs).", "id": "GoogleCloudRunV2EmptyDirVolumeSource", "properties": {"medium": {"description": "The medium on which the data is stored. Acceptable values today is only MEMORY or none. When none, the default will currently be backed by memory but could change over time. +optional", "enum": ["MEDIUM_UNSPECIFIED", "MEMORY"], "enumDescriptions": ["When not specified, falls back to the default implementation which is currently in memory (this may change over time).", "Explicitly set the EmptyDir to be in memory. Uses tmpfs."], "type": "string"}, "sizeLimit": {"description": "Limit on the storage usable by this EmptyDir volume. The size limit is also applicable for memory medium. The maximum usage on memory medium EmptyDir would be the minimum value between the SizeLimit specified here and the sum of memory limits of all containers. The default is nil which means that the limit is undefined. More info: https://cloud.google.com/run/docs/configuring/in-memory-volumes#configure-volume. Info in Kubernetes: https://kubernetes.io/docs/concepts/storage/volumes/#emptydir", "type": "string"}}, "type": "object"}, "GoogleCloudRunV2EnvVar": {"description": "EnvVar represents an environment variable present in a Container.", "id": "GoogleCloudRunV2EnvVar", "properties": {"name": {"description": "Required. Name of the environment variable. Must not exceed 32768 characters.", "type": "string"}, "value": {"description": "Literal value of the environment variable. Defaults to \"\", and the maximum length is 32768 bytes. Variable references are not supported in Cloud Run.", "type": "string"}, "valueSource": {"$ref": "GoogleCloudRunV2EnvVarSource", "description": "Source for the environment variable's value."}}, "type": "object"}, "GoogleCloudRunV2EnvVarSource": {"description": "EnvVarSource represents a source for the value of an EnvVar.", "id": "GoogleCloudRunV2EnvVarSource", "properties": {"secretKeyRef": {"$ref": "GoogleCloudRunV2SecretKeySelector", "description": "Selects a secret and a specific version from Cloud Secret Manager."}}, "type": "object"}, "GoogleCloudRunV2Execution": {"description": "Execution represents the configuration of a single execution. A execution an immutable resource that references a container image which is run to completion.", "id": "GoogleCloudRunV2Execution", "properties": {"annotations": {"additionalProperties": {"type": "string"}, "description": "Output only. Unstructured key value map that may be set by external tools to store and arbitrary metadata. They are not queryable and should be preserved when modifying objects.", "readOnly": true, "type": "object"}, "cancelledCount": {"description": "Output only. The number of tasks which reached phase Cancelled.", "format": "int32", "readOnly": true, "type": "integer"}, "completionTime": {"description": "Output only. Represents time when the execution was completed. It is not guaranteed to be set in happens-before order across separate operations.", "format": "google-datetime", "readOnly": true, "type": "string"}, "conditions": {"description": "Output only. The Condition of this Execution, containing its readiness status, and detailed error information in case it did not reach the desired state.", "items": {"$ref": "GoogleCloudRunV2Condition"}, "readOnly": true, "type": "array"}, "createTime": {"description": "Output only. Represents time when the execution was acknowledged by the execution controller. It is not guaranteed to be set in happens-before order across separate operations.", "format": "google-datetime", "readOnly": true, "type": "string"}, "creator": {"description": "Output only. Email address of the authenticated creator.", "readOnly": true, "type": "string"}, "deleteTime": {"description": "Output only. For a deleted resource, the deletion time. It is only populated as a response to a Delete request.", "format": "google-datetime", "readOnly": true, "type": "string"}, "etag": {"description": "Output only. A system-generated fingerprint for this version of the resource. May be used to detect modification conflict during updates.", "readOnly": true, "type": "string"}, "expireTime": {"description": "Output only. For a deleted resource, the time after which it will be permamently deleted. It is only populated as a response to a Delete request.", "format": "google-datetime", "readOnly": true, "type": "string"}, "failedCount": {"description": "Output only. The number of tasks which reached phase Failed.", "format": "int32", "readOnly": true, "type": "integer"}, "generation": {"description": "Output only. A number that monotonically increases every time the user modifies the desired state.", "format": "int64", "readOnly": true, "type": "string"}, "job": {"description": "Output only. The name of the parent Job.", "readOnly": true, "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Output only. Unstructured key value map that can be used to organize and categorize objects. User-provided labels are shared with Google's billing system, so they can be used to filter, or break down billing charges by team, component, environment, state, etc. For more information, visit https://cloud.google.com/resource-manager/docs/creating-managing-labels or https://cloud.google.com/run/docs/configuring/labels", "readOnly": true, "type": "object"}, "launchStage": {"description": "The least stable launch stage needed to create this resource, as defined by [Google Cloud Platform Launch Stages](https://cloud.google.com/terms/launch-stages). Cloud Run supports `ALPHA`, `BETA`, and `GA`. Note that this value might not be what was used as input. For example, if ALPHA was provided as input in the parent resource, but only BETA and GA-level features are were, this field will be BETA.", "enum": ["LAUNCH_STAGE_UNSPECIFIED", "UNIMPLEMENTED", "PRELAUNCH", "EARLY_ACCESS", "ALPHA", "BETA", "GA", "DEPRECATED"], "enumDescriptions": ["Do not use this default value.", "The feature is not yet implemented. Users can not use it.", "Prelaunch features are hidden from users and are only visible internally.", "Early Access features are limited to a closed group of testers. To use these features, you must sign up in advance and sign a Trusted Tester agreement (which includes confidentiality provisions). These features may be unstable, changed in backward-incompatible ways, and are not guaranteed to be released.", "Alpha is a limited availability test for releases before they are cleared for widespread use. By Alpha, all significant design issues are resolved and we are in the process of verifying functionality. Alpha customers need to apply for access, agree to applicable terms, and have their projects allowlisted. Alpha releases don't have to be feature complete, no SLAs are provided, and there are no technical support obligations, but they will be far enough along that customers can actually use them in test environments or for limited-use tests -- just like they would in normal production cases.", "Beta is the point at which we are ready to open a release for any customer to use. There are no SLA or technical support obligations in a Beta release. Products will be complete from a feature perspective, but may have some open outstanding issues. Beta releases are suitable for limited production use cases.", "GA features are open to all developers and are considered stable and fully qualified for production use.", "Deprecated features are scheduled to be shut down and removed. For more information, see the \"Deprecation Policy\" section of our [Terms of Service](https://cloud.google.com/terms/) and the [Google Cloud Platform Subject to the Deprecation Policy](https://cloud.google.com/terms/deprecation) documentation."], "type": "string"}, "logUri": {"description": "Output only. URI where logs for this execution can be found in Cloud Console.", "readOnly": true, "type": "string"}, "name": {"description": "Output only. The unique name of this Execution.", "readOnly": true, "type": "string"}, "observedGeneration": {"description": "Output only. The generation of this Execution. See comments in `reconciling` for additional information on reconciliation process in Cloud Run.", "format": "int64", "readOnly": true, "type": "string"}, "parallelism": {"description": "Output only. Specifies the maximum desired number of tasks the execution should run at any given time. Must be <= task_count. The actual number of tasks running in steady state will be less than this number when ((.spec.task_count - .status.successful) < .spec.parallelism), i.e. when the work left to do is less than max parallelism.", "format": "int32", "readOnly": true, "type": "integer"}, "reconciling": {"description": "Output only. Indicates whether the resource's reconciliation is still in progress. See comments in `Job.reconciling` for additional information on reconciliation process in Cloud Run.", "readOnly": true, "type": "boolean"}, "retriedCount": {"description": "Output only. The number of tasks which have retried at least once.", "format": "int32", "readOnly": true, "type": "integer"}, "runningCount": {"description": "Output only. The number of actively running tasks.", "format": "int32", "readOnly": true, "type": "integer"}, "satisfiesPzs": {"description": "Output only. Reserved for future use.", "readOnly": true, "type": "boolean"}, "startTime": {"description": "Output only. Represents time when the execution started to run. It is not guaranteed to be set in happens-before order across separate operations.", "format": "google-datetime", "readOnly": true, "type": "string"}, "succeededCount": {"description": "Output only. The number of tasks which reached phase Succeeded.", "format": "int32", "readOnly": true, "type": "integer"}, "taskCount": {"description": "Output only. Specifies the desired number of tasks the execution should run. Setting to 1 means that parallelism is limited to 1 and the success of that task signals the success of the execution.", "format": "int32", "readOnly": true, "type": "integer"}, "template": {"$ref": "GoogleCloudRunV2TaskTemplate", "description": "Output only. The template used to create tasks for this execution.", "readOnly": true}, "uid": {"description": "Output only. Server assigned unique identifier for the Execution. The value is a UUID4 string and guaranteed to remain unchanged until the resource is deleted.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. The last-modified time.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudRunV2ExecutionReference": {"description": "Reference to an Execution. Use /Executions.GetExecution with the given name to get full execution including the latest status.", "id": "GoogleCloudRunV2ExecutionReference", "properties": {"completionStatus": {"description": "Status for the execution completion.", "enum": ["COMPLETION_STATUS_UNSPECIFIED", "EXECUTION_SUCCEEDED", "EXECUTION_FAILED", "EXECUTION_RUNNING", "EXECUTION_PENDING", "EXECUTION_CANCELLED"], "enumDescriptions": ["The default value. This value is used if the state is omitted.", "Job execution has succeeded.", "Job execution has failed.", "Job execution is running normally.", "Waiting for backing resources to be provisioned.", "Job execution has been cancelled by the user."], "type": "string"}, "completionTime": {"description": "Creation timestamp of the execution.", "format": "google-datetime", "type": "string"}, "createTime": {"description": "Creation timestamp of the execution.", "format": "google-datetime", "type": "string"}, "deleteTime": {"description": "The deletion time of the execution. It is only populated as a response to a Delete request.", "format": "google-datetime", "type": "string"}, "name": {"description": "Name of the execution.", "type": "string"}}, "type": "object"}, "GoogleCloudRunV2ExecutionTemplate": {"description": "ExecutionTemplate describes the data an execution should have when created from a template.", "id": "GoogleCloudRunV2ExecutionTemplate", "properties": {"annotations": {"additionalProperties": {"type": "string"}, "description": "Unstructured key value map that may be set by external tools to store and arbitrary metadata. They are not queryable and should be preserved when modifying objects. Cloud Run API v2 does not support annotations with `run.googleapis.com`, `cloud.googleapis.com`, `serving.knative.dev`, or `autoscaling.knative.dev` namespaces, and they will be rejected. All system annotations in v1 now have a corresponding field in v2 ExecutionTemplate. This field follows Kubernetes annotations' namespacing, limits, and rules.", "type": "object"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Unstructured key value map that can be used to organize and categorize objects. User-provided labels are shared with Google's billing system, so they can be used to filter, or break down billing charges by team, component, environment, state, etc. For more information, visit https://cloud.google.com/resource-manager/docs/creating-managing-labels or https://cloud.google.com/run/docs/configuring/labels. Cloud Run API v2 does not support labels with `run.googleapis.com`, `cloud.googleapis.com`, `serving.knative.dev`, or `autoscaling.knative.dev` namespaces, and they will be rejected. All system labels in v1 now have a corresponding field in v2 ExecutionTemplate.", "type": "object"}, "parallelism": {"description": "Optional. Specifies the maximum desired number of tasks the execution should run at given time. When the job is run, if this field is 0 or unset, the maximum possible value will be used for that execution. The actual number of tasks running in steady state will be less than this number when there are fewer tasks waiting to be completed remaining, i.e. when the work left to do is less than max parallelism.", "format": "int32", "type": "integer"}, "taskCount": {"description": "Specifies the desired number of tasks the execution should run. Setting to 1 means that parallelism is limited to 1 and the success of that task signals the success of the execution. Defaults to 1.", "format": "int32", "type": "integer"}, "template": {"$ref": "GoogleCloudRunV2TaskTemplate", "description": "Required. Describes the task(s) that will be created when executing an execution."}}, "type": "object"}, "GoogleCloudRunV2ExportImageRequest": {"description": "Request message for exporting Cloud Run image.", "id": "GoogleCloudRunV2ExportImageRequest", "properties": {"destinationRepo": {"description": "Required. The export destination url (the Artifact Registry repo).", "type": "string"}}, "type": "object"}, "GoogleCloudRunV2ExportImageResponse": {"description": "ExportImageResponse contains an operation Id to track the image export operation.", "id": "GoogleCloudRunV2ExportImageResponse", "properties": {"operationId": {"description": "An operation ID used to track the status of image exports tied to the original pod ID in the request.", "type": "string"}}, "type": "object"}, "GoogleCloudRunV2ExportStatusResponse": {"description": "ExportStatusResponse contains the status of image export operation, with the status of each image export job.", "id": "GoogleCloudRunV2ExportStatusResponse", "properties": {"imageExportStatuses": {"description": "The status of each image export job.", "items": {"$ref": "GoogleCloudRunV2ImageExportStatus"}, "type": "array"}, "operationId": {"description": "The operation id.", "type": "string"}, "operationState": {"description": "Output only. The state of the overall export operation.", "enum": ["OPERATION_STATE_UNSPECIFIED", "IN_PROGRESS", "FINISHED"], "enumDescriptions": ["State unspecified.", "Operation still in progress.", "Operation finished."], "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudRunV2GCSVolumeSource": {"description": "Represents a volume backed by a Cloud Storage bucket using Cloud Storage FUSE.", "id": "GoogleCloudRunV2GCSVolumeSource", "properties": {"bucket": {"description": "Cloud Storage Bucket name.", "type": "string"}, "mountOptions": {"description": "A list of additional flags to pass to the gcsfuse CLI. Options should be specified without the leading \"--\".", "items": {"type": "string"}, "type": "array"}, "readOnly": {"description": "If true, the volume will be mounted as read only for all mounts.", "type": "boolean"}}, "type": "object"}, "GoogleCloudRunV2GRPCAction": {"description": "GRPCAction describes an action involving a GRPC port.", "id": "GoogleCloudRunV2GRPCAction", "properties": {"port": {"description": "Optional. Port number of the gRPC service. Number must be in the range 1 to 65535. If not specified, defaults to the exposed port of the container, which is the value of container.ports[0].containerPort.", "format": "int32", "type": "integer"}, "service": {"description": "Optional. Service is the name of the service to place in the gRPC HealthCheckRequest (see https://github.com/grpc/grpc/blob/master/doc/health-checking.md ). If this is not specified, the default behavior is defined by gRPC.", "type": "string"}}, "type": "object"}, "GoogleCloudRunV2HTTPGetAction": {"description": "HTTPGetAction describes an action based on HTTP Get requests.", "id": "GoogleCloudRunV2HTTPGetAction", "properties": {"httpHeaders": {"description": "Optional. Custom headers to set in the request. HTTP allows repeated headers.", "items": {"$ref": "GoogleCloudRunV2HTTPHeader"}, "type": "array"}, "path": {"description": "Optional. Path to access on the HTTP server. Defaults to '/'.", "type": "string"}, "port": {"description": "Optional. Port number to access on the container. Must be in the range 1 to 65535. If not specified, defaults to the exposed port of the container, which is the value of container.ports[0].containerPort.", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleCloudRunV2HTTPHeader": {"description": "HTTPHeader describes a custom header to be used in HTTP probes", "id": "GoogleCloudRunV2HTTPHeader", "properties": {"name": {"description": "Required. The header field name", "type": "string"}, "value": {"description": "Optional. The header field value", "type": "string"}}, "type": "object"}, "GoogleCloudRunV2ImageExportStatus": {"description": "The status of an image export job.", "id": "GoogleCloudRunV2ImageExportStatus", "properties": {"exportJobState": {"description": "Output only. Has the image export job finished (regardless of successful or failure).", "enum": ["EXPORT_JOB_STATE_UNSPECIFIED", "IN_PROGRESS", "FINISHED"], "enumDescriptions": ["State unspecified.", "Job still in progress.", "Job finished."], "readOnly": true, "type": "string"}, "exportedImageDigest": {"description": "The exported image ID as it will appear in Artifact Registry.", "type": "string"}, "status": {"$ref": "UtilStatusProto", "description": "The status of the export task if done."}, "tag": {"description": "The image tag as it will appear in Artifact Registry.", "type": "string"}}, "type": "object"}, "GoogleCloudRunV2InstanceSplit": {"description": "Holds a single instance split entry for the Worker. Allocations can be done to a specific Revision name, or pointing to the latest Ready Revision.", "id": "GoogleCloudRunV2InstanceSplit", "properties": {"percent": {"description": "Specifies percent of the instance split to this Revision. This defaults to zero if unspecified.", "format": "int32", "type": "integer"}, "revision": {"description": "Revision to which to assign this portion of instances, if split allocation is by revision.", "type": "string"}, "type": {"description": "The allocation type for this instance split.", "enum": ["INSTANCE_SPLIT_ALLOCATION_TYPE_UNSPECIFIED", "INSTANCE_SPLIT_ALLOCATION_TYPE_LATEST", "INSTANCE_SPLIT_ALLOCATION_TYPE_REVISION"], "enumDescriptions": ["Unspecified instance allocation type.", "Allocates instances to the Service's latest ready Revision.", "Allocates instances to a Revision by name."], "type": "string"}}, "type": "object"}, "GoogleCloudRunV2InstanceSplitStatus": {"description": "Represents the observed state of a single `InstanceSplit` entry.", "id": "GoogleCloudRunV2InstanceSplitStatus", "properties": {"percent": {"description": "Specifies percent of the instance split to this Revision.", "format": "int32", "type": "integer"}, "revision": {"description": "Revision to which this instance split is assigned.", "type": "string"}, "type": {"description": "The allocation type for this instance split.", "enum": ["INSTANCE_SPLIT_ALLOCATION_TYPE_UNSPECIFIED", "INSTANCE_SPLIT_ALLOCATION_TYPE_LATEST", "INSTANCE_SPLIT_ALLOCATION_TYPE_REVISION"], "enumDescriptions": ["Unspecified instance allocation type.", "Allocates instances to the Service's latest ready Revision.", "Allocates instances to a Revision by name."], "type": "string"}}, "type": "object"}, "GoogleCloudRunV2Job": {"description": "Job represents the configuration of a single job, which references a container image that is run to completion.", "id": "GoogleCloudRunV2Job", "properties": {"annotations": {"additionalProperties": {"type": "string"}, "description": "Unstructured key value map that may be set by external tools to store and arbitrary metadata. They are not queryable and should be preserved when modifying objects. Cloud Run API v2 does not support annotations with `run.googleapis.com`, `cloud.googleapis.com`, `serving.knative.dev`, or `autoscaling.knative.dev` namespaces, and they will be rejected on new resources. All system annotations in v1 now have a corresponding field in v2 Job. This field follows Kubernetes annotations' namespacing, limits, and rules.", "type": "object"}, "binaryAuthorization": {"$ref": "GoogleCloudRunV2BinaryAuthorization", "description": "Settings for the Binary Authorization feature."}, "client": {"description": "Arbitrary identifier for the API client.", "type": "string"}, "clientVersion": {"description": "Arbitrary version identifier for the API client.", "type": "string"}, "conditions": {"description": "Output only. The Conditions of all other associated sub-resources. They contain additional diagnostics information in case the Job does not reach its desired state. See comments in `reconciling` for additional information on reconciliation process in Cloud Run.", "items": {"$ref": "GoogleCloudRunV2Condition"}, "readOnly": true, "type": "array"}, "createTime": {"description": "Output only. The creation time.", "format": "google-datetime", "readOnly": true, "type": "string"}, "creator": {"description": "Output only. Email address of the authenticated creator.", "readOnly": true, "type": "string"}, "deleteTime": {"description": "Output only. The deletion time. It is only populated as a response to a Delete request.", "format": "google-datetime", "readOnly": true, "type": "string"}, "etag": {"description": "Optional. A system-generated fingerprint for this version of the resource. May be used to detect modification conflict during updates.", "type": "string"}, "executionCount": {"description": "Output only. Number of executions created for this job.", "format": "int32", "readOnly": true, "type": "integer"}, "expireTime": {"description": "Output only. For a deleted resource, the time after which it will be permamently deleted.", "format": "google-datetime", "readOnly": true, "type": "string"}, "generation": {"description": "Output only. A number that monotonically increases every time the user modifies the desired state.", "format": "int64", "readOnly": true, "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Unstructured key value map that can be used to organize and categorize objects. User-provided labels are shared with Google's billing system, so they can be used to filter, or break down billing charges by team, component, environment, state, etc. For more information, visit https://cloud.google.com/resource-manager/docs/creating-managing-labels or https://cloud.google.com/run/docs/configuring/labels. Cloud Run API v2 does not support labels with `run.googleapis.com`, `cloud.googleapis.com`, `serving.knative.dev`, or `autoscaling.knative.dev` namespaces, and they will be rejected. All system labels in v1 now have a corresponding field in v2 Job.", "type": "object"}, "lastModifier": {"description": "Output only. Email address of the last authenticated modifier.", "readOnly": true, "type": "string"}, "latestCreatedExecution": {"$ref": "GoogleCloudRunV2ExecutionReference", "description": "Output only. Name of the last created execution.", "readOnly": true}, "launchStage": {"description": "The launch stage as defined by [Google Cloud Platform Launch Stages](https://cloud.google.com/terms/launch-stages). Cloud Run supports `ALPHA`, `BETA`, and `GA`. If no value is specified, GA is assumed. Set the launch stage to a preview stage on input to allow use of preview features in that stage. On read (or output), describes whether the resource uses preview features. For example, if ALPHA is provided as input, but only BETA and GA-level features are used, this field will be BETA on output.", "enum": ["LAUNCH_STAGE_UNSPECIFIED", "UNIMPLEMENTED", "PRELAUNCH", "EARLY_ACCESS", "ALPHA", "BETA", "GA", "DEPRECATED"], "enumDescriptions": ["Do not use this default value.", "The feature is not yet implemented. Users can not use it.", "Prelaunch features are hidden from users and are only visible internally.", "Early Access features are limited to a closed group of testers. To use these features, you must sign up in advance and sign a Trusted Tester agreement (which includes confidentiality provisions). These features may be unstable, changed in backward-incompatible ways, and are not guaranteed to be released.", "Alpha is a limited availability test for releases before they are cleared for widespread use. By Alpha, all significant design issues are resolved and we are in the process of verifying functionality. Alpha customers need to apply for access, agree to applicable terms, and have their projects allowlisted. Alpha releases don't have to be feature complete, no SLAs are provided, and there are no technical support obligations, but they will be far enough along that customers can actually use them in test environments or for limited-use tests -- just like they would in normal production cases.", "Beta is the point at which we are ready to open a release for any customer to use. There are no SLA or technical support obligations in a Beta release. Products will be complete from a feature perspective, but may have some open outstanding issues. Beta releases are suitable for limited production use cases.", "GA features are open to all developers and are considered stable and fully qualified for production use.", "Deprecated features are scheduled to be shut down and removed. For more information, see the \"Deprecation Policy\" section of our [Terms of Service](https://cloud.google.com/terms/) and the [Google Cloud Platform Subject to the Deprecation Policy](https://cloud.google.com/terms/deprecation) documentation."], "type": "string"}, "name": {"description": "The fully qualified name of this Job. Format: projects/{project}/locations/{location}/jobs/{job}", "type": "string"}, "observedGeneration": {"description": "Output only. The generation of this Job. See comments in `reconciling` for additional information on reconciliation process in Cloud Run.", "format": "int64", "readOnly": true, "type": "string"}, "reconciling": {"description": "Output only. Returns true if the Job is currently being acted upon by the system to bring it into the desired state. When a new Job is created, or an existing one is updated, Cloud Run will asynchronously perform all necessary steps to bring the Job to the desired state. This process is called reconciliation. While reconciliation is in process, `observed_generation` and `latest_succeeded_execution`, will have transient values that might mismatch the intended state: Once reconciliation is over (and this field is false), there are two possible outcomes: reconciliation succeeded and the state matches the Job, or there was an error, and reconciliation failed. This state can be found in `terminal_condition.state`. If reconciliation succeeded, the following fields will match: `observed_generation` and `generation`, `latest_succeeded_execution` and `latest_created_execution`. If reconciliation failed, `observed_generation` and `latest_succeeded_execution` will have the state of the last succeeded execution or empty for newly created Job. Additional information on the failure can be found in `terminal_condition` and `conditions`.", "readOnly": true, "type": "boolean"}, "runExecutionToken": {"description": "A unique string used as a suffix for creating a new execution. The Job will become ready when the execution is successfully completed. The sum of job name and token length must be fewer than 63 characters.", "type": "string"}, "satisfiesPzs": {"description": "Output only. Reserved for future use.", "readOnly": true, "type": "boolean"}, "startExecutionToken": {"description": "A unique string used as a suffix creating a new execution. The Job will become ready when the execution is successfully started. The sum of job name and token length must be fewer than 63 characters.", "type": "string"}, "template": {"$ref": "GoogleCloudRunV2ExecutionTemplate", "description": "Required. The template used to create executions for this Job."}, "terminalCondition": {"$ref": "GoogleCloudRunV2Condition", "description": "Output only. The Condition of this Job, containing its readiness status, and detailed error information in case it did not reach the desired state.", "readOnly": true}, "uid": {"description": "Output only. Server assigned unique identifier for the Execution. The value is a UUID4 string and guaranteed to remain unchanged until the resource is deleted.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. The last-modified time.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudRunV2ListExecutionsResponse": {"description": "Response message containing a list of Executions.", "id": "GoogleCloudRunV2ListExecutionsResponse", "properties": {"executions": {"description": "The resulting list of Executions.", "items": {"$ref": "GoogleCloudRunV2Execution"}, "type": "array"}, "nextPageToken": {"description": "A token indicating there are more items than page_size. Use it in the next ListExecutions request to continue.", "type": "string"}}, "type": "object"}, "GoogleCloudRunV2ListJobsResponse": {"description": "Response message containing a list of Jobs.", "id": "GoogleCloudRunV2ListJobsResponse", "properties": {"jobs": {"description": "The resulting list of Jobs.", "items": {"$ref": "GoogleCloudRunV2Job"}, "type": "array"}, "nextPageToken": {"description": "A token indicating there are more items than page_size. Use it in the next ListJobs request to continue.", "type": "string"}}, "type": "object"}, "GoogleCloudRunV2ListRevisionsResponse": {"description": "Response message containing a list of Revisions.", "id": "GoogleCloudRunV2ListRevisionsResponse", "properties": {"nextPageToken": {"description": "A token indicating there are more items than page_size. Use it in the next ListRevisions request to continue.", "type": "string"}, "revisions": {"description": "The resulting list of Revisions.", "items": {"$ref": "GoogleCloudRunV2Revision"}, "type": "array"}}, "type": "object"}, "GoogleCloudRunV2ListServicesResponse": {"description": "Response message containing a list of Services.", "id": "GoogleCloudRunV2ListServicesResponse", "properties": {"nextPageToken": {"description": "A token indicating there are more items than page_size. Use it in the next ListServices request to continue.", "type": "string"}, "services": {"description": "The resulting list of Services.", "items": {"$ref": "GoogleCloudRunV2Service"}, "type": "array"}}, "type": "object"}, "GoogleCloudRunV2ListTasksResponse": {"description": "Response message containing a list of Tasks.", "id": "GoogleCloudRunV2ListTasksResponse", "properties": {"nextPageToken": {"description": "A token indicating there are more items than page_size. Use it in the next ListTasks request to continue.", "type": "string"}, "tasks": {"description": "The resulting list of Tasks.", "items": {"$ref": "GoogleCloudRunV2Task"}, "type": "array"}}, "type": "object"}, "GoogleCloudRunV2ListWorkerPoolsResponse": {"description": "Response message containing a list of WorkerPools.", "id": "GoogleCloudRunV2ListWorkerPoolsResponse", "properties": {"nextPageToken": {"description": "A token indicating there are more items than page_size. Use it in the next ListWorkerPools request to continue.", "type": "string"}, "workerPools": {"description": "The resulting list of WorkerPools.", "items": {"$ref": "GoogleCloudRunV2WorkerPool"}, "type": "array"}}, "type": "object"}, "GoogleCloudRunV2Metadata": {"description": "Metadata represents the JSON encoded generated customer metadata.", "id": "GoogleCloudRunV2Metadata", "properties": {"metadata": {"description": "JSON encoded Google-generated Customer <PERSON>adata for a given resource/project.", "type": "string"}}, "type": "object"}, "GoogleCloudRunV2MultiRegionSettings": {"description": "Settings for multi-region deployment.", "id": "GoogleCloudRunV2MultiRegionSettings", "properties": {"multiRegionId": {"description": "Optional. System-generated unique id for the multi-region Service.", "type": "string"}, "regions": {"description": "Required. List of regions to deploy to, including primary region.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleCloudRunV2NFSVolumeSource": {"description": "Represents an NFS mount.", "id": "GoogleCloudRunV2NFSVolumeSource", "properties": {"path": {"description": "Path that is exported by the NFS server.", "type": "string"}, "readOnly": {"description": "If true, the volume will be mounted as read only for all mounts.", "type": "boolean"}, "server": {"description": "Hostname or IP address of the NFS server", "type": "string"}}, "type": "object"}, "GoogleCloudRunV2NetworkInterface": {"description": "Direct VPC egress settings.", "id": "GoogleCloudRunV2NetworkInterface", "properties": {"network": {"description": "Optional. The VPC network that the Cloud Run resource will be able to send traffic to. At least one of network or subnetwork must be specified. If both network and subnetwork are specified, the given VPC subnetwork must belong to the given VPC network. If network is not specified, it will be looked up from the subnetwork.", "type": "string"}, "subnetwork": {"description": "Optional. The VPC subnetwork that the Cloud Run resource will get IPs from. At least one of network or subnetwork must be specified. If both network and subnetwork are specified, the given VPC subnetwork must belong to the given VPC network. If subnetwork is not specified, the subnetwork with the same name with the network will be used.", "type": "string"}, "tags": {"description": "Optional. Network tags applied to this Cloud Run resource.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleCloudRunV2NodeSelector": {"description": "Hardware constraints configuration.", "id": "GoogleCloudRunV2NodeSelector", "properties": {"accelerator": {"description": "Required. GPU accelerator type to attach to an instance.", "type": "string"}}, "type": "object"}, "GoogleCloudRunV2Overrides": {"description": "RunJob Overrides that contains Execution fields to be overridden.", "id": "GoogleCloudRunV2Overrides", "properties": {"containerOverrides": {"description": "Per container override specification.", "items": {"$ref": "GoogleCloudRunV2ContainerOverride"}, "type": "array"}, "taskCount": {"description": "Optional. The desired number of tasks the execution should run. Will replace existing task_count value.", "format": "int32", "type": "integer"}, "timeout": {"description": "Duration in seconds the task may be active before the system will actively try to mark it failed and kill associated containers. Will replace existing timeout_seconds value.", "format": "google-duration", "type": "string"}}, "type": "object"}, "GoogleCloudRunV2Probe": {"description": "Probe describes a health check to be performed against a container to determine whether it is alive or ready to receive traffic.", "id": "GoogleCloudRunV2Probe", "properties": {"failureThreshold": {"description": "Optional. Minimum consecutive failures for the probe to be considered failed after having succeeded. Defaults to 3. Minimum value is 1.", "format": "int32", "type": "integer"}, "grpc": {"$ref": "GoogleCloudRunV2GRPCAction", "description": "Optional. GRPC specifies an action involving a gRPC port. Exactly one of httpGet, tcpSocket, or grpc must be specified."}, "httpGet": {"$ref": "GoogleCloudRunV2HTTPGetAction", "description": "Optional. HTTPGet specifies the http request to perform. Exactly one of httpGet, tcpSocket, or grpc must be specified."}, "initialDelaySeconds": {"description": "Optional. Number of seconds after the container has started before the probe is initiated. Defaults to 0 seconds. Minimum value is 0. Maximum value for liveness probe is 3600. Maximum value for startup probe is 240.", "format": "int32", "type": "integer"}, "periodSeconds": {"description": "Optional. How often (in seconds) to perform the probe. Default to 10 seconds. Minimum value is 1. Maximum value for liveness probe is 3600. Maximum value for startup probe is 240. Must be greater or equal than timeout_seconds.", "format": "int32", "type": "integer"}, "tcpSocket": {"$ref": "GoogleCloudRunV2TCPSocketAction", "description": "Optional. TCPSocket specifies an action involving a TCP port. Exactly one of httpGet, tcpSocket, or grpc must be specified."}, "timeoutSeconds": {"description": "Optional. Number of seconds after which the probe times out. Defaults to 1 second. Minimum value is 1. Maximum value is 3600. Must be smaller than period_seconds.", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleCloudRunV2ResourceRequirements": {"description": "ResourceRequirements describes the compute resource requirements.", "id": "GoogleCloudRunV2ResourceRequirements", "properties": {"cpuIdle": {"description": "Determines whether CPU is only allocated during requests (true by default). However, if ResourceRequirements is set, the caller must explicitly set this field to true to preserve the default behavior.", "type": "boolean"}, "limits": {"additionalProperties": {"type": "string"}, "description": "Only `memory` and `cpu` keys in the map are supported. Notes: * The only supported values for CPU are '1', '2', '4', and '8'. Setting 4 CPU requires at least 2Gi of memory. For more information, go to https://cloud.google.com/run/docs/configuring/cpu. * For supported 'memory' values and syntax, go to https://cloud.google.com/run/docs/configuring/memory-limits", "type": "object"}, "startupCpuBoost": {"description": "Determines whether CPU should be boosted on startup of a new container instance above the requested CPU threshold, this can help reduce cold-start latency.", "type": "boolean"}}, "type": "object"}, "GoogleCloudRunV2Revision": {"description": "A Revision is an immutable snapshot of code and configuration. A Revision references a container image. Revisions are only created by updates to its parent Service.", "id": "GoogleCloudRunV2Revision", "properties": {"annotations": {"additionalProperties": {"type": "string"}, "description": "Output only. Unstructured key value map that may be set by external tools to store and arbitrary metadata. They are not queryable and should be preserved when modifying objects.", "readOnly": true, "type": "object"}, "conditions": {"description": "Output only. The Condition of this Revision, containing its readiness status, and detailed error information in case it did not reach a serving state.", "items": {"$ref": "GoogleCloudRunV2Condition"}, "readOnly": true, "type": "array"}, "containers": {"description": "Holds the single container that defines the unit of execution for this Revision.", "items": {"$ref": "GoogleCloudRunV2Container"}, "type": "array"}, "createTime": {"description": "Output only. The creation time.", "format": "google-datetime", "readOnly": true, "type": "string"}, "creator": {"description": "Output only. Email address of the authenticated creator.", "readOnly": true, "type": "string"}, "deleteTime": {"description": "Output only. For a deleted resource, the deletion time. It is only populated as a response to a Delete request.", "format": "google-datetime", "readOnly": true, "type": "string"}, "encryptionKey": {"description": "A reference to a customer managed encryption key (CMEK) to use to encrypt this container image. For more information, go to https://cloud.google.com/run/docs/securing/using-cmek", "type": "string"}, "encryptionKeyRevocationAction": {"description": "The action to take if the encryption key is revoked.", "enum": ["ENCRYPTION_KEY_REVOCATION_ACTION_UNSPECIFIED", "PREVENT_NEW", "SHUTDOWN"], "enumDescriptions": ["Unspecified", "Prevents the creation of new instances.", "Shuts down existing instances, and prevents creation of new ones."], "type": "string"}, "encryptionKeyShutdownDuration": {"description": "If encryption_key_revocation_action is SHUTDOWN, the duration before shutting down all instances. The minimum increment is 1 hour.", "format": "google-duration", "type": "string"}, "etag": {"description": "Output only. A system-generated fingerprint for this version of the resource. May be used to detect modification conflict during updates.", "readOnly": true, "type": "string"}, "executionEnvironment": {"description": "The execution environment being used to host this Revision.", "enum": ["EXECUTION_ENVIRONMENT_UNSPECIFIED", "EXECUTION_ENVIRONMENT_GEN1", "EXECUTION_ENVIRONMENT_GEN2"], "enumDescriptions": ["Unspecified", "Uses the First Generation environment.", "Uses Second Generation environment."], "type": "string"}, "expireTime": {"description": "Output only. For a deleted resource, the time after which it will be permamently deleted. It is only populated as a response to a Delete request.", "format": "google-datetime", "readOnly": true, "type": "string"}, "generation": {"description": "Output only. A number that monotonically increases every time the user modifies the desired state.", "format": "int64", "readOnly": true, "type": "string"}, "gpuZonalRedundancyDisabled": {"description": "Optional. Output only. True if GPU zonal redundancy is disabled on this revision.", "readOnly": true, "type": "boolean"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Output only. Unstructured key value map that can be used to organize and categorize objects. User-provided labels are shared with Google's billing system, so they can be used to filter, or break down billing charges by team, component, environment, state, etc. For more information, visit https://cloud.google.com/resource-manager/docs/creating-managing-labels or https://cloud.google.com/run/docs/configuring/labels.", "readOnly": true, "type": "object"}, "launchStage": {"description": "The least stable launch stage needed to create this resource, as defined by [Google Cloud Platform Launch Stages](https://cloud.google.com/terms/launch-stages). Cloud Run supports `ALPHA`, `BETA`, and `GA`. Note that this value might not be what was used as input. For example, if ALPHA was provided as input in the parent resource, but only BETA and GA-level features are were, this field will be BETA.", "enum": ["LAUNCH_STAGE_UNSPECIFIED", "UNIMPLEMENTED", "PRELAUNCH", "EARLY_ACCESS", "ALPHA", "BETA", "GA", "DEPRECATED"], "enumDescriptions": ["Do not use this default value.", "The feature is not yet implemented. Users can not use it.", "Prelaunch features are hidden from users and are only visible internally.", "Early Access features are limited to a closed group of testers. To use these features, you must sign up in advance and sign a Trusted Tester agreement (which includes confidentiality provisions). These features may be unstable, changed in backward-incompatible ways, and are not guaranteed to be released.", "Alpha is a limited availability test for releases before they are cleared for widespread use. By Alpha, all significant design issues are resolved and we are in the process of verifying functionality. Alpha customers need to apply for access, agree to applicable terms, and have their projects allowlisted. Alpha releases don't have to be feature complete, no SLAs are provided, and there are no technical support obligations, but they will be far enough along that customers can actually use them in test environments or for limited-use tests -- just like they would in normal production cases.", "Beta is the point at which we are ready to open a release for any customer to use. There are no SLA or technical support obligations in a Beta release. Products will be complete from a feature perspective, but may have some open outstanding issues. Beta releases are suitable for limited production use cases.", "GA features are open to all developers and are considered stable and fully qualified for production use.", "Deprecated features are scheduled to be shut down and removed. For more information, see the \"Deprecation Policy\" section of our [Terms of Service](https://cloud.google.com/terms/) and the [Google Cloud Platform Subject to the Deprecation Policy](https://cloud.google.com/terms/deprecation) documentation."], "type": "string"}, "logUri": {"description": "Output only. The Google Console URI to obtain logs for the Revision.", "readOnly": true, "type": "string"}, "maxInstanceRequestConcurrency": {"description": "Sets the maximum number of requests that each serving instance can receive.", "format": "int32", "type": "integer"}, "name": {"description": "Output only. The unique name of this Revision.", "readOnly": true, "type": "string"}, "nodeSelector": {"$ref": "GoogleCloudRunV2NodeSelector", "description": "The node selector for the revision."}, "observedGeneration": {"description": "Output only. The generation of this Revision currently serving traffic. See comments in `reconciling` for additional information on reconciliation process in Cloud Run.", "format": "int64", "readOnly": true, "type": "string"}, "reconciling": {"description": "Output only. Indicates whether the resource's reconciliation is still in progress. See comments in `Service.reconciling` for additional information on reconciliation process in Cloud Run.", "readOnly": true, "type": "boolean"}, "satisfiesPzs": {"description": "Output only. Reserved for future use.", "readOnly": true, "type": "boolean"}, "scaling": {"$ref": "GoogleCloudRunV2RevisionScaling", "description": "Scaling settings for this revision."}, "scalingStatus": {"$ref": "GoogleCloudRunV2RevisionScalingStatus", "description": "Output only. The current effective scaling settings for the revision.", "readOnly": true}, "service": {"description": "Output only. The name of the parent service.", "readOnly": true, "type": "string"}, "serviceAccount": {"description": "Email address of the IAM service account associated with the revision of the service. The service account represents the identity of the running revision, and determines what permissions the revision has.", "type": "string"}, "serviceMesh": {"$ref": "GoogleCloudRunV2ServiceMesh", "description": "Enables service mesh connectivity."}, "sessionAffinity": {"description": "Enable session affinity.", "type": "boolean"}, "timeout": {"description": "<PERSON> allowed time for an instance to respond to a request.", "format": "google-duration", "type": "string"}, "uid": {"description": "Output only. Server assigned unique identifier for the Revision. The value is a UUID4 string and guaranteed to remain unchanged until the resource is deleted.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. The last-modified time.", "format": "google-datetime", "readOnly": true, "type": "string"}, "volumes": {"description": "A list of Volumes to make available to containers.", "items": {"$ref": "GoogleCloudRunV2Volume"}, "type": "array"}, "vpcAccess": {"$ref": "GoogleCloudRunV2VpcAccess", "description": "VPC Access configuration for this Revision. For more information, visit https://cloud.google.com/run/docs/configuring/connecting-vpc."}}, "type": "object"}, "GoogleCloudRunV2RevisionScaling": {"description": "Settings for revision-level scaling settings.", "id": "GoogleCloudRunV2RevisionScaling", "properties": {"maxInstanceCount": {"description": "Optional. Maximum number of serving instances that this resource should have. When unspecified, the field is set to the server default value of 100. For more information see https://cloud.google.com/run/docs/configuring/max-instances", "format": "int32", "type": "integer"}, "minInstanceCount": {"description": "Optional. Minimum number of serving instances that this resource should have.", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleCloudRunV2RevisionScalingStatus": {"description": "Effective settings for the current revision", "id": "GoogleCloudRunV2RevisionScalingStatus", "properties": {"desiredMinInstanceCount": {"description": "The current number of min instances provisioned for this revision.", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleCloudRunV2RevisionTemplate": {"description": "RevisionTemplate describes the data a revision should have when created from a template.", "id": "GoogleCloudRunV2RevisionTemplate", "properties": {"annotations": {"additionalProperties": {"type": "string"}, "description": "Optional. Unstructured key value map that may be set by external tools to store and arbitrary metadata. They are not queryable and should be preserved when modifying objects. Cloud Run API v2 does not support annotations with `run.googleapis.com`, `cloud.googleapis.com`, `serving.knative.dev`, or `autoscaling.knative.dev` namespaces, and they will be rejected. All system annotations in v1 now have a corresponding field in v2 RevisionTemplate. This field follows Kubernetes annotations' namespacing, limits, and rules.", "type": "object"}, "containers": {"description": "Holds the single container that defines the unit of execution for this Revision.", "items": {"$ref": "GoogleCloudRunV2Container"}, "type": "array"}, "encryptionKey": {"description": "A reference to a customer managed encryption key (CMEK) to use to encrypt this container image. For more information, go to https://cloud.google.com/run/docs/securing/using-cmek", "type": "string"}, "encryptionKeyRevocationAction": {"description": "Optional. The action to take if the encryption key is revoked.", "enum": ["ENCRYPTION_KEY_REVOCATION_ACTION_UNSPECIFIED", "PREVENT_NEW", "SHUTDOWN"], "enumDescriptions": ["Unspecified", "Prevents the creation of new instances.", "Shuts down existing instances, and prevents creation of new ones."], "type": "string"}, "encryptionKeyShutdownDuration": {"description": "Optional. If encryption_key_revocation_action is SHUTDOWN, the duration before shutting down all instances. The minimum increment is 1 hour.", "format": "google-duration", "type": "string"}, "executionEnvironment": {"description": "Optional. The sandbox environment to host this Revision.", "enum": ["EXECUTION_ENVIRONMENT_UNSPECIFIED", "EXECUTION_ENVIRONMENT_GEN1", "EXECUTION_ENVIRONMENT_GEN2"], "enumDescriptions": ["Unspecified", "Uses the First Generation environment.", "Uses Second Generation environment."], "type": "string"}, "gpuZonalRedundancyDisabled": {"description": "Optional. True if GPU zonal redundancy is disabled on this revision.", "type": "boolean"}, "healthCheckDisabled": {"description": "Optional. Disables health checking containers during deployment.", "type": "boolean"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. Unstructured key value map that can be used to organize and categorize objects. User-provided labels are shared with Google's billing system, so they can be used to filter, or break down billing charges by team, component, environment, state, etc. For more information, visit https://cloud.google.com/resource-manager/docs/creating-managing-labels or https://cloud.google.com/run/docs/configuring/labels. Cloud Run API v2 does not support labels with `run.googleapis.com`, `cloud.googleapis.com`, `serving.knative.dev`, or `autoscaling.knative.dev` namespaces, and they will be rejected. All system labels in v1 now have a corresponding field in v2 RevisionTemplate.", "type": "object"}, "maxInstanceRequestConcurrency": {"description": "Optional. Sets the maximum number of requests that each serving instance can receive. If not specified or 0, concurrency defaults to 80 when requested `CPU >= 1` and defaults to 1 when requested `CPU < 1`.", "format": "int32", "type": "integer"}, "nodeSelector": {"$ref": "GoogleCloudRunV2NodeSelector", "description": "Optional. The node selector for the revision template."}, "revision": {"description": "Optional. The unique name for the revision. If this field is omitted, it will be automatically generated based on the Service name.", "type": "string"}, "scaling": {"$ref": "GoogleCloudRunV2RevisionScaling", "description": "Optional. Scaling settings for this Revision."}, "serviceAccount": {"description": "Optional. Email address of the IAM service account associated with the revision of the service. The service account represents the identity of the running revision, and determines what permissions the revision has. If not provided, the revision will use the project's default service account.", "type": "string"}, "serviceMesh": {"$ref": "GoogleCloudRunV2ServiceMesh", "description": "Optional. Enables service mesh connectivity."}, "sessionAffinity": {"description": "Optional. Enable session affinity.", "type": "boolean"}, "timeout": {"description": "Optional. Max allowed time for an instance to respond to a request.", "format": "google-duration", "type": "string"}, "volumes": {"description": "Optional. A list of Volumes to make available to containers.", "items": {"$ref": "GoogleCloudRunV2Volume"}, "type": "array"}, "vpcAccess": {"$ref": "GoogleCloudRunV2VpcAccess", "description": "Optional. VPC Access configuration to use for this Revision. For more information, visit https://cloud.google.com/run/docs/configuring/connecting-vpc."}}, "type": "object"}, "GoogleCloudRunV2RunJobRequest": {"description": "Request message to create a new Execution of a Job.", "id": "GoogleCloudRunV2RunJobRequest", "properties": {"etag": {"description": "A system-generated fingerprint for this version of the resource. May be used to detect modification conflict during updates.", "type": "string"}, "overrides": {"$ref": "GoogleCloudRunV2Overrides", "description": "Overrides specification for a given execution of a job. If provided, overrides will be applied to update the execution or task spec."}, "validateOnly": {"description": "Indicates that the request should be validated without actually deleting any resources.", "type": "boolean"}}, "type": "object"}, "GoogleCloudRunV2SecretKeySelector": {"description": "SecretEnvVarSource represents a source for the value of an EnvVar.", "id": "GoogleCloudRunV2SecretKeySelector", "properties": {"secret": {"description": "Required. The name of the secret in Cloud Secret Manager. Format: {secret_name} if the secret is in the same project. projects/{project}/secrets/{secret_name} if the secret is in a different project.", "type": "string"}, "version": {"description": "The Cloud Secret Manager secret version. Can be 'latest' for the latest version, an integer for a specific version, or a version alias.", "type": "string"}}, "type": "object"}, "GoogleCloudRunV2SecretVolumeSource": {"description": "The secret's value will be presented as the content of a file whose name is defined in the item path. If no items are defined, the name of the file is the secret.", "id": "GoogleCloudRunV2SecretVolumeSource", "properties": {"defaultMode": {"description": "Integer representation of mode bits to use on created files by default. Must be a value between 0000 and 0777 (octal), defaulting to 0444. Directories within the path are not affected by this setting. Notes * Internally, a umask of 0222 will be applied to any non-zero value. * This is an integer representation of the mode bits. So, the octal integer value should look exactly as the chmod numeric notation with a leading zero. Some examples: for chmod 640 (u=rw,g=r), set to 0640 (octal) or 416 (base-10). For chmod 755 (u=rwx,g=rx,o=rx), set to 0755 (octal) or 493 (base-10). * This might be in conflict with other options that affect the file mode, like fsGroup, and the result can be other mode bits set. This might be in conflict with other options that affect the file mode, like fsGroup, and as a result, other mode bits could be set.", "format": "int32", "type": "integer"}, "items": {"description": "If unspecified, the volume will expose a file whose name is the secret, relative to VolumeMount.mount_path. If specified, the key will be used as the version to fetch from Cloud Secret Manager and the path will be the name of the file exposed in the volume. When items are defined, they must specify a path and a version.", "items": {"$ref": "GoogleCloudRunV2VersionToPath"}, "type": "array"}, "secret": {"description": "Required. The name of the secret in Cloud Secret Manager. Format: {secret} if the secret is in the same project. projects/{project}/secrets/{secret} if the secret is in a different project.", "type": "string"}}, "type": "object"}, "GoogleCloudRunV2Service": {"description": "Service acts as a top-level container that manages a set of configurations and revision templates which implement a network service. Service exists to provide a singular abstraction which can be access controlled, reasoned about, and which encapsulates software lifecycle decisions such as rollout policy and team resource ownership.", "id": "GoogleCloudRunV2Service", "properties": {"annotations": {"additionalProperties": {"type": "string"}, "description": "Optional. Unstructured key value map that may be set by external tools to store and arbitrary metadata. They are not queryable and should be preserved when modifying objects. Cloud Run API v2 does not support annotations with `run.googleapis.com`, `cloud.googleapis.com`, `serving.knative.dev`, or `autoscaling.knative.dev` namespaces, and they will be rejected in new resources. All system annotations in v1 now have a corresponding field in v2 Service. This field follows Kubernetes annotations' namespacing, limits, and rules.", "type": "object"}, "binaryAuthorization": {"$ref": "GoogleCloudRunV2BinaryAuthorization", "description": "Optional. Settings for the Binary Authorization feature."}, "buildConfig": {"$ref": "GoogleCloudRunV2BuildConfig", "description": "Optional. Configuration for building a Cloud Run function."}, "client": {"description": "Arbitrary identifier for the API client.", "type": "string"}, "clientVersion": {"description": "Arbitrary version identifier for the API client.", "type": "string"}, "conditions": {"description": "Output only. The Conditions of all other associated sub-resources. They contain additional diagnostics information in case the Service does not reach its Serving state. See comments in `reconciling` for additional information on reconciliation process in Cloud Run.", "items": {"$ref": "GoogleCloudRunV2Condition"}, "readOnly": true, "type": "array"}, "createTime": {"description": "Output only. The creation time.", "format": "google-datetime", "readOnly": true, "type": "string"}, "creator": {"description": "Output only. Email address of the authenticated creator.", "readOnly": true, "type": "string"}, "customAudiences": {"description": "One or more custom audiences that you want this service to support. Specify each custom audience as the full URL in a string. The custom audiences are encoded in the token and used to authenticate requests. For more information, see https://cloud.google.com/run/docs/configuring/custom-audiences.", "items": {"type": "string"}, "type": "array"}, "defaultUriDisabled": {"description": "Optional. Disables public resolution of the default URI of this service.", "type": "boolean"}, "deleteTime": {"description": "Output only. The deletion time. It is only populated as a response to a Delete request.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "User-provided description of the Service. This field currently has a 512-character limit.", "type": "string"}, "etag": {"description": "Optional. A system-generated fingerprint for this version of the resource. May be used to detect modification conflict during updates.", "type": "string"}, "expireTime": {"description": "Output only. For a deleted resource, the time after which it will be permanently deleted.", "format": "google-datetime", "readOnly": true, "type": "string"}, "generation": {"description": "Output only. A number that monotonically increases every time the user modifies the desired state. Please note that unlike v1, this is an int64 value. As with most Google APIs, its JSON representation will be a `string` instead of an `integer`.", "format": "int64", "readOnly": true, "type": "string"}, "iapEnabled": {"description": "Optional. IAP settings on the Service.", "type": "boolean"}, "ingress": {"description": "Optional. Provides the ingress settings for this Service. On output, returns the currently observed ingress settings, or INGRESS_TRAFFIC_UNSPECIFIED if no revision is active.", "enum": ["INGRESS_TRAFFIC_UNSPECIFIED", "INGRESS_TRAFFIC_ALL", "INGRESS_TRAFFIC_INTERNAL_ONLY", "INGRESS_TRAFFIC_INTERNAL_LOAD_BALANCER", "INGRESS_TRAFFIC_NONE"], "enumDescriptions": ["Unspecified", "All inbound traffic is allowed.", "Only internal traffic is allowed.", "Both internal and Google Cloud Load Balancer traffic is allowed.", "No ingress traffic is allowed."], "type": "string"}, "invokerIamDisabled": {"description": "Optional. Disables IAM permission check for run.routes.invoke for callers of this service. For more information, visit https://cloud.google.com/run/docs/securing/managing-access#invoker_check.", "type": "boolean"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. Unstructured key value map that can be used to organize and categorize objects. User-provided labels are shared with Google's billing system, so they can be used to filter, or break down billing charges by team, component, environment, state, etc. For more information, visit https://cloud.google.com/resource-manager/docs/creating-managing-labels or https://cloud.google.com/run/docs/configuring/labels. Cloud Run API v2 does not support labels with `run.googleapis.com`, `cloud.googleapis.com`, `serving.knative.dev`, or `autoscaling.knative.dev` namespaces, and they will be rejected. All system labels in v1 now have a corresponding field in v2 Service.", "type": "object"}, "lastModifier": {"description": "Output only. Email address of the last authenticated modifier.", "readOnly": true, "type": "string"}, "latestCreatedRevision": {"description": "Output only. Name of the last created revision. See comments in `reconciling` for additional information on reconciliation process in Cloud Run.", "readOnly": true, "type": "string"}, "latestReadyRevision": {"description": "Output only. Name of the latest revision that is serving traffic. See comments in `reconciling` for additional information on reconciliation process in Cloud Run.", "readOnly": true, "type": "string"}, "launchStage": {"description": "Optional. The launch stage as defined by [Google Cloud Platform Launch Stages](https://cloud.google.com/terms/launch-stages). Cloud Run supports `ALPHA`, `BETA`, and `GA`. If no value is specified, GA is assumed. Set the launch stage to a preview stage on input to allow use of preview features in that stage. On read (or output), describes whether the resource uses preview features. For example, if ALPHA is provided as input, but only BETA and GA-level features are used, this field will be BETA on output.", "enum": ["LAUNCH_STAGE_UNSPECIFIED", "UNIMPLEMENTED", "PRELAUNCH", "EARLY_ACCESS", "ALPHA", "BETA", "GA", "DEPRECATED"], "enumDescriptions": ["Do not use this default value.", "The feature is not yet implemented. Users can not use it.", "Prelaunch features are hidden from users and are only visible internally.", "Early Access features are limited to a closed group of testers. To use these features, you must sign up in advance and sign a Trusted Tester agreement (which includes confidentiality provisions). These features may be unstable, changed in backward-incompatible ways, and are not guaranteed to be released.", "Alpha is a limited availability test for releases before they are cleared for widespread use. By Alpha, all significant design issues are resolved and we are in the process of verifying functionality. Alpha customers need to apply for access, agree to applicable terms, and have their projects allowlisted. Alpha releases don't have to be feature complete, no SLAs are provided, and there are no technical support obligations, but they will be far enough along that customers can actually use them in test environments or for limited-use tests -- just like they would in normal production cases.", "Beta is the point at which we are ready to open a release for any customer to use. There are no SLA or technical support obligations in a Beta release. Products will be complete from a feature perspective, but may have some open outstanding issues. Beta releases are suitable for limited production use cases.", "GA features are open to all developers and are considered stable and fully qualified for production use.", "Deprecated features are scheduled to be shut down and removed. For more information, see the \"Deprecation Policy\" section of our [Terms of Service](https://cloud.google.com/terms/) and the [Google Cloud Platform Subject to the Deprecation Policy](https://cloud.google.com/terms/deprecation) documentation."], "type": "string"}, "multiRegionSettings": {"$ref": "GoogleCloudRunV2MultiRegionSettings", "description": "Optional. Settings for multi-region deployment."}, "name": {"description": "The fully qualified name of this Service. In CreateServiceRequest, this field is ignored, and instead composed from CreateServiceRequest.parent and CreateServiceRequest.service_id. Format: projects/{project}/locations/{location}/services/{service_id}", "type": "string"}, "observedGeneration": {"description": "Output only. The generation of this Service currently serving traffic. See comments in `reconciling` for additional information on reconciliation process in Cloud Run. Please note that unlike v1, this is an int64 value. As with most Google APIs, its JSON representation will be a `string` instead of an `integer`.", "format": "int64", "readOnly": true, "type": "string"}, "reconciling": {"description": "Output only. Returns true if the Service is currently being acted upon by the system to bring it into the desired state. When a new Service is created, or an existing one is updated, Cloud Run will asynchronously perform all necessary steps to bring the Service to the desired serving state. This process is called reconciliation. While reconciliation is in process, `observed_generation`, `latest_ready_revision`, `traffic_statuses`, and `uri` will have transient values that might mismatch the intended state: Once reconciliation is over (and this field is false), there are two possible outcomes: reconciliation succeeded and the serving state matches the Service, or there was an error, and reconciliation failed. This state can be found in `terminal_condition.state`. If reconciliation succeeded, the following fields will match: `traffic` and `traffic_statuses`, `observed_generation` and `generation`, `latest_ready_revision` and `latest_created_revision`. If reconciliation failed, `traffic_statuses`, `observed_generation`, and `latest_ready_revision` will have the state of the last serving revision, or empty for newly created Services. Additional information on the failure can be found in `terminal_condition` and `conditions`.", "readOnly": true, "type": "boolean"}, "satisfiesPzs": {"description": "Output only. Reserved for future use.", "readOnly": true, "type": "boolean"}, "scaling": {"$ref": "GoogleCloudRunV2ServiceScaling", "description": "Optional. Specifies service-level scaling settings"}, "template": {"$ref": "GoogleCloudRunV2RevisionTemplate", "description": "Required. The template used to create revisions for this Service."}, "terminalCondition": {"$ref": "GoogleCloudRunV2Condition", "description": "Output only. The Condition of this Service, containing its readiness status, and detailed error information in case it did not reach a serving state. See comments in `reconciling` for additional information on reconciliation process in Cloud Run.", "readOnly": true}, "threatDetectionEnabled": {"description": "Output only. True if Cloud Run Threat Detection monitoring is enabled for the parent project of this Service.", "readOnly": true, "type": "boolean"}, "traffic": {"description": "Optional. Specifies how to distribute traffic over a collection of Revisions belonging to the Service. If traffic is empty or not provided, defaults to 100% traffic to the latest `Ready` Revision.", "items": {"$ref": "GoogleCloudRunV2TrafficTarget"}, "type": "array"}, "trafficStatuses": {"description": "Output only. Detailed status information for corresponding traffic targets. See comments in `reconciling` for additional information on reconciliation process in Cloud Run.", "items": {"$ref": "GoogleCloudRunV2TrafficTargetStatus"}, "readOnly": true, "type": "array"}, "uid": {"description": "Output only. Server assigned unique identifier for the trigger. The value is a UUID4 string and guaranteed to remain unchanged until the resource is deleted.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. The last-modified time.", "format": "google-datetime", "readOnly": true, "type": "string"}, "uri": {"description": "Output only. The main URI in which this Service is serving traffic.", "readOnly": true, "type": "string"}, "urls": {"description": "Output only. All URLs serving traffic for this Service.", "items": {"type": "string"}, "readOnly": true, "type": "array"}}, "type": "object"}, "GoogleCloudRunV2ServiceMesh": {"description": "Settings for Cloud Service Mesh. For more information see https://cloud.google.com/service-mesh/docs/overview.", "id": "GoogleCloudRunV2ServiceMesh", "properties": {"mesh": {"description": "The Mesh resource name. Format: `projects/{project}/locations/global/meshes/{mesh}`, where `{project}` can be project id or number.", "type": "string"}}, "type": "object"}, "GoogleCloudRunV2ServiceScaling": {"description": "Scaling settings applied at the service level rather than at the revision level.", "id": "GoogleCloudRunV2ServiceScaling", "properties": {"manualInstanceCount": {"description": "Optional. total instance count for the service in manual scaling mode. This number of instances is divided among all revisions with specified traffic based on the percent of traffic they are receiving.", "format": "int32", "type": "integer"}, "maxInstanceCount": {"description": "Optional. total max instances for the service. This number of instances is divided among all revisions with specified traffic based on the percent of traffic they are receiving.", "format": "int32", "type": "integer"}, "minInstanceCount": {"description": "Optional. total min instances for the service. This number of instances is divided among all revisions with specified traffic based on the percent of traffic they are receiving.", "format": "int32", "type": "integer"}, "scalingMode": {"description": "Optional. The scaling mode for the service.", "enum": ["SCALING_MODE_UNSPECIFIED", "AUTOMATIC", "MANUAL"], "enumDescriptions": ["Unspecified.", "Scale based on traffic between min and max instances.", "Scale to exactly min instances and ignore max instances."], "type": "string"}}, "type": "object"}, "GoogleCloudRunV2StorageSource": {"description": "Location of the source in an archive file in Google Cloud Storage.", "id": "GoogleCloudRunV2StorageSource", "properties": {"bucket": {"description": "Required. Google Cloud Storage bucket containing the source (see [Bucket Name Requirements](https://cloud.google.com/storage/docs/bucket-naming#requirements)).", "type": "string"}, "generation": {"description": "Optional. Google Cloud Storage generation for the object. If the generation is omitted, the latest generation will be used.", "format": "int64", "type": "string"}, "object": {"description": "Required. Google Cloud Storage object containing the source. This object must be a gzipped archive file (`.tar.gz`) containing source to build.", "type": "string"}}, "type": "object"}, "GoogleCloudRunV2SubmitBuildRequest": {"description": "Request message for submitting a Build.", "id": "GoogleCloudRunV2SubmitBuildRequest", "properties": {"buildpackBuild": {"$ref": "GoogleCloudRunV2BuildpacksBuild", "description": "Build the source using Buildpacks."}, "dockerBuild": {"$ref": "GoogleCloudRunV2DockerBuild", "description": "Build the source using Docker. This means the source has a Dockerfile."}, "imageUri": {"description": "Required. Artifact Registry URI to store the built image.", "type": "string"}, "serviceAccount": {"description": "Optional. The service account to use for the build. If not set, the default Cloud Build service account for the project will be used.", "type": "string"}, "storageSource": {"$ref": "GoogleCloudRunV2StorageSource", "description": "Required. Source for the build."}, "tags": {"description": "Optional. Additional tags to annotate the build.", "items": {"type": "string"}, "type": "array"}, "workerPool": {"description": "Optional. Name of the Cloud Build Custom Worker Pool that should be used to build the function. The format of this field is `projects/{project}/locations/{region}/workerPools/{workerPool}` where `{project}` and `{region}` are the project id and region respectively where the worker pool is defined and `{workerPool}` is the short name of the worker pool.", "type": "string"}}, "type": "object"}, "GoogleCloudRunV2SubmitBuildResponse": {"description": "Response message for submitting a Build.", "id": "GoogleCloudRunV2SubmitBuildResponse", "properties": {"baseImageUri": {"description": "URI of the base builder image in Artifact Registry being used in the build. Used to opt into automatic base image updates.", "type": "string"}, "baseImageWarning": {"description": "Warning message for the base image.", "type": "string"}, "buildOperation": {"$ref": "GoogleLongrunningOperation", "description": "Cloud Build operation to be polled via CloudBuild API."}}, "type": "object"}, "GoogleCloudRunV2TCPSocketAction": {"description": "TCPSocketAction describes an action based on opening a socket", "id": "GoogleCloudRunV2TCPSocketAction", "properties": {"port": {"description": "Optional. Port number to access on the container. Must be in the range 1 to 65535. If not specified, defaults to the exposed port of the container, which is the value of container.ports[0].containerPort.", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleCloudRunV2Task": {"description": "Task represents a single run of a container to completion.", "id": "GoogleCloudRunV2Task", "properties": {"annotations": {"additionalProperties": {"type": "string"}, "description": "Output only. Unstructured key value map that may be set by external tools to store and arbitrary metadata. They are not queryable and should be preserved when modifying objects.", "readOnly": true, "type": "object"}, "completionTime": {"description": "Output only. Represents time when the Task was completed. It is not guaranteed to be set in happens-before order across separate operations.", "format": "google-datetime", "readOnly": true, "type": "string"}, "conditions": {"description": "Output only. The Condition of this Task, containing its readiness status, and detailed error information in case it did not reach the desired state.", "items": {"$ref": "GoogleCloudRunV2Condition"}, "readOnly": true, "type": "array"}, "containers": {"description": "Holds the single container that defines the unit of execution for this task.", "items": {"$ref": "GoogleCloudRunV2Container"}, "type": "array"}, "createTime": {"description": "Output only. Represents time when the task was created by the system. It is not guaranteed to be set in happens-before order across separate operations.", "format": "google-datetime", "readOnly": true, "type": "string"}, "deleteTime": {"description": "Output only. For a deleted resource, the deletion time. It is only populated as a response to a Delete request.", "format": "google-datetime", "readOnly": true, "type": "string"}, "encryptionKey": {"description": "Output only. A reference to a customer managed encryption key (CMEK) to use to encrypt this container image. For more information, go to https://cloud.google.com/run/docs/securing/using-cmek", "readOnly": true, "type": "string"}, "etag": {"description": "Output only. A system-generated fingerprint for this version of the resource. May be used to detect modification conflict during updates.", "readOnly": true, "type": "string"}, "execution": {"description": "Output only. The name of the parent Execution.", "readOnly": true, "type": "string"}, "executionEnvironment": {"description": "The execution environment being used to host this Task.", "enum": ["EXECUTION_ENVIRONMENT_UNSPECIFIED", "EXECUTION_ENVIRONMENT_GEN1", "EXECUTION_ENVIRONMENT_GEN2"], "enumDescriptions": ["Unspecified", "Uses the First Generation environment.", "Uses Second Generation environment."], "type": "string"}, "expireTime": {"description": "Output only. For a deleted resource, the time after which it will be permamently deleted. It is only populated as a response to a Delete request.", "format": "google-datetime", "readOnly": true, "type": "string"}, "generation": {"description": "Output only. A number that monotonically increases every time the user modifies the desired state.", "format": "int64", "readOnly": true, "type": "string"}, "gpuZonalRedundancyDisabled": {"description": "Optional. Output only. True if GPU zonal redundancy is disabled on this task.", "readOnly": true, "type": "boolean"}, "index": {"description": "Output only. Index of the Task, unique per execution, and beginning at 0.", "format": "int32", "readOnly": true, "type": "integer"}, "job": {"description": "Output only. The name of the parent Job.", "readOnly": true, "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Output only. Unstructured key value map that can be used to organize and categorize objects. User-provided labels are shared with Google's billing system, so they can be used to filter, or break down billing charges by team, component, environment, state, etc. For more information, visit https://cloud.google.com/resource-manager/docs/creating-managing-labels or https://cloud.google.com/run/docs/configuring/labels", "readOnly": true, "type": "object"}, "lastAttemptResult": {"$ref": "GoogleCloudRunV2TaskAttemptResult", "description": "Output only. Result of the last attempt of this Task.", "readOnly": true}, "logUri": {"description": "Output only. URI where logs for this execution can be found in Cloud Console.", "readOnly": true, "type": "string"}, "maxRetries": {"description": "Number of retries allowed per Task, before marking this Task failed.", "format": "int32", "type": "integer"}, "name": {"description": "Output only. The unique name of this Task.", "readOnly": true, "type": "string"}, "nodeSelector": {"$ref": "GoogleCloudRunV2NodeSelector", "description": "Output only. The node selector for the task.", "readOnly": true}, "observedGeneration": {"description": "Output only. The generation of this Task. See comments in `Job.reconciling` for additional information on reconciliation process in Cloud Run.", "format": "int64", "readOnly": true, "type": "string"}, "reconciling": {"description": "Output only. Indicates whether the resource's reconciliation is still in progress. See comments in `Job.reconciling` for additional information on reconciliation process in Cloud Run.", "readOnly": true, "type": "boolean"}, "retried": {"description": "Output only. The number of times this Task was retried. Tasks are retried when they fail up to the maxRetries limit.", "format": "int32", "readOnly": true, "type": "integer"}, "satisfiesPzs": {"description": "Output only. Reserved for future use.", "readOnly": true, "type": "boolean"}, "scheduledTime": {"description": "Output only. Represents time when the task was scheduled to run by the system. It is not guaranteed to be set in happens-before order across separate operations.", "format": "google-datetime", "readOnly": true, "type": "string"}, "serviceAccount": {"description": "Email address of the IAM service account associated with the Task of a Job. The service account represents the identity of the running task, and determines what permissions the task has. If not provided, the task will use the project's default service account.", "type": "string"}, "startTime": {"description": "Output only. Represents time when the task started to run. It is not guaranteed to be set in happens-before order across separate operations.", "format": "google-datetime", "readOnly": true, "type": "string"}, "timeout": {"description": "Max allowed time duration the Task may be active before the system will actively try to mark it failed and kill associated containers. This applies per attempt of a task, meaning each retry can run for the full timeout.", "format": "google-duration", "type": "string"}, "uid": {"description": "Output only. Server assigned unique identifier for the Task. The value is a UUID4 string and guaranteed to remain unchanged until the resource is deleted.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. The last-modified time.", "format": "google-datetime", "readOnly": true, "type": "string"}, "volumes": {"description": "A list of Volumes to make available to containers.", "items": {"$ref": "GoogleCloudRunV2Volume"}, "type": "array"}, "vpcAccess": {"$ref": "GoogleCloudRunV2VpcAccess", "description": "Output only. VPC Access configuration to use for this Task. For more information, visit https://cloud.google.com/run/docs/configuring/connecting-vpc.", "readOnly": true}}, "type": "object"}, "GoogleCloudRunV2TaskAttemptResult": {"description": "Result of a task attempt.", "id": "GoogleCloudRunV2TaskAttemptResult", "properties": {"exitCode": {"description": "Output only. The exit code of this attempt. This may be unset if the container was unable to exit cleanly with a code due to some other failure. See status field for possible failure details. At most one of exit_code or term_signal will be set.", "format": "int32", "readOnly": true, "type": "integer"}, "status": {"$ref": "GoogleRpcStatus", "description": "Output only. The status of this attempt. If the status code is OK, then the attempt succeeded.", "readOnly": true}, "termSignal": {"description": "Output only. Termination signal of the container. This is set to non-zero if the container is terminated by the system. At most one of exit_code or term_signal will be set.", "format": "int32", "readOnly": true, "type": "integer"}}, "type": "object"}, "GoogleCloudRunV2TaskTemplate": {"description": "TaskTemplate describes the data a task should have when created from a template.", "id": "GoogleCloudRunV2TaskTemplate", "properties": {"containers": {"description": "Holds the single container that defines the unit of execution for this task.", "items": {"$ref": "GoogleCloudRunV2Container"}, "type": "array"}, "encryptionKey": {"description": "A reference to a customer managed encryption key (CMEK) to use to encrypt this container image. For more information, go to https://cloud.google.com/run/docs/securing/using-cmek", "type": "string"}, "executionEnvironment": {"description": "Optional. The execution environment being used to host this Task.", "enum": ["EXECUTION_ENVIRONMENT_UNSPECIFIED", "EXECUTION_ENVIRONMENT_GEN1", "EXECUTION_ENVIRONMENT_GEN2"], "enumDescriptions": ["Unspecified", "Uses the First Generation environment.", "Uses Second Generation environment."], "type": "string"}, "gpuZonalRedundancyDisabled": {"description": "Optional. True if GPU zonal redundancy is disabled on this task template.", "type": "boolean"}, "maxRetries": {"description": "Number of retries allowed per Task, before marking this Task failed. Defaults to 3.", "format": "int32", "type": "integer"}, "nodeSelector": {"$ref": "GoogleCloudRunV2NodeSelector", "description": "Optional. The node selector for the task template."}, "serviceAccount": {"description": "Optional. Email address of the IAM service account associated with the Task of a Job. The service account represents the identity of the running task, and determines what permissions the task has. If not provided, the task will use the project's default service account.", "type": "string"}, "timeout": {"description": "Optional. Max allowed time duration the Task may be active before the system will actively try to mark it failed and kill associated containers. This applies per attempt of a task, meaning each retry can run for the full timeout. Defaults to 600 seconds.", "format": "google-duration", "type": "string"}, "volumes": {"description": "Optional. A list of Volumes to make available to containers.", "items": {"$ref": "GoogleCloudRunV2Volume"}, "type": "array"}, "vpcAccess": {"$ref": "GoogleCloudRunV2VpcAccess", "description": "Optional. VPC Access configuration to use for this Task. For more information, visit https://cloud.google.com/run/docs/configuring/connecting-vpc."}}, "type": "object"}, "GoogleCloudRunV2TrafficTarget": {"description": "Holds a single traffic routing entry for the Service. Allocations can be done to a specific Revision name, or pointing to the latest Ready Revision.", "id": "GoogleCloudRunV2TrafficTarget", "properties": {"percent": {"description": "Specifies percent of the traffic to this Revision. This defaults to zero if unspecified.", "format": "int32", "type": "integer"}, "revision": {"description": "Revision to which to send this portion of traffic, if traffic allocation is by revision.", "type": "string"}, "tag": {"description": "Indicates a string to be part of the URI to exclusively reference this target.", "type": "string"}, "type": {"description": "The allocation type for this traffic target.", "enum": ["TRAFFIC_TARGET_ALLOCATION_TYPE_UNSPECIFIED", "TRAFFIC_TARGET_ALLOCATION_TYPE_LATEST", "TRAFFIC_TARGET_ALLOCATION_TYPE_REVISION"], "enumDescriptions": ["Unspecified instance allocation type.", "Allocates instances to the Service's latest ready Revision.", "Allocates instances to a Revision by name."], "type": "string"}}, "type": "object"}, "GoogleCloudRunV2TrafficTargetStatus": {"description": "Represents the observed state of a single `TrafficTarget` entry.", "id": "GoogleCloudRunV2TrafficTargetStatus", "properties": {"percent": {"description": "Specifies percent of the traffic to this Revision.", "format": "int32", "type": "integer"}, "revision": {"description": "Revision to which this traffic is sent.", "type": "string"}, "tag": {"description": "Indicates the string used in the URI to exclusively reference this target.", "type": "string"}, "type": {"description": "The allocation type for this traffic target.", "enum": ["TRAFFIC_TARGET_ALLOCATION_TYPE_UNSPECIFIED", "TRAFFIC_TARGET_ALLOCATION_TYPE_LATEST", "TRAFFIC_TARGET_ALLOCATION_TYPE_REVISION"], "enumDescriptions": ["Unspecified instance allocation type.", "Allocates instances to the Service's latest ready Revision.", "Allocates instances to a Revision by name."], "type": "string"}, "uri": {"description": "Displays the target URI.", "type": "string"}}, "type": "object"}, "GoogleCloudRunV2VersionToPath": {"description": "VersionToPath maps a specific version of a secret to a relative file to mount to, relative to VolumeMount's mount_path.", "id": "GoogleCloudRunV2VersionToPath", "properties": {"mode": {"description": "Integer octal mode bits to use on this file, must be a value between 01 and 0777 (octal). If 0 or not set, the Volume's default mode will be used. Notes * Internally, a umask of 0222 will be applied to any non-zero value. * This is an integer representation of the mode bits. So, the octal integer value should look exactly as the chmod numeric notation with a leading zero. Some examples: for chmod 640 (u=rw,g=r), set to 0640 (octal) or 416 (base-10). For chmod 755 (u=rwx,g=rx,o=rx), set to 0755 (octal) or 493 (base-10). * This might be in conflict with other options that affect the file mode, like fsGroup, and the result can be other mode bits set.", "format": "int32", "type": "integer"}, "path": {"description": "Required. The relative path of the secret in the container.", "type": "string"}, "version": {"description": "The Cloud Secret Manager secret version. Can be 'latest' for the latest value, or an integer or a secret alias for a specific version.", "type": "string"}}, "type": "object"}, "GoogleCloudRunV2Volume": {"description": "Volume represents a named volume in a container.", "id": "GoogleCloudRunV2Volume", "properties": {"cloudSqlInstance": {"$ref": "GoogleCloudRunV2CloudSqlInstance", "description": "For Cloud SQL volumes, contains the specific instances that should be mounted. Visit https://cloud.google.com/sql/docs/mysql/connect-run for more information on how to connect Cloud SQL and Cloud Run."}, "emptyDir": {"$ref": "GoogleCloudRunV2EmptyDirVolumeSource", "description": "Ephemeral storage used as a shared volume."}, "gcs": {"$ref": "GoogleCloudRunV2GCSVolumeSource", "description": "Persistent storage backed by a Google Cloud Storage bucket."}, "name": {"description": "Required. Volume's name.", "type": "string"}, "nfs": {"$ref": "GoogleCloudRunV2NFSVolumeSource", "description": "For NFS Voumes, contains the path to the nfs Volume"}, "secret": {"$ref": "GoogleCloudRunV2SecretVolumeSource", "description": "Secret represents a secret that should populate this volume."}}, "type": "object"}, "GoogleCloudRunV2VolumeMount": {"description": "VolumeMount describes a mounting of a Volume within a container.", "id": "GoogleCloudRunV2VolumeMount", "properties": {"mountPath": {"description": "Required. Path within the container at which the volume should be mounted. Must not contain ':'. For Cloud SQL volumes, it can be left empty, or must otherwise be `/cloudsql`. All instances defined in the Volume will be available as `/cloudsql/[instance]`. For more information on Cloud SQL volumes, visit https://cloud.google.com/sql/docs/mysql/connect-run", "type": "string"}, "name": {"description": "Required. This must match the Name of a Volume.", "type": "string"}}, "type": "object"}, "GoogleCloudRunV2VpcAccess": {"description": "VPC Access settings. For more information on sending traffic to a VPC network, visit https://cloud.google.com/run/docs/configuring/connecting-vpc.", "id": "GoogleCloudRunV2VpcAccess", "properties": {"connector": {"description": "VPC Access connector name. Format: `projects/{project}/locations/{location}/connectors/{connector}`, where `{project}` can be project id or number. For more information on sending traffic to a VPC network via a connector, visit https://cloud.google.com/run/docs/configuring/vpc-connectors.", "type": "string"}, "egress": {"description": "Optional. Traffic VPC egress settings. If not provided, it defaults to PRIVATE_RANGES_ONLY.", "enum": ["VPC_EGRESS_UNSPECIFIED", "ALL_TRAFFIC", "PRIVATE_RANGES_ONLY"], "enumDescriptions": ["Unspecified", "All outbound traffic is routed through the VPC connector.", "Only private IP ranges are routed through the VPC connector."], "type": "string"}, "networkInterfaces": {"description": "Optional. Direct VPC egress settings. Currently only single network interface is supported.", "items": {"$ref": "GoogleCloudRunV2NetworkInterface"}, "type": "array"}}, "type": "object"}, "GoogleCloudRunV2WorkerPool": {"description": "WorkerPool acts as a top-level container that manages a set of configurations and revision templates which implement a pull-based workload. WorkerPool exists to provide a singular abstraction which can be access controlled, reasoned about, and which encapsulates software lifecycle decisions such as rollout policy and team resource ownership.", "id": "GoogleCloudRunV2WorkerPool", "properties": {"annotations": {"additionalProperties": {"type": "string"}, "description": "Optional. Unstructured key value map that may be set by external tools to store and arbitrary metadata. They are not queryable and should be preserved when modifying objects. Cloud Run API v2 does not support annotations with `run.googleapis.com`, `cloud.googleapis.com`, `serving.knative.dev`, or `autoscaling.knative.dev` namespaces, and they will be rejected in new resources. All system annotations in v1 now have a corresponding field in v2 WorkerPool. This field follows Kubernetes annotations' namespacing, limits, and rules.", "type": "object"}, "binaryAuthorization": {"$ref": "GoogleCloudRunV2BinaryAuthorization", "description": "Optional. Settings for the Binary Authorization feature."}, "client": {"description": "Arbitrary identifier for the API client.", "type": "string"}, "clientVersion": {"description": "Arbitrary version identifier for the API client.", "type": "string"}, "conditions": {"description": "Output only. The Conditions of all other associated sub-resources. They contain additional diagnostics information in case the WorkerPool does not reach its Serving state. See comments in `reconciling` for additional information on reconciliation process in Cloud Run.", "items": {"$ref": "GoogleCloudRunV2Condition"}, "readOnly": true, "type": "array"}, "createTime": {"description": "Output only. The creation time.", "format": "google-datetime", "readOnly": true, "type": "string"}, "creator": {"description": "Output only. Email address of the authenticated creator.", "readOnly": true, "type": "string"}, "customAudiences": {"description": "One or more custom audiences that you want this worker pool to support. Specify each custom audience as the full URL in a string. The custom audiences are encoded in the token and used to authenticate requests. For more information, see https://cloud.google.com/run/docs/configuring/custom-audiences.", "items": {"type": "string"}, "type": "array"}, "deleteTime": {"description": "Output only. The deletion time. It is only populated as a response to a Delete request.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "User-provided description of the WorkerPool. This field currently has a 512-character limit.", "type": "string"}, "etag": {"description": "Optional. A system-generated fingerprint for this version of the resource. May be used to detect modification conflict during updates.", "type": "string"}, "expireTime": {"description": "Output only. For a deleted resource, the time after which it will be permamently deleted.", "format": "google-datetime", "readOnly": true, "type": "string"}, "generation": {"description": "Output only. A number that monotonically increases every time the user modifies the desired state. Please note that unlike v1, this is an int64 value. As with most Google APIs, its JSON representation will be a `string` instead of an `integer`.", "format": "int64", "readOnly": true, "type": "string"}, "instanceSplitStatuses": {"description": "Output only. Detailed status information for corresponding instance splits. See comments in `reconciling` for additional information on reconciliation process in Cloud Run.", "items": {"$ref": "GoogleCloudRunV2InstanceSplitStatus"}, "readOnly": true, "type": "array"}, "instanceSplits": {"description": "Optional. Specifies how to distribute instances over a collection of Revisions belonging to the WorkerPool. If instance split is empty or not provided, defaults to 100% instances assigned to the latest `Ready` Revision.", "items": {"$ref": "GoogleCloudRunV2InstanceSplit"}, "type": "array"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. Unstructured key value map that can be used to organize and categorize objects. User-provided labels are shared with Google's billing system, so they can be used to filter, or break down billing charges by team, component, environment, state, etc. For more information, visit https://cloud.google.com/resource-manager/docs/creating-managing-labels or https://cloud.google.com/run/docs/configuring/labels. Cloud Run API v2 does not support labels with `run.googleapis.com`, `cloud.googleapis.com`, `serving.knative.dev`, or `autoscaling.knative.dev` namespaces, and they will be rejected. All system labels in v1 now have a corresponding field in v2 WorkerPool.", "type": "object"}, "lastModifier": {"description": "Output only. Email address of the last authenticated modifier.", "readOnly": true, "type": "string"}, "latestCreatedRevision": {"description": "Output only. Name of the last created revision. See comments in `reconciling` for additional information on reconciliation process in Cloud Run.", "readOnly": true, "type": "string"}, "latestReadyRevision": {"description": "Output only. Name of the latest revision that is serving traffic. See comments in `reconciling` for additional information on reconciliation process in Cloud Run.", "readOnly": true, "type": "string"}, "launchStage": {"description": "Optional. The launch stage as defined by [Google Cloud Platform Launch Stages](https://cloud.google.com/terms/launch-stages). Cloud Run supports `ALPHA`, `BETA`, and `GA`. If no value is specified, GA is assumed. Set the launch stage to a preview stage on input to allow use of preview features in that stage. On read (or output), describes whether the resource uses preview features. For example, if ALPHA is provided as input, but only BETA and GA-level features are used, this field will be BETA on output.", "enum": ["LAUNCH_STAGE_UNSPECIFIED", "UNIMPLEMENTED", "PRELAUNCH", "EARLY_ACCESS", "ALPHA", "BETA", "GA", "DEPRECATED"], "enumDescriptions": ["Do not use this default value.", "The feature is not yet implemented. Users can not use it.", "Prelaunch features are hidden from users and are only visible internally.", "Early Access features are limited to a closed group of testers. To use these features, you must sign up in advance and sign a Trusted Tester agreement (which includes confidentiality provisions). These features may be unstable, changed in backward-incompatible ways, and are not guaranteed to be released.", "Alpha is a limited availability test for releases before they are cleared for widespread use. By Alpha, all significant design issues are resolved and we are in the process of verifying functionality. Alpha customers need to apply for access, agree to applicable terms, and have their projects allowlisted. Alpha releases don't have to be feature complete, no SLAs are provided, and there are no technical support obligations, but they will be far enough along that customers can actually use them in test environments or for limited-use tests -- just like they would in normal production cases.", "Beta is the point at which we are ready to open a release for any customer to use. There are no SLA or technical support obligations in a Beta release. Products will be complete from a feature perspective, but may have some open outstanding issues. Beta releases are suitable for limited production use cases.", "GA features are open to all developers and are considered stable and fully qualified for production use.", "Deprecated features are scheduled to be shut down and removed. For more information, see the \"Deprecation Policy\" section of our [Terms of Service](https://cloud.google.com/terms/) and the [Google Cloud Platform Subject to the Deprecation Policy](https://cloud.google.com/terms/deprecation) documentation."], "type": "string"}, "name": {"description": "The fully qualified name of this WorkerPool. In CreateWorkerPoolRequest, this field is ignored, and instead composed from CreateWorkerPoolRequest.parent and CreateWorkerPoolRequest.worker_id. Format: `projects/{project}/locations/{location}/workerPools/{worker_id}`", "type": "string"}, "observedGeneration": {"description": "Output only. The generation of this WorkerPool currently serving traffic. See comments in `reconciling` for additional information on reconciliation process in Cloud Run. Please note that unlike v1, this is an int64 value. As with most Google APIs, its JSON representation will be a `string` instead of an `integer`.", "format": "int64", "readOnly": true, "type": "string"}, "reconciling": {"description": "Output only. Returns true if the WorkerPool is currently being acted upon by the system to bring it into the desired state. When a new WorkerPool is created, or an existing one is updated, Cloud Run will asynchronously perform all necessary steps to bring the WorkerPool to the desired serving state. This process is called reconciliation. While reconciliation is in process, `observed_generation`, `latest_ready_revison`, `traffic_statuses`, and `uri` will have transient values that might mismatch the intended state: Once reconciliation is over (and this field is false), there are two possible outcomes: reconciliation succeeded and the serving state matches the WorkerPool, or there was an error, and reconciliation failed. This state can be found in `terminal_condition.state`. If reconciliation succeeded, the following fields will match: `traffic` and `traffic_statuses`, `observed_generation` and `generation`, `latest_ready_revision` and `latest_created_revision`. If reconciliation failed, `traffic_statuses`, `observed_generation`, and `latest_ready_revision` will have the state of the last serving revision, or empty for newly created WorkerPools. Additional information on the failure can be found in `terminal_condition` and `conditions`.", "readOnly": true, "type": "boolean"}, "satisfiesPzs": {"description": "Output only. Reserved for future use.", "readOnly": true, "type": "boolean"}, "scaling": {"$ref": "GoogleCloudRunV2WorkerPoolScaling", "description": "Optional. Specifies worker-pool-level scaling settings"}, "template": {"$ref": "GoogleCloudRunV2WorkerPoolRevisionTemplate", "description": "Required. The template used to create revisions for this WorkerPool."}, "terminalCondition": {"$ref": "GoogleCloudRunV2Condition", "description": "Output only. The Condition of this WorkerPool, containing its readiness status, and detailed error information in case it did not reach a serving state. See comments in `reconciling` for additional information on reconciliation process in Cloud Run.", "readOnly": true}, "uid": {"description": "Output only. Server assigned unique identifier for the trigger. The value is a UUID4 string and guaranteed to remain unchanged until the resource is deleted.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. The last-modified time.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudRunV2WorkerPoolRevisionTemplate": {"description": "WorkerPoolRevisionTemplate describes the data a worker pool revision should have when created from a template.", "id": "GoogleCloudRunV2WorkerPoolRevisionTemplate", "properties": {"annotations": {"additionalProperties": {"type": "string"}, "description": "Optional. Unstructured key value map that may be set by external tools to store and arbitrary metadata. They are not queryable and should be preserved when modifying objects. Cloud Run API v2 does not support annotations with `run.googleapis.com`, `cloud.googleapis.com`, `serving.knative.dev`, or `autoscaling.knative.dev` namespaces, and they will be rejected. All system annotations in v1 now have a corresponding field in v2 WorkerPoolRevisionTemplate. This field follows Kubernetes annotations' namespacing, limits, and rules.", "type": "object"}, "containers": {"description": "Holds list of the containers that defines the unit of execution for this Revision.", "items": {"$ref": "GoogleCloudRunV2Container"}, "type": "array"}, "encryptionKey": {"description": "A reference to a customer managed encryption key (CMEK) to use to encrypt this container image. For more information, go to https://cloud.google.com/run/docs/securing/using-cmek", "type": "string"}, "encryptionKeyRevocationAction": {"description": "Optional. The action to take if the encryption key is revoked.", "enum": ["ENCRYPTION_KEY_REVOCATION_ACTION_UNSPECIFIED", "PREVENT_NEW", "SHUTDOWN"], "enumDescriptions": ["Unspecified", "Prevents the creation of new instances.", "Shuts down existing instances, and prevents creation of new ones."], "type": "string"}, "encryptionKeyShutdownDuration": {"description": "Optional. If encryption_key_revocation_action is SHUTDOWN, the duration before shutting down all instances. The minimum increment is 1 hour.", "format": "google-duration", "type": "string"}, "gpuZonalRedundancyDisabled": {"description": "Optional. True if GPU zonal redundancy is disabled on this worker pool.", "type": "boolean"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. Unstructured key value map that can be used to organize and categorize objects. User-provided labels are shared with Google's billing system, so they can be used to filter, or break down billing charges by team, component, environment, state, etc. For more information, visit https://cloud.google.com/resource-manager/docs/creating-managing-labels or https://cloud.google.com/run/docs/configuring/labels. Cloud Run API v2 does not support labels with `run.googleapis.com`, `cloud.googleapis.com`, `serving.knative.dev`, or `autoscaling.knative.dev` namespaces, and they will be rejected. All system labels in v1 now have a corresponding field in v2 WorkerPoolRevisionTemplate.", "type": "object"}, "nodeSelector": {"$ref": "GoogleCloudRunV2NodeSelector", "description": "Optional. The node selector for the revision template."}, "revision": {"description": "Optional. The unique name for the revision. If this field is omitted, it will be automatically generated based on the WorkerPool name.", "type": "string"}, "serviceAccount": {"description": "Optional. Email address of the IAM service account associated with the revision of the service. The service account represents the identity of the running revision, and determines what permissions the revision has. If not provided, the revision will use the project's default service account.", "type": "string"}, "serviceMesh": {"$ref": "GoogleCloudRunV2ServiceMesh", "description": "Optional. Enables service mesh connectivity."}, "volumes": {"description": "Optional. A list of Volumes to make available to containers.", "items": {"$ref": "GoogleCloudRunV2Volume"}, "type": "array"}, "vpcAccess": {"$ref": "GoogleCloudRunV2VpcAccess", "description": "Optional. VPC Access configuration to use for this Revision. For more information, visit https://cloud.google.com/run/docs/configuring/connecting-vpc."}}, "type": "object"}, "GoogleCloudRunV2WorkerPoolScaling": {"description": "Worker pool scaling settings.", "id": "GoogleCloudRunV2WorkerPoolScaling", "properties": {"manualInstanceCount": {"description": "Optional. The total number of instances in manual scaling mode.", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleDevtoolsCloudbuildV1ApprovalConfig": {"description": "ApprovalConfig describes configuration for manual approval of a build.", "id": "GoogleDevtoolsCloudbuildV1ApprovalConfig", "properties": {"approvalRequired": {"description": "Whether or not approval is needed. If this is set on a build, it will become pending when created, and will need to be explicitly approved to start.", "type": "boolean"}}, "type": "object"}, "GoogleDevtoolsCloudbuildV1ApprovalResult": {"description": "ApprovalResult describes the decision and associated metadata of a manual approval of a build.", "id": "GoogleDevtoolsCloudbuildV1ApprovalResult", "properties": {"approvalTime": {"description": "Output only. The time when the approval decision was made.", "format": "google-datetime", "readOnly": true, "type": "string"}, "approverAccount": {"description": "Output only. Email of the user that called the ApproveBuild API to approve or reject a build at the time that the API was called.", "readOnly": true, "type": "string"}, "comment": {"description": "Optional. An optional comment for this manual approval result.", "type": "string"}, "decision": {"description": "Required. The decision of this manual approval.", "enum": ["DECISION_UNSPECIFIED", "APPROVED", "REJECTED"], "enumDescriptions": ["Default enum type. This should not be used.", "Build is approved.", "Build is rejected."], "type": "string"}, "url": {"description": "Optional. An optional URL tied to this manual approval result. This field is essentially the same as comment, except that it will be rendered by the UI differently. An example use case is a link to an external job that approved this Build.", "type": "string"}}, "type": "object"}, "GoogleDevtoolsCloudbuildV1ArtifactObjects": {"description": "Files in the workspace to upload to Cloud Storage upon successful completion of all build steps.", "id": "GoogleDevtoolsCloudbuildV1ArtifactObjects", "properties": {"location": {"description": "Cloud Storage bucket and optional object path, in the form \"gs://bucket/path/to/somewhere/\". (see [Bucket Name Requirements](https://cloud.google.com/storage/docs/bucket-naming#requirements)). Files in the workspace matching any path pattern will be uploaded to Cloud Storage with this location as a prefix.", "type": "string"}, "paths": {"description": "Path globs used to match files in the build's workspace.", "items": {"type": "string"}, "type": "array"}, "timing": {"$ref": "GoogleDevtoolsCloudbuildV1TimeSpan", "description": "Output only. Stores timing information for pushing all artifact objects.", "readOnly": true}}, "type": "object"}, "GoogleDevtoolsCloudbuildV1Artifacts": {"description": "Artifacts produced by a build that should be uploaded upon successful completion of all build steps.", "id": "GoogleDevtoolsCloudbuildV1Artifacts", "properties": {"goModules": {"description": "Optional. A list of Go modules to be uploaded to Artifact Registry upon successful completion of all build steps. If any objects fail to be pushed, the build is marked FAILURE.", "items": {"$ref": "GoogleDevtoolsCloudbuildV1GoModule"}, "type": "array"}, "images": {"description": "A list of images to be pushed upon the successful completion of all build steps. The images will be pushed using the builder service account's credentials. The digests of the pushed images will be stored in the Build resource's results field. If any of the images fail to be pushed, the build is marked FAILURE.", "items": {"type": "string"}, "type": "array"}, "mavenArtifacts": {"description": "A list of Maven artifacts to be uploaded to Artifact Registry upon successful completion of all build steps. Artifacts in the workspace matching specified paths globs will be uploaded to the specified Artifact Registry repository using the builder service account's credentials. If any artifacts fail to be pushed, the build is marked FAILURE.", "items": {"$ref": "GoogleDevtoolsCloudbuildV1MavenArtifact"}, "type": "array"}, "npmPackages": {"description": "A list of npm packages to be uploaded to Artifact Registry upon successful completion of all build steps. Npm packages in the specified paths will be uploaded to the specified Artifact Registry repository using the builder service account's credentials. If any packages fail to be pushed, the build is marked FAILURE.", "items": {"$ref": "GoogleDevtoolsCloudbuildV1NpmPackage"}, "type": "array"}, "objects": {"$ref": "GoogleDevtoolsCloudbuildV1ArtifactObjects", "description": "A list of objects to be uploaded to Cloud Storage upon successful completion of all build steps. Files in the workspace matching specified paths globs will be uploaded to the specified Cloud Storage location using the builder service account's credentials. The location and generation of the uploaded objects will be stored in the Build resource's results field. If any objects fail to be pushed, the build is marked FAILURE."}, "pythonPackages": {"description": "A list of Python packages to be uploaded to Artifact Registry upon successful completion of all build steps. The build service account credentials will be used to perform the upload. If any objects fail to be pushed, the build is marked FAILURE.", "items": {"$ref": "GoogleDevtoolsCloudbuildV1PythonPackage"}, "type": "array"}}, "type": "object"}, "GoogleDevtoolsCloudbuildV1Build": {"description": "A build resource in the Cloud Build API. At a high level, a `Build` describes where to find source code, how to build it (for example, the builder image to run on the source), and where to store the built artifacts. Fields can include the following variables, which will be expanded when the build is created: - $PROJECT_ID: the project ID of the build. - $PROJECT_NUMBER: the project number of the build. - $LOCATION: the location/region of the build. - $BUILD_ID: the autogenerated ID of the build. - $REPO_NAME: the source repository name specified by RepoSource. - $BRANCH_NAME: the branch name specified by RepoSource. - $TAG_NAME: the tag name specified by RepoSource. - $REVISION_ID or $COMMIT_SHA: the commit SHA specified by RepoSource or resolved from the specified branch or tag. - $SHORT_SHA: first 7 characters of $REVISION_ID or $COMMIT_SHA.", "id": "GoogleDevtoolsCloudbuildV1Build", "properties": {"approval": {"$ref": "GoogleDevtoolsCloudbuildV1BuildApproval", "description": "Output only. Describes this build's approval configuration, status, and result.", "readOnly": true}, "artifacts": {"$ref": "GoogleDevtoolsCloudbuildV1Artifacts", "description": "Artifacts produced by the build that should be uploaded upon successful completion of all build steps."}, "availableSecrets": {"$ref": "GoogleDevtoolsCloudbuildV1Secrets", "description": "Secrets and secret environment variables."}, "buildTriggerId": {"description": "Output only. The ID of the `BuildTrigger` that triggered this build, if it was triggered automatically.", "readOnly": true, "type": "string"}, "createTime": {"description": "Output only. Time at which the request to create the build was received.", "format": "google-datetime", "readOnly": true, "type": "string"}, "dependencies": {"description": "Optional. Dependencies that the Cloud Build worker will fetch before executing user steps.", "items": {"$ref": "GoogleDevtoolsCloudbuildV1Dependency"}, "type": "array"}, "failureInfo": {"$ref": "GoogleDevtoolsCloudbuildV1FailureInfo", "description": "Output only. Contains information about the build when status=FAILURE.", "readOnly": true}, "finishTime": {"description": "Output only. Time at which execution of the build was finished. The difference between finish_time and start_time is the duration of the build's execution.", "format": "google-datetime", "readOnly": true, "type": "string"}, "gitConfig": {"$ref": "GoogleDevtoolsCloudbuildV1GitConfig", "description": "Optional. Configuration for git operations."}, "id": {"description": "Output only. Unique identifier of the build.", "readOnly": true, "type": "string"}, "images": {"description": "A list of images to be pushed upon the successful completion of all build steps. The images are pushed using the builder service account's credentials. The digests of the pushed images will be stored in the `Build` resource's results field. If any of the images fail to be pushed, the build status is marked `FAILURE`.", "items": {"type": "string"}, "type": "array"}, "logUrl": {"description": "Output only. URL to logs for this build in Google Cloud Console.", "readOnly": true, "type": "string"}, "logsBucket": {"description": "Cloud Storage bucket where logs should be written (see [Bucket Name Requirements](https://cloud.google.com/storage/docs/bucket-naming#requirements)). Logs file names will be of the format `${logs_bucket}/log-${build_id}.txt`.", "type": "string"}, "name": {"description": "Output only. The 'Build' name with format: `projects/{project}/locations/{location}/builds/{build}`, where {build} is a unique identifier generated by the service.", "readOnly": true, "type": "string"}, "options": {"$ref": "GoogleDevtoolsCloudbuildV1BuildOptions", "description": "Special options for this build."}, "projectId": {"description": "Output only. ID of the project.", "readOnly": true, "type": "string"}, "queueTtl": {"description": "TTL in queue for this build. If provided and the build is enqueued longer than this value, the build will expire and the build status will be `EXPIRED`. The TTL starts ticking from create_time.", "format": "google-duration", "type": "string"}, "results": {"$ref": "GoogleDevtoolsCloudbuildV1Results", "description": "Output only. Results of the build.", "readOnly": true}, "secrets": {"description": "Secrets to decrypt using Cloud Key Management Service. Note: Secret Manager is the recommended technique for managing sensitive data with Cloud Build. Use `available_secrets` to configure builds to access secrets from Secret Manager. For instructions, see: https://cloud.google.com/cloud-build/docs/securing-builds/use-secrets", "items": {"$ref": "GoogleDevtoolsCloudbuildV1Secret"}, "type": "array"}, "serviceAccount": {"description": "IAM service account whose credentials will be used at build runtime. Must be of the format `projects/{PROJECT_ID}/serviceAccounts/{ACCOUNT}`. ACCOUNT can be email address or uniqueId of the service account. ", "type": "string"}, "source": {"$ref": "GoogleDevtoolsCloudbuildV1Source", "description": "Optional. The location of the source files to build."}, "sourceProvenance": {"$ref": "GoogleDevtoolsCloudbuildV1SourceProvenance", "description": "Output only. A permanent fixed identifier for source.", "readOnly": true}, "startTime": {"description": "Output only. Time at which execution of the build was started.", "format": "google-datetime", "readOnly": true, "type": "string"}, "status": {"description": "Output only. Status of the build.", "enum": ["STATUS_UNKNOWN", "PENDING", "QUEUED", "WORKING", "SUCCESS", "FAILURE", "INTERNAL_ERROR", "TIMEOUT", "CANCELLED", "EXPIRED"], "enumDescriptions": ["Status of the build is unknown.", "Build has been created and is pending execution and queuing. It has not been queued.", "Build or step is queued; work has not yet begun.", "Build or step is being executed.", "Build or step finished successfully.", "Build or step failed to complete successfully.", "Build or step failed due to an internal cause.", "Build or step took longer than was allowed.", "Build or step was canceled by a user.", "Build was enqueued for longer than the value of `queue_ttl`."], "readOnly": true, "type": "string"}, "statusDetail": {"description": "Output only. Customer-readable message about the current status.", "readOnly": true, "type": "string"}, "steps": {"description": "Required. The operations to be performed on the workspace.", "items": {"$ref": "GoogleDevtoolsCloudbuildV1BuildStep"}, "type": "array"}, "substitutions": {"additionalProperties": {"type": "string"}, "description": "Substitutions data for `Build` resource.", "type": "object"}, "tags": {"description": "Tags for annotation of a `Build`. These are not docker tags.", "items": {"type": "string"}, "type": "array"}, "timeout": {"description": "Amount of time that this build should be allowed to run, to second granularity. If this amount of time elapses, work on the build will cease and the build status will be `TIMEOUT`. `timeout` starts ticking from `startTime`. Default time is 60 minutes.", "format": "google-duration", "type": "string"}, "timing": {"additionalProperties": {"$ref": "GoogleDevtoolsCloudbuildV1TimeSpan"}, "description": "Output only. Stores timing information for phases of the build. Valid keys are: * BUILD: time to execute all build steps. * PUSH: time to push all artifacts including docker images and non docker artifacts. * FETCHSOURCE: time to fetch source. * SETUPBUILD: time to set up build. If the build does not specify source or images, these keys will not be included.", "readOnly": true, "type": "object"}, "warnings": {"description": "Output only. Non-fatal problems encountered during the execution of the build.", "items": {"$ref": "GoogleDevtoolsCloudbuildV1Warning"}, "readOnly": true, "type": "array"}}, "type": "object"}, "GoogleDevtoolsCloudbuildV1BuildApproval": {"description": "BuildApproval describes a build's approval configuration, state, and result.", "id": "GoogleDevtoolsCloudbuildV1BuildApproval", "properties": {"config": {"$ref": "GoogleDevtoolsCloudbuildV1ApprovalConfig", "description": "Output only. Configuration for manual approval of this build.", "readOnly": true}, "result": {"$ref": "GoogleDevtoolsCloudbuildV1ApprovalResult", "description": "Output only. Result of manual approval for this Build.", "readOnly": true}, "state": {"description": "Output only. The state of this build's approval.", "enum": ["STATE_UNSPECIFIED", "PENDING", "APPROVED", "REJECTED", "CANCELLED"], "enumDescriptions": ["Default enum type. This should not be used.", "Build approval is pending.", "Build approval has been approved.", "Build approval has been rejected.", "Build was cancelled while it was still pending approval."], "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleDevtoolsCloudbuildV1BuildOperationMetadata": {"description": "Metadata for build operations.", "id": "GoogleDevtoolsCloudbuildV1BuildOperationMetadata", "properties": {"build": {"$ref": "GoogleDevtoolsCloudbuildV1Build", "description": "The build that the operation is tracking."}}, "type": "object"}, "GoogleDevtoolsCloudbuildV1BuildOptions": {"description": "Optional arguments to enable specific features of builds.", "id": "GoogleDevtoolsCloudbuildV1BuildOptions", "properties": {"automapSubstitutions": {"description": "Option to include built-in and custom substitutions as env variables for all build steps.", "type": "boolean"}, "defaultLogsBucketBehavior": {"description": "Optional. Option to specify how default logs buckets are setup.", "enum": ["DEFAULT_LOGS_BUCKET_BEHAVIOR_UNSPECIFIED", "REGIONAL_USER_OWNED_BUCKET", "LEGACY_BUCKET"], "enumDescriptions": ["Unspecified.", "Bucket is located in user-owned project in the same region as the build. The builder service account must have access to create and write to Cloud Storage buckets in the build project.", "Bucket is located in a Google-owned project and is not regionalized."], "type": "string"}, "diskSizeGb": {"description": "Requested disk size for the VM that runs the build. Note that this is *NOT* \"disk free\"; some of the space will be used by the operating system and build utilities. Also note that this is the minimum disk size that will be allocated for the build -- the build may run with a larger disk than requested. At present, the maximum disk size is 4000GB; builds that request more than the maximum are rejected with an error.", "format": "int64", "type": "string"}, "dynamicSubstitutions": {"description": "Option to specify whether or not to apply bash style string operations to the substitutions. NOTE: this is always enabled for triggered builds and cannot be overridden in the build configuration file.", "type": "boolean"}, "enableStructuredLogging": {"description": "Optional. Option to specify whether structured logging is enabled. If true, JSON-formatted logs are parsed as structured logs.", "type": "boolean"}, "env": {"description": "A list of global environment variable definitions that will exist for all build steps in this build. If a variable is defined in both globally and in a build step, the variable will use the build step value. The elements are of the form \"KEY=VALUE\" for the environment variable \"KEY\" being given the value \"VALUE\".", "items": {"type": "string"}, "type": "array"}, "logStreamingOption": {"description": "Option to define build log streaming behavior to Cloud Storage.", "enum": ["STREAM_DEFAULT", "STREAM_ON", "STREAM_OFF"], "enumDescriptions": ["Service may automatically determine build log streaming behavior.", "Build logs should be streamed to Cloud Storage.", "Build logs should not be streamed to Cloud Storage; they will be written when the build is completed."], "type": "string"}, "logging": {"description": "Option to specify the logging mode, which determines if and where build logs are stored.", "enum": ["LOGGING_UNSPECIFIED", "LEGACY", "GCS_ONLY", "STACKDRIVER_ONLY", "CLOUD_LOGGING_ONLY", "NONE"], "enumDeprecated": [false, false, false, true, false, false], "enumDescriptions": ["The service determines the logging mode. The default is `LEGACY`. Do not rely on the default logging behavior as it may change in the future.", "Build logs are stored in Cloud Logging and Cloud Storage.", "Build logs are stored in Cloud Storage.", "This option is the same as CLOUD_LOGGING_ONLY.", "Build logs are stored in Cloud Logging. Selecting this option will not allow [logs streaming](https://cloud.google.com/sdk/gcloud/reference/builds/log).", "Turn off all logging. No build logs will be captured."], "type": "string"}, "machineType": {"description": "Compute Engine machine type on which to run the build.", "enum": ["UNSPECIFIED", "N1_HIGHCPU_8", "N1_HIGHCPU_32", "E2_HIGHCPU_8", "E2_HIGHCPU_32", "E2_MEDIUM"], "enumDeprecated": [false, true, true, false, false, false], "enumDescriptions": ["Standard machine type.", "Highcpu machine with 8 CPUs.", "Highcpu machine with 32 CPUs.", "Highcpu e2 machine with 8 CPUs.", "Highcpu e2 machine with 32 CPUs.", "E2 machine with 1 CPU."], "type": "string"}, "pool": {"$ref": "GoogleDevtoolsCloudbuildV1PoolOption", "description": "Optional. Specification for execution on a `WorkerPool`. See [running builds in a private pool](https://cloud.google.com/build/docs/private-pools/run-builds-in-private-pool) for more information."}, "pubsubTopic": {"description": "Optional. Option to specify the Pub/Sub topic to receive build status updates.", "type": "string"}, "requestedVerifyOption": {"description": "Requested verifiability options.", "enum": ["NOT_VERIFIED", "VERIFIED"], "enumDescriptions": ["Not a verifiable build (the default).", "Build must be verified."], "type": "string"}, "secretEnv": {"description": "A list of global environment variables, which are encrypted using a Cloud Key Management Service crypto key. These values must be specified in the build's `Secret`. These variables will be available to all build steps in this build.", "items": {"type": "string"}, "type": "array"}, "sourceProvenanceHash": {"description": "Requested hash for SourceProvenance.", "items": {"enum": ["NONE", "SHA256", "MD5", "GO_MODULE_H1", "SHA512"], "enumDescriptions": ["No hash requested.", "Use a sha256 hash.", "Use a md5 hash.", "Dirhash of a Go module's source code which is then hex-encoded.", "Use a sha512 hash."], "type": "string"}, "type": "array"}, "substitutionOption": {"description": "Option to specify behavior when there is an error in the substitution checks. NOTE: this is always set to ALLOW_LOOSE for triggered builds and cannot be overridden in the build configuration file.", "enum": ["MUST_MATCH", "ALLOW_LOOSE"], "enumDescriptions": ["Fails the build if error in substitutions checks, like missing a substitution in the template or in the map.", "Do not fail the build if error in substitutions checks."], "type": "string"}, "volumes": {"description": "Global list of volumes to mount for ALL build steps Each volume is created as an empty volume prior to starting the build process. Upon completion of the build, volumes and their contents are discarded. Global volume names and paths cannot conflict with the volumes defined a build step. Using a global volume in a build with only one step is not valid as it is indicative of a build request with an incorrect configuration.", "items": {"$ref": "GoogleDevtoolsCloudbuildV1Volume"}, "type": "array"}, "workerPool": {"deprecated": true, "description": "This field deprecated; please use `pool.name` instead.", "type": "string"}}, "type": "object"}, "GoogleDevtoolsCloudbuildV1BuildStep": {"description": "A step in the build pipeline.", "id": "GoogleDevtoolsCloudbuildV1BuildStep", "properties": {"allowExitCodes": {"description": "Allow this build step to fail without failing the entire build if and only if the exit code is one of the specified codes. If allow_failure is also specified, this field will take precedence.", "items": {"format": "int32", "type": "integer"}, "type": "array"}, "allowFailure": {"description": "Allow this build step to fail without failing the entire build. If false, the entire build will fail if this step fails. Otherwise, the build will succeed, but this step will still have a failure status. Error information will be reported in the failure_detail field.", "type": "boolean"}, "args": {"description": "A list of arguments that will be presented to the step when it is started. If the image used to run the step's container has an entrypoint, the `args` are used as arguments to that entrypoint. If the image does not define an entrypoint, the first element in args is used as the entrypoint, and the remainder will be used as arguments.", "items": {"type": "string"}, "type": "array"}, "automapSubstitutions": {"description": "Option to include built-in and custom substitutions as env variables for this build step. This option will override the global option in BuildOption.", "type": "boolean"}, "dir": {"description": "Working directory to use when running this step's container. If this value is a relative path, it is relative to the build's working directory. If this value is absolute, it may be outside the build's working directory, in which case the contents of the path may not be persisted across build step executions, unless a `volume` for that path is specified. If the build specifies a `RepoSource` with `dir` and a step with a `dir`, which specifies an absolute path, the `RepoSource` `dir` is ignored for the step's execution.", "type": "string"}, "entrypoint": {"description": "Entrypoint to be used instead of the build step image's default entrypoint. If unset, the image's default entrypoint is used.", "type": "string"}, "env": {"description": "A list of environment variable definitions to be used when running a step. The elements are of the form \"KEY=VALUE\" for the environment variable \"KEY\" being given the value \"VALUE\".", "items": {"type": "string"}, "type": "array"}, "exitCode": {"description": "Output only. Return code from running the step.", "format": "int32", "readOnly": true, "type": "integer"}, "id": {"description": "Unique identifier for this build step, used in `wait_for` to reference this build step as a dependency.", "type": "string"}, "name": {"description": "Required. The name of the container image that will run this particular build step. If the image is available in the host's Docker daemon's cache, it will be run directly. If not, the host will attempt to pull the image first, using the builder service account's credentials if necessary. The Docker daemon's cache will already have the latest versions of all of the officially supported build steps ([https://github.com/GoogleCloudPlatform/cloud-builders](https://github.com/GoogleCloudPlatform/cloud-builders)). The Docker daemon will also have cached many of the layers for some popular images, like \"ubuntu\", \"debian\", but they will be refreshed at the time you attempt to use them. If you built an image in a previous build step, it will be stored in the host's Docker daemon's cache and is available to use as the name for a later build step.", "type": "string"}, "pullTiming": {"$ref": "GoogleDevtoolsCloudbuildV1TimeSpan", "description": "Output only. Stores timing information for pulling this build step's builder image only.", "readOnly": true}, "script": {"description": "A shell script to be executed in the step. When script is provided, the user cannot specify the entrypoint or args.", "type": "string"}, "secretEnv": {"description": "A list of environment variables which are encrypted using a Cloud Key Management Service crypto key. These values must be specified in the build's `Secret`.", "items": {"type": "string"}, "type": "array"}, "status": {"description": "Output only. Status of the build step. At this time, build step status is only updated on build completion; step status is not updated in real-time as the build progresses.", "enum": ["STATUS_UNKNOWN", "PENDING", "QUEUED", "WORKING", "SUCCESS", "FAILURE", "INTERNAL_ERROR", "TIMEOUT", "CANCELLED", "EXPIRED"], "enumDescriptions": ["Status of the build is unknown.", "Build has been created and is pending execution and queuing. It has not been queued.", "Build or step is queued; work has not yet begun.", "Build or step is being executed.", "Build or step finished successfully.", "Build or step failed to complete successfully.", "Build or step failed due to an internal cause.", "Build or step took longer than was allowed.", "Build or step was canceled by a user.", "Build was enqueued for longer than the value of `queue_ttl`."], "readOnly": true, "type": "string"}, "timeout": {"description": "Time limit for executing this build step. If not defined, the step has no time limit and will be allowed to continue to run until either it completes or the build itself times out.", "format": "google-duration", "type": "string"}, "timing": {"$ref": "GoogleDevtoolsCloudbuildV1TimeSpan", "description": "Output only. Stores timing information for executing this build step.", "readOnly": true}, "volumes": {"description": "List of volumes to mount into the build step. Each volume is created as an empty volume prior to execution of the build step. Upon completion of the build, volumes and their contents are discarded. Using a named volume in only one step is not valid as it is indicative of a build request with an incorrect configuration.", "items": {"$ref": "GoogleDevtoolsCloudbuildV1Volume"}, "type": "array"}, "waitFor": {"description": "The ID(s) of the step(s) that this build step depends on. This build step will not start until all the build steps in `wait_for` have completed successfully. If `wait_for` is empty, this build step will start when all previous build steps in the `Build.Steps` list have completed successfully.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleDevtoolsCloudbuildV1BuiltImage": {"description": "An image built by the pipeline.", "id": "GoogleDevtoolsCloudbuildV1BuiltImage", "properties": {"digest": {"description": "Docker Registry 2.0 digest.", "type": "string"}, "name": {"description": "Name used to push the container image to Google Container Registry, as presented to `docker push`.", "type": "string"}, "pushTiming": {"$ref": "GoogleDevtoolsCloudbuildV1TimeSpan", "description": "Output only. Stores timing information for pushing the specified image.", "readOnly": true}}, "type": "object"}, "GoogleDevtoolsCloudbuildV1ConnectedRepository": {"description": "Location of the source in a 2nd-gen Google Cloud Build repository resource.", "id": "GoogleDevtoolsCloudbuildV1ConnectedRepository", "properties": {"dir": {"description": "Optional. Directory, relative to the source root, in which to run the build.", "type": "string"}, "repository": {"description": "Required. Name of the Google Cloud Build repository, formatted as `projects/*/locations/*/connections/*/repositories/*`.", "type": "string"}, "revision": {"description": "Required. The revision to fetch from the Git repository such as a branch, a tag, a commit SHA, or any Git ref.", "type": "string"}}, "type": "object"}, "GoogleDevtoolsCloudbuildV1Dependency": {"description": "A dependency that the Cloud Build worker will fetch before executing user steps.", "id": "GoogleDevtoolsCloudbuildV1Dependency", "properties": {"empty": {"description": "If set to true disable all dependency fetching (ignoring the default source as well).", "type": "boolean"}, "gitSource": {"$ref": "GoogleDevtoolsCloudbuildV1GitSourceDependency", "description": "Represents a git repository as a build dependency."}}, "type": "object"}, "GoogleDevtoolsCloudbuildV1DeveloperConnectConfig": {"description": "This config defines the location of a source through Developer Connect.", "id": "GoogleDevtoolsCloudbuildV1DeveloperConnectConfig", "properties": {"dir": {"description": "Required. Directory, relative to the source root, in which to run the build.", "type": "string"}, "gitRepositoryLink": {"description": "Required. The Developer Connect Git repository link, formatted as `projects/*/locations/*/connections/*/gitRepositoryLink/*`.", "type": "string"}, "revision": {"description": "Required. The revision to fetch from the Git repository such as a branch, a tag, a commit SHA, or any Git ref.", "type": "string"}}, "type": "object"}, "GoogleDevtoolsCloudbuildV1FailureInfo": {"description": "A fatal problem encountered during the execution of the build.", "id": "GoogleDevtoolsCloudbuildV1FailureInfo", "properties": {"detail": {"description": "Explains the failure issue in more detail using hard-coded text.", "type": "string"}, "type": {"description": "The name of the failure.", "enum": ["FAILURE_TYPE_UNSPECIFIED", "PUSH_FAILED", "PUSH_IMAGE_NOT_FOUND", "PUSH_NOT_AUTHORIZED", "LOGGING_FAILURE", "USER_BUILD_STEP", "FETCH_SOURCE_FAILED"], "enumDescriptions": ["Type unspecified", "Unable to push the image to the repository.", "Final image not found.", "Unauthorized push of the final image.", "Backend logging failures. Should retry.", "A build step has failed.", "The source fetching has failed."], "type": "string"}}, "type": "object"}, "GoogleDevtoolsCloudbuildV1FileHashes": {"description": "Container message for hashes of byte content of files, used in SourceProvenance messages to verify integrity of source input to the build.", "id": "GoogleDevtoolsCloudbuildV1FileHashes", "properties": {"fileHash": {"description": "Collection of file hashes.", "items": {"$ref": "GoogleDevtoolsCloudbuildV1Hash"}, "type": "array"}}, "type": "object"}, "GoogleDevtoolsCloudbuildV1GitConfig": {"description": "GitConfig is a configuration for git operations.", "id": "GoogleDevtoolsCloudbuildV1GitConfig", "properties": {"http": {"$ref": "GoogleDevtoolsCloudbuildV1HttpConfig", "description": "Configuration for HTTP related git operations."}}, "type": "object"}, "GoogleDevtoolsCloudbuildV1GitSource": {"description": "Location of the source in any accessible Git repository.", "id": "GoogleDevtoolsCloudbuildV1GitSource", "properties": {"dir": {"description": "Optional. Directory, relative to the source root, in which to run the build. This must be a relative path. If a step's `dir` is specified and is an absolute path, this value is ignored for that step's execution.", "type": "string"}, "revision": {"description": "Optional. The revision to fetch from the Git repository such as a branch, a tag, a commit SHA, or any Git ref. Cloud Build uses `git fetch` to fetch the revision from the Git repository; therefore make sure that the string you provide for `revision` is parsable by the command. For information on string values accepted by `git fetch`, see https://git-scm.com/docs/gitrevisions#_specifying_revisions. For information on `git fetch`, see https://git-scm.com/docs/git-fetch.", "type": "string"}, "url": {"description": "Required. Location of the Git repo to build. This will be used as a `git remote`, see https://git-scm.com/docs/git-remote.", "type": "string"}}, "type": "object"}, "GoogleDevtoolsCloudbuildV1GitSourceDependency": {"description": "Represents a git repository as a build dependency.", "id": "GoogleDevtoolsCloudbuildV1GitSourceDependency", "properties": {"depth": {"description": "Optional. How much history should be fetched for the build (default 1, -1 for all history).", "format": "int64", "type": "string"}, "destPath": {"description": "Required. Where should the files be placed on the worker.", "type": "string"}, "recurseSubmodules": {"description": "Optional. True if submodules should be fetched too (default false).", "type": "boolean"}, "repository": {"$ref": "GoogleDevtoolsCloudbuildV1GitSourceRepository", "description": "Required. The kind of repo (url or dev connect)."}, "revision": {"description": "Required. The revision that we will fetch the repo at.", "type": "string"}}, "type": "object"}, "GoogleDevtoolsCloudbuildV1GitSourceRepository": {"description": "A repository for a git source.", "id": "GoogleDevtoolsCloudbuildV1GitSourceRepository", "properties": {"developerConnect": {"description": "The Developer Connect Git repository link formatted as `projects/*/locations/*/connections/*/gitRepositoryLink/*`", "type": "string"}, "url": {"description": "Location of the Git repository.", "type": "string"}}, "type": "object"}, "GoogleDevtoolsCloudbuildV1GoModule": {"description": "Go module to upload to Artifact Registry upon successful completion of all build steps. A module refers to all dependencies in a go.mod file.", "id": "GoogleDevtoolsCloudbuildV1GoModule", "properties": {"modulePath": {"description": "Optional. The Go module's \"module path\". e.g. example.com/foo/v2", "type": "string"}, "moduleVersion": {"description": "Optional. The Go module's semantic version in the form vX.Y.Z. e.g. v0.1.1 Pre-release identifiers can also be added by appending a dash and dot separated ASCII alphanumeric characters and hyphens. e.g. v0.2.3-alpha.x.12m.5", "type": "string"}, "repositoryLocation": {"description": "Optional. Location of the Artifact Registry repository. i.e. us-east1 Defaults to the build’s location.", "type": "string"}, "repositoryName": {"description": "Optional. Artifact Registry repository name. Specified Go modules will be zipped and uploaded to Artifact Registry with this location as a prefix. e.g. my-go-repo", "type": "string"}, "repositoryProjectId": {"description": "Optional. Project ID of the Artifact Registry repository. Defaults to the build project.", "type": "string"}, "sourcePath": {"description": "Optional. Source path of the go.mod file in the build's workspace. If not specified, this will default to the current directory. e.g. ~/code/go/mypackage", "type": "string"}}, "type": "object"}, "GoogleDevtoolsCloudbuildV1Hash": {"description": "Container message for hash values.", "id": "GoogleDevtoolsCloudbuildV1Hash", "properties": {"type": {"description": "The type of hash that was performed.", "enum": ["NONE", "SHA256", "MD5", "GO_MODULE_H1", "SHA512"], "enumDescriptions": ["No hash requested.", "Use a sha256 hash.", "Use a md5 hash.", "Dirhash of a Go module's source code which is then hex-encoded.", "Use a sha512 hash."], "type": "string"}, "value": {"description": "The hash value.", "format": "byte", "type": "string"}}, "type": "object"}, "GoogleDevtoolsCloudbuildV1HttpConfig": {"description": "HttpConfig is a configuration for HTTP related git operations.", "id": "GoogleDevtoolsCloudbuildV1HttpConfig", "properties": {"proxySecretVersionName": {"description": "SecretVersion resource of the HTTP proxy URL. The Service Account used in the build (either the default Service Account or user-specified Service Account) should have `secretmanager.versions.access` permissions on this secret. The proxy URL should be in format `protocol://@]proxyhost[:port]`.", "type": "string"}}, "type": "object"}, "GoogleDevtoolsCloudbuildV1InlineSecret": {"description": "Pairs a set of secret environment variables mapped to encrypted values with the Cloud KMS key to use to decrypt the value.", "id": "GoogleDevtoolsCloudbuildV1InlineSecret", "properties": {"envMap": {"additionalProperties": {"format": "byte", "type": "string"}, "description": "Map of environment variable name to its encrypted value. Secret environment variables must be unique across all of a build's secrets, and must be used by at least one build step. Values can be at most 64 KB in size. There can be at most 100 secret values across all of a build's secrets.", "type": "object"}, "kmsKeyName": {"description": "Resource name of Cloud KMS crypto key to decrypt the encrypted value. In format: projects/*/locations/*/keyRings/*/cryptoKeys/*", "type": "string"}}, "type": "object"}, "GoogleDevtoolsCloudbuildV1MavenArtifact": {"description": "A Maven artifact to upload to Artifact Registry upon successful completion of all build steps.", "id": "GoogleDevtoolsCloudbuildV1MavenArtifact", "properties": {"artifactId": {"description": "Maven `artifactId` value used when uploading the artifact to Artifact Registry.", "type": "string"}, "groupId": {"description": "Maven `groupId` value used when uploading the artifact to Artifact Registry.", "type": "string"}, "path": {"description": "Optional. Path to an artifact in the build's workspace to be uploaded to Artifact Registry. This can be either an absolute path, e.g. /workspace/my-app/target/my-app-1.0.SNAPSHOT.jar or a relative path from /workspace, e.g. my-app/target/my-app-1.0.SNAPSHOT.jar.", "type": "string"}, "repository": {"description": "Artifact Registry repository, in the form \"https://$REGION-maven.pkg.dev/$PROJECT/$REPOSITORY\" Artifact in the workspace specified by path will be uploaded to Artifact Registry with this location as a prefix.", "type": "string"}, "version": {"description": "Maven `version` value used when uploading the artifact to Artifact Registry.", "type": "string"}}, "type": "object"}, "GoogleDevtoolsCloudbuildV1NpmPackage": {"description": "Npm package to upload to Artifact Registry upon successful completion of all build steps.", "id": "GoogleDevtoolsCloudbuildV1NpmPackage", "properties": {"packagePath": {"description": "Path to the package.json. e.g. workspace/path/to/package", "type": "string"}, "repository": {"description": "Artifact Registry repository, in the form \"https://$REGION-npm.pkg.dev/$PROJECT/$REPOSITORY\" Npm package in the workspace specified by path will be zipped and uploaded to Artifact Registry with this location as a prefix.", "type": "string"}}, "type": "object"}, "GoogleDevtoolsCloudbuildV1PoolOption": {"description": "Details about how a build should be executed on a `WorkerPool`. See [running builds in a private pool](https://cloud.google.com/build/docs/private-pools/run-builds-in-private-pool) for more information.", "id": "GoogleDevtoolsCloudbuildV1PoolOption", "properties": {"name": {"description": "The `WorkerPool` resource to execute the build on. You must have `cloudbuild.workerpools.use` on the project hosting the WorkerPool. Format projects/{project}/locations/{location}/workerPools/{workerPoolId}", "type": "string"}}, "type": "object"}, "GoogleDevtoolsCloudbuildV1PythonPackage": {"description": "Python package to upload to Artifact Registry upon successful completion of all build steps. A package can encapsulate multiple objects to be uploaded to a single repository.", "id": "GoogleDevtoolsCloudbuildV1PythonPackage", "properties": {"paths": {"description": "Path globs used to match files in the build's workspace. For Python/ Twine, this is usually `dist/*`, and sometimes additionally an `.asc` file.", "items": {"type": "string"}, "type": "array"}, "repository": {"description": "Artifact Registry repository, in the form \"https://$REGION-python.pkg.dev/$PROJECT/$REPOSITORY\" Files in the workspace matching any path pattern will be uploaded to Artifact Registry with this location as a prefix.", "type": "string"}}, "type": "object"}, "GoogleDevtoolsCloudbuildV1RepoSource": {"description": "Location of the source in a Google Cloud Source Repository.", "id": "GoogleDevtoolsCloudbuildV1RepoSource", "properties": {"branchName": {"description": "Regex matching branches to build. The syntax of the regular expressions accepted is the syntax accepted by RE2 and described at https://github.com/google/re2/wiki/Syntax", "type": "string"}, "commitSha": {"description": "Explicit commit SHA to build.", "type": "string"}, "dir": {"description": "Optional. Directory, relative to the source root, in which to run the build. This must be a relative path. If a step's `dir` is specified and is an absolute path, this value is ignored for that step's execution.", "type": "string"}, "invertRegex": {"description": "Optional. Only trigger a build if the revision regex does NOT match the revision regex.", "type": "boolean"}, "projectId": {"description": "Optional. ID of the project that owns the Cloud Source Repository. If omitted, the project ID requesting the build is assumed.", "type": "string"}, "repoName": {"description": "Required. Name of the Cloud Source Repository.", "type": "string"}, "substitutions": {"additionalProperties": {"type": "string"}, "description": "Optional. Substitutions to use in a triggered build. Should only be used with RunBuildTrigger", "type": "object"}, "tagName": {"description": "Regex matching tags to build. The syntax of the regular expressions accepted is the syntax accepted by RE2 and described at https://github.com/google/re2/wiki/Syntax", "type": "string"}}, "type": "object"}, "GoogleDevtoolsCloudbuildV1Results": {"description": "Artifacts created by the build pipeline.", "id": "GoogleDevtoolsCloudbuildV1Results", "properties": {"artifactManifest": {"description": "Path to the artifact manifest for non-container artifacts uploaded to Cloud Storage. Only populated when artifacts are uploaded to Cloud Storage.", "type": "string"}, "artifactTiming": {"$ref": "GoogleDevtoolsCloudbuildV1TimeSpan", "description": "Time to push all non-container artifacts to Cloud Storage."}, "buildStepImages": {"description": "List of build step digests, in the order corresponding to build step indices.", "items": {"type": "string"}, "type": "array"}, "buildStepOutputs": {"description": "List of build step outputs, produced by builder images, in the order corresponding to build step indices. [Cloud Builders](https://cloud.google.com/cloud-build/docs/cloud-builders) can produce this output by writing to `$BUILDER_OUTPUT/output`. Only the first 50KB of data is stored. Note that the `$BUILDER_OUTPUT` variable is read-only and can't be substituted.", "items": {"format": "byte", "type": "string"}, "type": "array"}, "goModules": {"description": "Optional. Go module artifacts uploaded to Artifact Registry at the end of the build.", "items": {"$ref": "GoogleDevtoolsCloudbuildV1UploadedGoModule"}, "type": "array"}, "images": {"description": "Container images that were built as a part of the build.", "items": {"$ref": "GoogleDevtoolsCloudbuildV1BuiltImage"}, "type": "array"}, "mavenArtifacts": {"description": "Maven artifacts uploaded to Artifact Registry at the end of the build.", "items": {"$ref": "GoogleDevtoolsCloudbuildV1UploadedMavenArtifact"}, "type": "array"}, "npmPackages": {"description": "Npm packages uploaded to Artifact Registry at the end of the build.", "items": {"$ref": "GoogleDevtoolsCloudbuildV1UploadedNpmPackage"}, "type": "array"}, "numArtifacts": {"description": "Number of non-container artifacts uploaded to Cloud Storage. Only populated when artifacts are uploaded to Cloud Storage.", "format": "int64", "type": "string"}, "pythonPackages": {"description": "Python artifacts uploaded to Artifact Registry at the end of the build.", "items": {"$ref": "GoogleDevtoolsCloudbuildV1UploadedPythonPackage"}, "type": "array"}}, "type": "object"}, "GoogleDevtoolsCloudbuildV1Secret": {"description": "Pairs a set of secret environment variables containing encrypted values with the Cloud KMS key to use to decrypt the value. Note: Use `kmsKeyName` with `available_secrets` instead of using `kmsKeyName` with `secret`. For instructions see: https://cloud.google.com/cloud-build/docs/securing-builds/use-encrypted-credentials.", "id": "GoogleDevtoolsCloudbuildV1Secret", "properties": {"kmsKeyName": {"description": "Cloud KMS key name to use to decrypt these envs.", "type": "string"}, "secretEnv": {"additionalProperties": {"format": "byte", "type": "string"}, "description": "Map of environment variable name to its encrypted value. Secret environment variables must be unique across all of a build's secrets, and must be used by at least one build step. Values can be at most 64 KB in size. There can be at most 100 secret values across all of a build's secrets.", "type": "object"}}, "type": "object"}, "GoogleDevtoolsCloudbuildV1SecretManagerSecret": {"description": "Pairs a secret environment variable with a SecretVersion in Secret Manager.", "id": "GoogleDevtoolsCloudbuildV1SecretManagerSecret", "properties": {"env": {"description": "Environment variable name to associate with the secret. Secret environment variables must be unique across all of a build's secrets, and must be used by at least one build step.", "type": "string"}, "versionName": {"description": "Resource name of the SecretVersion. In format: projects/*/secrets/*/versions/*", "type": "string"}}, "type": "object"}, "GoogleDevtoolsCloudbuildV1Secrets": {"description": "Secrets and secret environment variables.", "id": "GoogleDevtoolsCloudbuildV1Secrets", "properties": {"inline": {"description": "Secrets encrypted with KMS key and the associated secret environment variable.", "items": {"$ref": "GoogleDevtoolsCloudbuildV1InlineSecret"}, "type": "array"}, "secretManager": {"description": "Secrets in Secret Manager and associated secret environment variable.", "items": {"$ref": "GoogleDevtoolsCloudbuildV1SecretManagerSecret"}, "type": "array"}}, "type": "object"}, "GoogleDevtoolsCloudbuildV1Source": {"description": "Location of the source in a supported storage service.", "id": "GoogleDevtoolsCloudbuildV1Source", "properties": {"connectedRepository": {"$ref": "GoogleDevtoolsCloudbuildV1ConnectedRepository", "description": "Optional. If provided, get the source from this 2nd-gen Google Cloud Build repository resource."}, "developerConnectConfig": {"$ref": "GoogleDevtoolsCloudbuildV1DeveloperConnectConfig", "description": "If provided, get the source from this Developer Connect config."}, "gitSource": {"$ref": "GoogleDevtoolsCloudbuildV1GitSource", "description": "If provided, get the source from this Git repository."}, "repoSource": {"$ref": "GoogleDevtoolsCloudbuildV1RepoSource", "description": "If provided, get the source from this location in a Cloud Source Repository."}, "storageSource": {"$ref": "GoogleDevtoolsCloudbuildV1StorageSource", "description": "If provided, get the source from this location in Cloud Storage."}, "storageSourceManifest": {"$ref": "GoogleDevtoolsCloudbuildV1StorageSourceManifest", "description": "If provided, get the source from this manifest in Cloud Storage. This feature is in Preview; see description [here](https://github.com/GoogleCloudPlatform/cloud-builders/tree/master/gcs-fetcher)."}}, "type": "object"}, "GoogleDevtoolsCloudbuildV1SourceProvenance": {"description": "Provenance of the source. Ways to find the original source, or verify that some source was used for this build.", "id": "GoogleDevtoolsCloudbuildV1SourceProvenance", "properties": {"fileHashes": {"additionalProperties": {"$ref": "GoogleDevtoolsCloudbuildV1FileHashes"}, "description": "Output only. Hash(es) of the build source, which can be used to verify that the original source integrity was maintained in the build. Note that `FileHashes` will only be populated if `BuildOptions` has requested a `SourceProvenanceHash`. The keys to this map are file paths used as build source and the values contain the hash values for those files. If the build source came in a single package such as a gzipped tarfile (`.tar.gz`), the `FileHash` will be for the single path to that file.", "readOnly": true, "type": "object"}, "resolvedConnectedRepository": {"$ref": "GoogleDevtoolsCloudbuildV1ConnectedRepository", "description": "Output only. A copy of the build's `source.connected_repository`, if exists, with any revisions resolved.", "readOnly": true}, "resolvedGitSource": {"$ref": "GoogleDevtoolsCloudbuildV1GitSource", "description": "Output only. A copy of the build's `source.git_source`, if exists, with any revisions resolved.", "readOnly": true}, "resolvedRepoSource": {"$ref": "GoogleDevtoolsCloudbuildV1RepoSource", "description": "A copy of the build's `source.repo_source`, if exists, with any revisions resolved."}, "resolvedStorageSource": {"$ref": "GoogleDevtoolsCloudbuildV1StorageSource", "description": "A copy of the build's `source.storage_source`, if exists, with any generations resolved."}, "resolvedStorageSourceManifest": {"$ref": "GoogleDevtoolsCloudbuildV1StorageSourceManifest", "description": "A copy of the build's `source.storage_source_manifest`, if exists, with any revisions resolved. This feature is in Preview."}}, "type": "object"}, "GoogleDevtoolsCloudbuildV1StorageSource": {"description": "Location of the source in an archive file in Cloud Storage.", "id": "GoogleDevtoolsCloudbuildV1StorageSource", "properties": {"bucket": {"description": "Cloud Storage bucket containing the source (see [Bucket Name Requirements](https://cloud.google.com/storage/docs/bucket-naming#requirements)).", "type": "string"}, "generation": {"description": "Optional. Cloud Storage generation for the object. If the generation is omitted, the latest generation will be used.", "format": "int64", "type": "string"}, "object": {"description": "Required. Cloud Storage object containing the source. This object must be a zipped (`.zip`) or gzipped archive file (`.tar.gz`) containing source to build.", "type": "string"}, "sourceFetcher": {"description": "Optional. Option to specify the tool to fetch the source file for the build.", "enum": ["SOURCE_FETCHER_UNSPECIFIED", "GSUTIL", "GCS_FETCHER"], "enumDescriptions": ["Unspecified defaults to GSUTIL.", "Use the \"gsutil\" tool to download the source file.", "Use the Cloud Storage Fetcher tool to download the source file."], "type": "string"}}, "type": "object"}, "GoogleDevtoolsCloudbuildV1StorageSourceManifest": {"description": "Location of the source manifest in Cloud Storage. This feature is in Preview; see description [here](https://github.com/GoogleCloudPlatform/cloud-builders/tree/master/gcs-fetcher).", "id": "GoogleDevtoolsCloudbuildV1StorageSourceManifest", "properties": {"bucket": {"description": "Required. Cloud Storage bucket containing the source manifest (see [Bucket Name Requirements](https://cloud.google.com/storage/docs/bucket-naming#requirements)).", "type": "string"}, "generation": {"description": "Cloud Storage generation for the object. If the generation is omitted, the latest generation will be used.", "format": "int64", "type": "string"}, "object": {"description": "Required. Cloud Storage object containing the source manifest. This object must be a JSON file.", "type": "string"}}, "type": "object"}, "GoogleDevtoolsCloudbuildV1TimeSpan": {"description": "Start and end times for a build execution phase.", "id": "GoogleDevtoolsCloudbuildV1TimeSpan", "properties": {"endTime": {"description": "End of time span.", "format": "google-datetime", "type": "string"}, "startTime": {"description": "Start of time span.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleDevtoolsCloudbuildV1UploadedGoModule": {"description": "A Go module artifact uploaded to Artifact Registry using the GoModule directive.", "id": "GoogleDevtoolsCloudbuildV1UploadedGoModule", "properties": {"fileHashes": {"$ref": "GoogleDevtoolsCloudbuildV1FileHashes", "description": "Hash types and values of the Go Module Artifact."}, "pushTiming": {"$ref": "GoogleDevtoolsCloudbuildV1TimeSpan", "description": "Output only. Stores timing information for pushing the specified artifact.", "readOnly": true}, "uri": {"description": "URI of the uploaded artifact.", "type": "string"}}, "type": "object"}, "GoogleDevtoolsCloudbuildV1UploadedMavenArtifact": {"description": "A Maven artifact uploaded using the MavenArtifact directive.", "id": "GoogleDevtoolsCloudbuildV1UploadedMavenArtifact", "properties": {"fileHashes": {"$ref": "GoogleDevtoolsCloudbuildV1FileHashes", "description": "Hash types and values of the Maven Artifact."}, "pushTiming": {"$ref": "GoogleDevtoolsCloudbuildV1TimeSpan", "description": "Output only. Stores timing information for pushing the specified artifact.", "readOnly": true}, "uri": {"description": "URI of the uploaded artifact.", "type": "string"}}, "type": "object"}, "GoogleDevtoolsCloudbuildV1UploadedNpmPackage": {"description": "An npm package uploaded to Artifact Registry using the NpmPackage directive.", "id": "GoogleDevtoolsCloudbuildV1UploadedNpmPackage", "properties": {"fileHashes": {"$ref": "GoogleDevtoolsCloudbuildV1FileHashes", "description": "Hash types and values of the npm package."}, "pushTiming": {"$ref": "GoogleDevtoolsCloudbuildV1TimeSpan", "description": "Output only. Stores timing information for pushing the specified artifact.", "readOnly": true}, "uri": {"description": "URI of the uploaded npm package.", "type": "string"}}, "type": "object"}, "GoogleDevtoolsCloudbuildV1UploadedPythonPackage": {"description": "Artifact uploaded using the PythonPackage directive.", "id": "GoogleDevtoolsCloudbuildV1UploadedPythonPackage", "properties": {"fileHashes": {"$ref": "GoogleDevtoolsCloudbuildV1FileHashes", "description": "Hash types and values of the Python Artifact."}, "pushTiming": {"$ref": "GoogleDevtoolsCloudbuildV1TimeSpan", "description": "Output only. Stores timing information for pushing the specified artifact.", "readOnly": true}, "uri": {"description": "URI of the uploaded artifact.", "type": "string"}}, "type": "object"}, "GoogleDevtoolsCloudbuildV1Volume": {"description": "Volume describes a Docker container volume which is mounted into build steps in order to persist files across build step execution.", "id": "GoogleDevtoolsCloudbuildV1Volume", "properties": {"name": {"description": "Name of the volume to mount. Volume names must be unique per build step and must be valid names for Docker volumes. Each named volume must be used by at least two build steps.", "type": "string"}, "path": {"description": "Path at which to mount the volume. Paths must be absolute and cannot conflict with other volume paths on the same build step or with certain reserved volume paths.", "type": "string"}}, "type": "object"}, "GoogleDevtoolsCloudbuildV1Warning": {"description": "A non-fatal problem encountered during the execution of the build.", "id": "GoogleDevtoolsCloudbuildV1Warning", "properties": {"priority": {"description": "The priority for this warning.", "enum": ["PRIORITY_UNSPECIFIED", "INFO", "WARNING", "ALERT"], "enumDescriptions": ["Should not be used.", "e.g. deprecation warnings and alternative feature highlights.", "e.g. automated detection of possible issues with the build.", "e.g. alerts that a feature used in the build is pending removal"], "type": "string"}, "text": {"description": "Explanation of the warning generated.", "type": "string"}}, "type": "object"}, "GoogleIamV1AuditConfig": {"description": "Specifies the audit configuration for a service. The configuration determines which permission types are logged, and what identities, if any, are exempted from logging. An AuditConfig must have one or more AuditLogConfigs. If there are AuditConfigs for both `allServices` and a specific service, the union of the two AuditConfigs is used for that service: the log_types specified in each AuditConfig are enabled, and the exempted_members in each AuditLogConfig are exempted. Example Policy with multiple AuditConfigs: { \"audit_configs\": [ { \"service\": \"allServices\", \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\", \"exempted_members\": [ \"user:<EMAIL>\" ] }, { \"log_type\": \"DATA_WRITE\" }, { \"log_type\": \"ADMIN_READ\" } ] }, { \"service\": \"sampleservice.googleapis.com\", \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\" }, { \"log_type\": \"DATA_WRITE\", \"exempted_members\": [ \"user:<EMAIL>\" ] } ] } ] } For sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ logging. It also exempts `<EMAIL>` from DATA_READ logging, and `<EMAIL>` from DATA_WRITE logging.", "id": "GoogleIamV1AuditConfig", "properties": {"auditLogConfigs": {"description": "The configuration for logging of each type of permission.", "items": {"$ref": "GoogleIamV1AuditLogConfig"}, "type": "array"}, "service": {"description": "Specifies a service that will be enabled for audit logging. For example, `storage.googleapis.com`, `cloudsql.googleapis.com`. `allServices` is a special value that covers all services.", "type": "string"}}, "type": "object"}, "GoogleIamV1AuditLogConfig": {"description": "Provides the configuration for logging a type of permissions. Example: { \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\", \"exempted_members\": [ \"user:<EMAIL>\" ] }, { \"log_type\": \"DATA_WRITE\" } ] } This enables 'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from DATA_READ logging.", "id": "GoogleIamV1AuditLogConfig", "properties": {"exemptedMembers": {"description": "Specifies the identities that do not cause logging for this type of permission. Follows the same format of Binding.members.", "items": {"type": "string"}, "type": "array"}, "logType": {"description": "The log type that this config enables.", "enum": ["LOG_TYPE_UNSPECIFIED", "ADMIN_READ", "DATA_WRITE", "DATA_READ"], "enumDescriptions": ["Default case. Should never be this.", "Admin reads. Example: CloudIAM getIamPolicy", "Data writes. Example: CloudSQL Users create", "Data reads. Example: CloudSQL Users list"], "type": "string"}}, "type": "object"}, "GoogleIamV1Binding": {"description": "Associates `members`, or principals, with a `role`.", "id": "GoogleIamV1Binding", "properties": {"condition": {"$ref": "GoogleTypeExpr", "description": "The condition that is associated with this binding. If the condition evaluates to `true`, then this binding applies to the current request. If the condition evaluates to `false`, then this binding does not apply to the current request. However, a different role binding might grant the same role to one or more of the principals in this binding. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies)."}, "members": {"description": "Specifies the principals requesting access for a Google Cloud resource. `members` can have the following values: * `allUsers`: A special identifier that represents anyone who is on the internet; with or without a Google account. * `allAuthenticatedUsers`: A special identifier that represents anyone who is authenticated with a Google account or a service account. Does not include identities that come from external identity providers (IdPs) through identity federation. * `user:{emailid}`: An email address that represents a specific Google account. For example, `<EMAIL>` . * `serviceAccount:{emailid}`: An email address that represents a Google service account. For example, `<EMAIL>`. * `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`: An identifier for a [Kubernetes service account](https://cloud.google.com/kubernetes-engine/docs/how-to/kubernetes-service-accounts). For example, `my-project.svc.id.goog[my-namespace/my-kubernetes-sa]`. * `group:{emailid}`: An email address that represents a Google group. For example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain (primary) that represents all the users of that domain. For example, `google.com` or `example.com`. * `principal://iam.googleapis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: A single identity in a workforce identity pool. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/group/{group_id}`: All workforce identities in a group. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/attribute.{attribute_name}/{attribute_value}`: All workforce identities with a specific attribute value. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/*`: All identities in a workforce identity pool. * `principal://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/subject/{subject_attribute_value}`: A single identity in a workload identity pool. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/group/{group_id}`: A workload identity pool group. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/attribute.{attribute_name}/{attribute_value}`: All identities in a workload identity pool with a certain attribute. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/*`: All identities in a workload identity pool. * `deleted:user:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a user that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the user is recovered, this value reverts to `user:{emailid}` and the recovered user retains the role in the binding. * `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a service account that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the service account is undeleted, this value reverts to `serviceAccount:{emailid}` and the undeleted service account retains the role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a Google group that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the group is recovered, this value reverts to `group:{emailid}` and the recovered group retains the role in the binding. * `deleted:principal://iam.googleapis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: Deleted single identity in a workforce identity pool. For example, `deleted:principal://iam.googleapis.com/locations/global/workforcePools/my-pool-id/subject/my-subject-attribute-value`.", "items": {"type": "string"}, "type": "array"}, "role": {"description": "Role that is assigned to the list of `members`, or principals. For example, `roles/viewer`, `roles/editor`, or `roles/owner`. For an overview of the IAM roles and permissions, see the [IAM documentation](https://cloud.google.com/iam/docs/roles-overview). For a list of the available pre-defined roles, see [here](https://cloud.google.com/iam/docs/understanding-roles).", "type": "string"}}, "type": "object"}, "GoogleIamV1Policy": {"description": "An Identity and Access Management (IAM) policy, which specifies access controls for Google Cloud resources. A `Policy` is a collection of `bindings`. A `binding` binds one or more `members`, or principals, to a single `role`. Principals can be user accounts, service accounts, Google groups, and domains (such as G Suite). A `role` is a named list of permissions; each `role` can be an IAM predefined role or a user-created custom role. For some types of Google Cloud resources, a `binding` can also specify a `condition`, which is a logical expression that allows access to a resource only if the expression evaluates to `true`. A condition can add constraints based on attributes of the request, the resource, or both. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies). **JSON example:** ``` { \"bindings\": [ { \"role\": \"roles/resourcemanager.organizationAdmin\", \"members\": [ \"user:<EMAIL>\", \"group:<EMAIL>\", \"domain:google.com\", \"serviceAccount:<EMAIL>\" ] }, { \"role\": \"roles/resourcemanager.organizationViewer\", \"members\": [ \"user:<EMAIL>\" ], \"condition\": { \"title\": \"expirable access\", \"description\": \"Does not grant access after Sep 2020\", \"expression\": \"request.time < timestamp('2020-10-01T00:00:00.000Z')\", } } ], \"etag\": \"BwWWja0YfJA=\", \"version\": 3 } ``` **YAML example:** ``` bindings: - members: - user:<EMAIL> - group:<EMAIL> - domain:google.com - serviceAccount:<EMAIL> role: roles/resourcemanager.organizationAdmin - members: - user:<EMAIL> role: roles/resourcemanager.organizationViewer condition: title: expirable access description: Does not grant access after Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z') etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features, see the [IAM documentation](https://cloud.google.com/iam/docs/).", "id": "GoogleIamV1Policy", "properties": {"auditConfigs": {"description": "Specifies cloud audit logging configuration for this policy.", "items": {"$ref": "GoogleIamV1AuditConfig"}, "type": "array"}, "bindings": {"description": "Associates a list of `members`, or principals, with a `role`. Optionally, may specify a `condition` that determines how and when the `bindings` are applied. Each of the `bindings` must contain at least one principal. The `bindings` in a `Policy` can refer to up to 1,500 principals; up to 250 of these principals can be Google groups. Each occurrence of a principal counts towards these limits. For example, if the `bindings` grant 50 different roles to `user:<EMAIL>`, and not to any other principal, then you can add another 1,450 principals to the `bindings` in the `Policy`.", "items": {"$ref": "GoogleIamV1Binding"}, "type": "array"}, "etag": {"description": "`etag` is used for optimistic concurrency control as a way to help prevent simultaneous updates of a policy from overwriting each other. It is strongly suggested that systems make use of the `etag` in the read-modify-write cycle to perform policy updates in order to avoid race conditions: An `etag` is returned in the response to `getIamPolicy`, and systems are expected to put that etag in the request to `setIamPolicy` to ensure that their change will be applied to the same version of the policy. **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost.", "format": "byte", "type": "string"}, "version": {"description": "Specifies the format of the policy. Valid values are `0`, `1`, and `3`. Requests that specify an invalid value are rejected. Any operation that affects conditional role bindings must specify version `3`. This requirement applies to the following operations: * Getting a policy that includes a conditional role binding * Adding a conditional role binding to a policy * Changing a conditional role binding in a policy * Removing any role binding, with or without a condition, from a policy that includes conditions **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost. If a policy does not include any conditions, operations on that policy may specify any valid version or leave the field unset. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleIamV1SetIamPolicyRequest": {"description": "Request message for `SetIamPolicy` method.", "id": "GoogleIamV1SetIamPolicyRequest", "properties": {"policy": {"$ref": "GoogleIamV1Policy", "description": "REQUIRED: The complete policy to be applied to the `resource`. The size of the policy is limited to a few 10s of KB. An empty policy is a valid policy but certain Google Cloud services (such as Projects) might reject them."}, "updateMask": {"description": "OPTIONAL: A FieldMask specifying which fields of the policy to modify. Only the fields in the mask will be modified. If no mask is provided, the following default mask is used: `paths: \"bindings, etag\"`", "format": "google-fieldmask", "type": "string"}}, "type": "object"}, "GoogleIamV1TestIamPermissionsRequest": {"description": "Request message for `TestIamPermissions` method.", "id": "GoogleIamV1TestIamPermissionsRequest", "properties": {"permissions": {"description": "The set of permissions to check for the `resource`. Permissions with wildcards (such as `*` or `storage.*`) are not allowed. For more information see [IAM Overview](https://cloud.google.com/iam/docs/overview#permissions).", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleIamV1TestIamPermissionsResponse": {"description": "Response message for `TestIamPermissions` method.", "id": "GoogleIamV1TestIamPermissionsResponse", "properties": {"permissions": {"description": "A subset of `TestPermissionsRequest.permissions` that the caller is allowed.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleLongrunningListOperationsResponse": {"description": "The response message for Operations.ListOperations.", "id": "GoogleLongrunningListOperationsResponse", "properties": {"nextPageToken": {"description": "The standard List next-page token.", "type": "string"}, "operations": {"description": "A list of operations that matches the specified filter in the request.", "items": {"$ref": "GoogleLongrunningOperation"}, "type": "array"}}, "type": "object"}, "GoogleLongrunningOperation": {"description": "This resource represents a long-running operation that is the result of a network API call.", "id": "GoogleLongrunningOperation", "properties": {"done": {"description": "If the value is `false`, it means the operation is still in progress. If `true`, the operation is completed, and either `error` or `response` is available.", "type": "boolean"}, "error": {"$ref": "GoogleRpcStatus", "description": "The error result of the operation in case of failure or cancellation."}, "metadata": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata. Any method that returns a long-running operation should document the metadata type, if any.", "type": "object"}, "name": {"description": "The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the `name` should be a resource name ending with `operations/{unique_id}`.", "type": "string"}, "response": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "The normal, successful response of the operation. If the original method returns no data on success, such as `Delete`, the response is `google.protobuf.Empty`. If the original method is standard `Get`/`Create`/`Update`, the response should be the resource. For other methods, the response should have the type `XxxResponse`, where `Xxx` is the original method name. For example, if the original method name is `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.", "type": "object"}}, "type": "object"}, "GoogleLongrunningWaitOperationRequest": {"description": "The request message for Operations.WaitOperation.", "id": "GoogleLongrunningWaitOperationRequest", "properties": {"timeout": {"description": "The maximum duration to wait before timing out. If left blank, the wait will be at most the time permitted by the underlying HTTP/RPC protocol. If RPC context deadline is also specified, the shorter one will be used.", "format": "google-duration", "type": "string"}}, "type": "object"}, "GoogleProtobufEmpty": {"description": "A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }", "id": "GoogleProtobufEmpty", "properties": {}, "type": "object"}, "GoogleRpcStatus": {"description": "The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).", "id": "GoogleRpcStatus", "properties": {"code": {"description": "The status code, which should be an enum value of google.rpc.Code.", "format": "int32", "type": "integer"}, "details": {"description": "A list of messages that carry the error details. There is a common set of message types for APIs to use.", "items": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "type": "object"}, "type": "array"}, "message": {"description": "A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.", "type": "string"}}, "type": "object"}, "GoogleTypeExpr": {"description": "Represents a textual expression in the Common Expression Language (CEL) syntax. CEL is a C-like expression language. The syntax and semantics of CEL are documented at https://github.com/google/cel-spec. Example (Comparison): title: \"Summary size limit\" description: \"Determines if a summary is less than 100 chars\" expression: \"document.summary.size() < 100\" Example (Equality): title: \"Requestor is owner\" description: \"Determines if requestor is the document owner\" expression: \"document.owner == request.auth.claims.email\" Example (Logic): title: \"Public documents\" description: \"Determine whether the document should be publicly visible\" expression: \"document.type != 'private' && document.type != 'internal'\" Example (Data Manipulation): title: \"Notification string\" description: \"Create a notification string with a timestamp.\" expression: \"'New message received at ' + string(document.create_time)\" The exact variables and functions that may be referenced within an expression are determined by the service that evaluates it. See the service documentation for additional information.", "id": "GoogleTypeExpr", "properties": {"description": {"description": "Optional. Description of the expression. This is a longer text which describes the expression, e.g. when hovered over it in a UI.", "type": "string"}, "expression": {"description": "Textual representation of an expression in Common Expression Language syntax.", "type": "string"}, "location": {"description": "Optional. String indicating the location of the expression for error reporting, e.g. a file name and a position in the file.", "type": "string"}, "title": {"description": "Optional. Title for the expression, i.e. a short string describing its purpose. This can be used e.g. in UIs which allow to enter the expression.", "type": "string"}}, "type": "object"}, "Proto2BridgeMessageSet": {"deprecated": true, "description": "This is proto2's version of MessageSet. DEPRECATED: DO NOT USE FOR NEW FIELDS. If you are using editions or proto2, please make your own extendable messages for your use case. If you are using proto3, please use `Any` instead. MessageSet was the implementation of extensions for proto1. When proto2 was introduced, extensions were implemented as a first-class feature. This schema for MessageSet was meant to be a \"bridge\" solution to migrate MessageSet-bearing messages from proto1 to proto2. This schema has been open-sourced only to facilitate the migration of Google products with MessageSet-bearing messages to open-source environments.", "id": "Proto2BridgeMessageSet", "properties": {}, "type": "object"}, "UtilStatusProto": {"description": "Wire-format for a Status object", "id": "UtilStatusProto", "properties": {"canonicalCode": {"description": "copybara:strip_begin(b/383363683) copybara:strip_end_and_replace optional int32 canonical_code = 6;", "format": "int32", "type": "integer"}, "code": {"description": "Numeric code drawn from the space specified below. Often, this is the canonical error space, and code is drawn from google3/util/task/codes.proto copybara:strip_begin(b/383363683) copybara:strip_end_and_replace optional int32 code = 1;", "format": "int32", "type": "integer"}, "message": {"description": "Detail message copybara:strip_begin(b/383363683) copybara:strip_end_and_replace optional string message = 3;", "type": "string"}, "messageSet": {"$ref": "Proto2BridgeMessageSet", "description": "message_set associates an arbitrary proto message with the status. copybara:strip_begin(b/383363683) copybara:strip_end_and_replace optional proto2.bridge.MessageSet message_set = 5;"}, "space": {"description": "copybara:strip_begin(b/383363683) Space to which this status belongs copybara:strip_end_and_replace optional string space = 2; // Space to which this status belongs", "type": "string"}}, "type": "object"}}, "servicePath": "", "title": "Cloud Run Admin API", "version": "v2", "version_module": true}