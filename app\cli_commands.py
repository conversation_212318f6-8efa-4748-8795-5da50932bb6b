"""
Commandes CLI personnalisées pour l'application
"""
import click
from flask.cli import with_appcontext
from app import db
from app.modules.inventory.models_product import Product
from app.modules.inventory.models_ingredient import Ingredient
from app.modules.auth.models import User


@click.command()
@with_appcontext
def init_average_costs():
    """Initialise les coûts moyens pour tous les produits et ingrédients existants"""
    click.echo("Initialisation des coûts moyens...")
    
    # Récupérer tous les utilisateurs
    users = User.query.all()
    
    total_products_updated = 0
    total_ingredients_updated = 0
    
    for user in users:
        click.echo(f"\nTraitement de l'utilisateur: {user.username}")
        
        # Produits sans recette
        products = Product.query.filter(
            Product.owner_id == user.id,
            Product.has_recipe == False,
            Product.average_cost == 0
        ).all()
        
        products_updated = 0
        for product in products:
            if product.cost_price and product.cost_price > 0:
                product.average_cost = product.cost_price
                products_updated += 1
        
        click.echo(f"  - {products_updated} produits mis à jour")
        total_products_updated += products_updated
        
        # Ingrédients
        ingredients = Ingredient.query.filter(
            Ingredient.owner_id == user.id,
            Ingredient.average_cost == 0
        ).all()
        
        ingredients_updated = 0
        for ingredient in ingredients:
            if ingredient.price_per_unit and ingredient.price_per_unit > 0:
                ingredient.average_cost = ingredient.price_per_unit
                ingredients_updated += 1
        
        click.echo(f"  - {ingredients_updated} ingrédients mis à jour")
        total_ingredients_updated += ingredients_updated
    
    # Sauvegarder les modifications
    try:
        db.session.commit()
        click.echo(f"\n✅ Initialisation terminée avec succès!")
        click.echo(f"Total: {total_products_updated} produits et {total_ingredients_updated} ingrédients mis à jour")
    except Exception as e:
        db.session.rollback()
        click.echo(f"\n❌ Erreur lors de la sauvegarde: {e}")


def init_app(app):
    """Enregistre les commandes CLI avec l'application Flask"""
    app.cli.add_command(init_average_costs)
