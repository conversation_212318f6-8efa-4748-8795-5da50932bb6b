<!doctype html>
<html>
  <head>
    <title>CodeMirror: NTriples mode</title>
    <link rel="stylesheet" href="../../lib/codemirror.css">
    <script src="../../lib/codemirror.js"></script>
    <script src="ntriples.js"></script>
    <link rel="stylesheet" href="../../doc/docs.css">
    <style type="text/css">
      .CodeMirror {
        border: 1px solid #eee;
      }
    </style>   
  </head>
  <body>
    <h1>CodeMirror: NTriples mode</h1>
<form>
<textarea id="ntriples" name="ntriples">    
<http://Sub1>     <http://pred1>     <http://obj> .
<http://Sub2>     <http://pred2#an2> "literal 1" .
<http://Sub3#an3> <http://pred3>     _:bnode3 .
_:bnode4          <http://pred4>     "literal 2"@lang .
_:bnode5          <http://pred5>     "literal 3"^^<http://type> .
</textarea>
</form>

    <script>
      var editor = CodeMirror.fromTextArea(document.getElementById("ntriples"), {});
    </script>
    <p><strong>MIME types defined:</strong> <code>text/n-triples</code>.</p>
  </body>
</html>
