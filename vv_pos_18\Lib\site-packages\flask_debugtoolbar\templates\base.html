<div id="flDebug" style="display:none;" role="navigation">
  <script type="text/javascript">var DEBUG_TOOLBAR_STATIC_PATH = '{{ static_path }}'</script>
  <script type="text/javascript" src="{{ static_path }}js/jquery.js"></script>
  <!-- Temporarily adding jquery-migrate during the Jquery upgrade process, this can be removed post-upgrade -->
  <script src="{{ static_path }}js/jquery-migrate.js"></script>
  <script type="text/javascript" src="{{ static_path }}js/jquery.tablesorter.js"></script>
  <script type="text/javascript" src="{{ static_path }}js/toolbar.js"></script>

  <div style="display: none;" id="flDebugToolbar">
    <ol id="flDebugPanelList">
      {% if panels %}
      <li><a id="flDebugHideToolBarButton" href="#" title="Hide Toolbar">Hide &raquo;</a></li>
      {% else %}
      <li id="flDebugButton">DEBUG</li>
      {% endif %}

      {% for panel in panels %}
      <li id="{{ panel.dom_id() }}">
        {% if panel.has_content %}
        <a href="{{ panel.url()|default("#") }}" title="{{ panel.title() }}" class="{{ panel.dom_id() }}">
        {% else %}
        <div class="flDebugContentless">
        {% endif %}

        {{ panel.nav_title() }}
        {% if panel.nav_subtitle() %}<br /><small>{{ panel.nav_subtitle() }}</small>{% endif %}

        {% if panel.has_content %}
        </a>
        {% else %}
        </div>
        {% endif %}
        {% if panel.user_activate %}
        <span class="flDebugSwitch {{ 'flDebugActive' if panel.is_active else 'flDebugInactive' }}" title="Enable or disable the panel"></span>
        {% endif %}
      </li>
      {% endfor %}
    </ol>
  </div>
  <div style="display:none;" id="flDebugToolbarHandle">
    <a title="Show Toolbar" id="flDebugShowToolBarButton" href="#">&laquo;</a>
  </div>
  {% for panel in panels %}
    {% if panel.has_content %}
    <div id="{{ panel.dom_id() }}-content" class="flDebugPanelContentParent">
      <div class="flDebugPanelTitle">
        <a href="" class="flDebugClose">Close</a>
        <h3>{{ panel.title()|safe }}</h3>
      </div>
      <div class="flDebugPanelContent">
        <div class="flDebugScroll">
          {{ panel.content()|safe }}
        </div>
      </div>
    </div>
    {% endif %}
  {% endfor %}
  <div id="flDebugWindow" class="flDebugPanelContentParent"></div>
</div>
