#!/usr/bin/env python3
"""
Script pour initialiser les coûts moyens des produits et ingrédients existants
"""
import sys
import os

# Ajouter le répertoire parent au path pour pouvoir importer l'app
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import create_app, db
from app.modules.inventory.models_product import Product
from app.modules.inventory.models_ingredient import Ingredient
from app.modules.auth.models import User

def initialize_average_costs():
    """Initialise les coûts moyens pour tous les utilisateurs"""
    app = create_app()
    
    with app.app_context():
        print("Initialisation des coûts moyens...")
        
        # Récupérer tous les utilisateurs
        users = User.query.all()
        
        for user in users:
            print(f"\nTraitement de l'utilisateur: {user.username}")
            
            # Produits sans recette
            products = Product.query.filter(
                Product.owner_id == user.id,
                Product.has_recipe == False,
                Product.average_cost == 0
            ).all()
            
            products_updated = 0
            for product in products:
                if product.cost_price and product.cost_price > 0:
                    product.average_cost = product.cost_price
                    products_updated += 1
            
            print(f"  - {products_updated} produits mis à jour")
            
            # Ingrédients
            ingredients = Ingredient.query.filter(
                Ingredient.owner_id == user.id,
                Ingredient.average_cost == 0
            ).all()
            
            ingredients_updated = 0
            for ingredient in ingredients:
                if ingredient.price_per_unit and ingredient.price_per_unit > 0:
                    ingredient.average_cost = ingredient.price_per_unit
                    ingredients_updated += 1
            
            print(f"  - {ingredients_updated} ingrédients mis à jour")
        
        # Sauvegarder les modifications
        try:
            db.session.commit()
            print("\n✅ Initialisation terminée avec succès!")
        except Exception as e:
            db.session.rollback()
            print(f"\n❌ Erreur lors de la sauvegarde: {e}")

if __name__ == "__main__":
    initialize_average_costs()
