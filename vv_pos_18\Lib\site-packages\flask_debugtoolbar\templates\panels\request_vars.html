
<h4>View information</h4>
<table>
  <thead>
    <tr>
      <th>View Function</th>
      <th>kwargs</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>{{ view_func }}</td>
      <td>
        {% if view_kwargs.items() %}
          {% for k, v in view_kwargs.items() %}
            {{ k }}={{ v }}{% if not loop.last %}, {% endif %}
          {% endfor %}
        {% else %}
          None
        {% endif %}
      </td>
    </tr>
  </tbody>
</table>

{% macro show_map(map) %}
<table>
  <colgroup>
    <col style="width:20%"/>
    <col/>
  </colgroup>
  <thead>
    <tr>
      <th>Variable</th>
      <th>Value</th>
    </tr>
  </thead>
  <tbody>
    {% for key, value in map %}
    <tr class="{{ loop.cycle('flDebugOdd', 'flDebugEven') }}">
      <td>{{ key|printable }}</td>
      <td>{{ value|printable }}</td>
    </tr>
    {% endfor %}
  </tbody>
</table>
{% endmacro %}


<h4>COOKIES Variables</h4>
{% if cookies %}
  {{ show_map(cookies) }}
{% else %}
  <p>No COOKIE data</p>
{% endif %}

<h4>SESSION Variables</h4>
{% if session %}
  {{ show_map(session) }}
{% else %}
  <p>No SESSION data</p>
{% endif %}


{% macro show_multi_map(map) %}
<table>
  <thead>
    <tr>
      <th>Variable</th>
      <th>Value</th>
    </tr>
  </thead>
  <tbody>
    {% for key, value in map %}
    <tr class="{{ loop.cycle('flDebugOdd', 'flDebugEven') }}">
      <td>{{ key|printable }}</td>
      <td>
        {%- set sep = joiner() -%}
        {%- for v in value -%}
          {{ sep() }}{{ v|printable }}
        {%- endfor -%}
      </td>
    </tr>
    {% endfor %}
  </tbody>
</table>
{% endmacro %}


<h4>GET Variables</h4>
{% if get %}
  {{ show_multi_map(get) }}
{% else %}
  <p>No GET data</p>
{% endif %}

<h4>POST Variables</h4>
{% if post %}
  {{ show_multi_map(post) }}
{% else %}
  <p>No POST data</p>
{% endif %}
