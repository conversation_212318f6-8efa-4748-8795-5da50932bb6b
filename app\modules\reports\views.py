from datetime import datetime
from io import StringIO, BytesIO
import csv
import math
from flask import send_file, jsonify, current_app
from app.modules.inventory.models_stock_movement import StockMovement, StockMovementType, StockMovementReason
from app.modules.inventory.models_product import ProductCategory
from app.modules.inventory.models_ingredient import IngredientCategory
from flask import render_template, request, jsonify, send_file, flash, redirect, url_for
from flask_login import login_required, current_user
from datetime import datetime, timedelta
from app import db
from app.modules.pos.models_sale import Sale, SaleStatus, SaleItem
from app.modules.inventory.models_product import Product
from app.modules.inventory.models_ingredient import Ingredient
from app.modules.expenses.models_expense import Expense, ExpenseCategory
from app.modules.settings.models_settings import Settings
from app.modules.inventory.models_supplier import Supplier
from app.modules.auth.models import UserRole
from app.modules.online_ordering_sites.models import OnlineOrder, OnlineOrderStatus, PaymentStatus as OnlinePaymentStatus, OnlineOrderItem
from app.modules.tables.models_table import Table, Room
from app.utils import get_date_range, format_currency
from sqlalchemy import func, cast, Date, extract, and_

from . import bp

# Formulaires
from flask_wtf import FlaskForm
from wtforms import SelectField, IntegerField, TextAreaField, HiddenField
from wtforms.validators import DataRequired

class StockAdjustmentForm(FlaskForm):
    movement_type = SelectField('Type', choices=[('IN', 'Entrée'), ('OUT', 'Sortie')], validators=[DataRequired()])
    quantity = IntegerField('Quantité', validators=[DataRequired()])
    note = TextAreaField('Note')
    item_id = HiddenField('Item ID')
    item_type = HiddenField('Item Type')

@bp.route('/')
@login_required
def index():
    """Page principale des rapports"""
    return render_template('reports/index.html',
                         title='Rapports')

@bp.route('/inventory')
@login_required
def inventory():
    """Rapport d'inventaire"""
    # Create the form
    form = StockAdjustmentForm()

    # Get period from query parameters
    period = request.args.get('period', 'month')
    
    # Set date range based on period
    end_date = datetime.now()
    if period == 'today':
        start_date = end_date.replace(hour=0, minute=0, second=0, microsecond=0)
    elif period == 'week':
        start_date = end_date - timedelta(days=7)
    elif period == 'month':
        start_date = end_date - timedelta(days=30)
    elif period == 'year':
        start_date = end_date - timedelta(days=365)
    elif period == 'custom':
        try:
            start_date = datetime.strptime(request.args.get('start_date'), '%Y-%m-%d')
            end_date = datetime.strptime(request.args.get('end_date'), '%Y-%m-%d')
            end_date = end_date.replace(hour=23, minute=59, second=59, microsecond=999999)
        except (TypeError, ValueError):
            start_date = end_date - timedelta(days=30)  # Default to month if invalid dates
    else:
        start_date = end_date - timedelta(days=30)  # Default to month
    
    # Get filter parameters
    item_types = request.args.getlist('item_types')
    if not item_types:  # Si aucun filtre sélectionné, afficher tout
        item_types = ['products_no_recipe', 'products_with_recipe', 'ingredients']

    category_filter = request.args.get('category', '')
    stock_status_filter = request.args.get('stock_status', '')
    search_filter = request.args.get('search', '').strip()

    # Parse category filter to determine type and ID
    product_category_id = None
    ingredient_category_id = None
    if category_filter:
        if category_filter.startswith('product_'):
            try:
                product_category_id = int(category_filter.replace('product_', ''))
            except ValueError:
                pass
        elif category_filter.startswith('ingredient_'):
            try:
                ingredient_category_id = int(category_filter.replace('ingredient_', ''))
            except ValueError:
                pass

    # Get products and ingredients based on selected filters
    products = []
    ingredients = []

    if 'products_no_recipe' in item_types:
        query = Product.query.filter_by(owner_id=current_user.id, has_recipe=False)
        if product_category_id:
            query = query.filter_by(category_id=product_category_id)
        if search_filter:
            query = query.filter(Product.name.ilike(f'%{search_filter}%'))
        products.extend(query.all())

    if 'products_with_recipe' in item_types:
        query = Product.query.filter_by(owner_id=current_user.id, has_recipe=True)
        if product_category_id:
            query = query.filter_by(category_id=product_category_id)
        if search_filter:
            query = query.filter(Product.name.ilike(f'%{search_filter}%'))
        products.extend(query.all())

    if 'ingredients' in item_types:
        query = Ingredient.query.filter_by(owner_id=current_user.id)
        if ingredient_category_id:
            query = query.filter_by(category_id=ingredient_category_id)
        if search_filter:
            query = query.filter(Ingredient.name.ilike(f'%{search_filter}%'))
        ingredients = query.all()
    
    # Calculate stock values
    product_stock_value = sum(p.stock_quantity * p.price for p in products if not p.has_recipe)  # Only include products without recipe
    ingredient_stock_value = sum(i.stock_quantity * i.price_per_unit for i in ingredients)
    total_stock_value = product_stock_value + ingredient_stock_value
    
    # Initialize counters
    max_movements = 0
    max_turnover = 0
    total_movements = 0
    low_stock_count = 0
    out_of_stock_count = 0
    
    # Get recent movements for history with pagination
    history_page = request.args.get('history_page', 1, type=int)
    history_per_page = 10

    movement_history_query = StockMovement.query.filter(
        StockMovement.owner_id == current_user.id,
        StockMovement.created_at.between(start_date, end_date)
    ).order_by(StockMovement.created_at.desc())

    movement_history_pagination = movement_history_query.paginate(
        page=history_page, per_page=history_per_page, error_out=False
    )

    movement_history = movement_history_pagination.items
    
    # Combine products and ingredients into a single list of inventory items
    inventory_items = []
    active_items = []
    
    for product in products:
        # Calculate movements count for products
        movements = StockMovement.query.filter(
            StockMovement.product_id == product.id,
            StockMovement.created_at.between(start_date, end_date)
        ).count()
        max_movements = max(max_movements, movements)
        total_movements += movements
        
        # Get available quantity
        available_quantity = product.get_available_quantity()
        
        # Check stock status
        if available_quantity <= product.minimum_stock:
            low_stock_count += 1
        if available_quantity == 0:
            out_of_stock_count += 1
        
        try:
            category_name = product.category.name if product.category else "Sans catégorie"
        except AttributeError:
            category_name = "Sans catégorie"

        item_data = {
            'type': 'product',
            'id': product.id,
            'name': product.name,
            'category': category_name,
            'stock_quantity': available_quantity,
            'minimum_stock': product.minimum_stock,
            'unit_price': float(product.price),
            'total_value': float(available_quantity * product.price),
            'has_recipe': product.has_recipe
        }
        inventory_items.append(item_data)
        
        # Add product to active items if it has movements
        active_items.append({
            'name': product.name,
            'movements_count': movements,
            'type': 'product'
        })
    
    for ingredient in ingredients:
        # Calculate movements count for ingredients
        movements = StockMovement.query.filter(
            StockMovement.ingredient_id == ingredient.id,
            StockMovement.created_at.between(start_date, end_date)
        ).count()
        max_movements = max(max_movements, movements)
        total_movements += movements
        
        # Check stock status
        if ingredient.stock_quantity <= ingredient.minimum_stock:
            low_stock_count += 1
        if ingredient.stock_quantity == 0:
            out_of_stock_count += 1
        
        try:
            category_name = ingredient.category.name if ingredient.category else "Sans catégorie"
        except AttributeError:
            category_name = "Sans catégorie"

        item_data = {
            'type': 'ingredient',
            'id': ingredient.id,
            'name': ingredient.name,
            'category': category_name,
            'stock_quantity': ingredient.stock_quantity,
            'minimum_stock': ingredient.minimum_stock,
            'unit_price': float(ingredient.price_per_unit),
            'total_value': float(ingredient.stock_quantity * ingredient.price_per_unit),
            'has_recipe': False  # Les ingrédients n'ont jamais de recette
        }
        inventory_items.append(item_data)
        
        # Add ingredient to active items if it has movements
        active_items.append({
            'name': ingredient.name,
            'movements_count': movements,
            'type': 'ingredient'
        })
    
    # Filter out items with no movements and sort by movement count
    active_items = [item for item in active_items if item['movements_count'] > 0]
    active_items.sort(key=lambda x: x['movements_count'], reverse=True)
    active_items = active_items[:5]  # Keep only top 5

    # Apply stock status filter
    stock_status_filter = request.args.get('stock_status', '')
    if stock_status_filter:
        filtered_items = []
        for item in inventory_items:
            if stock_status_filter == 'low' and item['stock_quantity'] <= item['minimum_stock']:
                filtered_items.append(item)
            elif stock_status_filter == 'out' and item['stock_quantity'] == 0:
                filtered_items.append(item)
            elif stock_status_filter == 'normal' and item['stock_quantity'] > item['minimum_stock']:
                filtered_items.append(item)
        inventory_items = filtered_items

    # Prepare category data for charts
    category_data = {}
    for item in inventory_items:
        cat = item['category']
        if cat not in category_data:
            category_data[cat] = 0
        category_data[cat] += item['total_value']
    
    # Create pagination
    page = request.args.get('page', 1, type=int)
    per_page = 10
    total_items = len(inventory_items)
    inventory_items = inventory_items[(page-1)*per_page:page*per_page]
    
    class Pagination:
        def __init__(self, items, page, per_page, total):
            self.items = items
            self.page = page
            self.per_page = per_page
            self.total = total
            self.pages = (total + per_page - 1) // per_page

        @property
        def has_prev(self):
            return self.page > 1

        @property
        def has_next(self):
            return self.page < self.pages

        @property
        def prev_num(self):
            return self.page - 1 if self.has_prev else None

        @property
        def next_num(self):
            return self.page + 1 if self.has_next else None

        def iter_pages(self, left_edge=2, left_current=2, right_current=3, right_edge=2):
            """Iterate over page numbers for pagination display"""
            last = self.pages
            for num in range(1, last + 1):
                if num <= left_edge or \
                   (self.page - left_current - 1 < num < self.page + right_current) or \
                   num > last - right_edge:
                    yield num
    
    pagination = Pagination(inventory_items, page, per_page, total_items)

    # Prepare daily stock value data
    daily_stock_values = {}
    current_date = start_date
    
    # Get all stock movements for the period
    movements = StockMovement.query.filter(
        StockMovement.owner_id == current_user.id,
        StockMovement.created_at.between(start_date, end_date)
    ).order_by(StockMovement.created_at.asc()).all()

    # Initialize stock values with current quantities and prices
    product_stocks = {p.id: {'quantity': p.stock_quantity, 'price': p.price} for p in products}
    ingredient_stocks = {i.id: {'quantity': i.stock_quantity, 'price': i.price_per_unit} for i in ingredients}
    
    # Calculate initial total value
    total_value = sum(data['quantity'] * data['price'] for data in product_stocks.values()) + \
                 sum(data['quantity'] * data['price'] for data in ingredient_stocks.values())
    
    # Store initial value
    daily_stock_values[start_date.strftime('%Y-%m-%d')] = round(float(total_value), 2)
    
    # Process each movement chronologically
    for movement in movements:
        date_str = movement.created_at.strftime('%Y-%m-%d')
        
        # Update stock quantities based on movement (only for filtered items)
        if movement.product_id and movement.product_id in product_stocks:
            if movement.type == StockMovementType.IN:
                product_stocks[movement.product_id]['quantity'] += movement.quantity
            else:
                product_stocks[movement.product_id]['quantity'] -= movement.quantity
        elif movement.ingredient_id and movement.ingredient_id in ingredient_stocks:
            if movement.type == StockMovementType.IN:
                ingredient_stocks[movement.ingredient_id]['quantity'] += movement.quantity
            else:
                ingredient_stocks[movement.ingredient_id]['quantity'] -= movement.quantity
        
        # Calculate new total value
        total_value = sum(data['quantity'] * data['price'] for data in product_stocks.values()) + \
                     sum(data['quantity'] * data['price'] for data in ingredient_stocks.values())
        
        # Update daily value
        daily_stock_values[date_str] = round(float(total_value), 2)
    
    # Fill in missing dates with the last known value
    all_dates = []
    current_date = start_date
    last_value = daily_stock_values.get(start_date.strftime('%Y-%m-%d'), 0)
    
    while current_date <= end_date:
        date_str = current_date.strftime('%Y-%m-%d')
        if date_str not in daily_stock_values:
            daily_stock_values[date_str] = last_value
        else:
            last_value = daily_stock_values[date_str]
        all_dates.append(date_str)
        current_date += timedelta(days=1)
    
    # Sort dates and get corresponding values
    dates = sorted(all_dates)
    stock_values = [daily_stock_values[date] for date in dates]
    
    # Get categories separated by type for better filtering
    product_categories = ProductCategory.query.filter_by(owner_id=current_user.id).order_by(ProductCategory.name).all()
    ingredient_categories = IngredientCategory.query.filter_by(owner_id=current_user.id).order_by(IngredientCategory.name).all()

    return render_template('reports/inventory.html',
                         title='Rapport d\'inventaire',
                         items=pagination,
                         product_categories=product_categories,
                         ingredient_categories=ingredient_categories,
                         StockMovementType=StockMovementType,
                         total_stock_value=total_stock_value,
                         max_movements=max_movements,
                         period=period,
                         total_movements=total_movements,
                         days_count=(end_date - start_date).days + 1,
                         start_date=start_date,
                         end_date=end_date,
                         low_stock_count=low_stock_count,
                         out_of_stock_count=out_of_stock_count,
                         categories=sorted(list(category_data.keys())),
                         category_labels=list(category_data.keys()),
                         category_data=list(category_data.values()),
                         category_colors=[
                             'rgba(78, 115, 223, 0.8)',
                             'rgba(54, 185, 204, 0.8)',
                             'rgba(246, 194, 62, 0.8)',
                             'rgba(231, 74, 59, 0.8)',
                             'rgba(28, 200, 138, 0.8)',
                             'rgba(133, 135, 150, 0.8)',
                             'rgba(105, 0, 132, 0.8)',
                             'rgba(255, 159, 64, 0.8)',
                         ],
                         items_count=total_items,
                         active_items=active_items,
                         movement_history=movement_history,
                         movement_history_pagination=movement_history_pagination,
                         dates=dates,
                         stock_values=stock_values,
                         form=form)

@bp.route('/inventory/quick-stock-update', methods=['POST'])
@login_required
def quick_stock_update():
    """Mise à jour rapide du stock"""
    form = StockAdjustmentForm()

    if form.validate_on_submit():
        item_id = form.item_id.data or request.form.get('item_id')
        item_type = form.item_type.data or request.form.get('item_type')  # 'product' ou 'ingredient'
        movement_type = form.movement_type.data  # 'IN' ou 'OUT'
        quantity = form.quantity.data
        note = form.note.data

        try:
            if quantity <= 0:
                raise ValueError("La quantité doit être positive")

            # Find the item (product or ingredient)
            item = None

            if item_type == 'ingredient':
                item = Ingredient.query.filter_by(id=item_id, owner_id=current_user.id).first()
            else:  # product
                item = Product.query.filter_by(id=item_id, owner_id=current_user.id).first()

            if not item:
                flash('Article non trouvé.', 'error')
                return redirect(url_for('reports.inventory'))

            # Empêcher la modification des produits avec recette
            if item_type == 'product' and hasattr(item, 'has_recipe') and item.has_recipe:
                flash('Impossible de modifier le stock d\'un produit avec recette. Le stock est calculé automatiquement selon la fiche technique.', 'error')
                return redirect(url_for('reports.inventory'))

            # Store previous quantity before modification
            previous_quantity = item.stock_quantity

            # Set the appropriate item reference and update stock
            if item_type == 'ingredient':
                # Update ingredient stock
                if movement_type == 'IN':
                    item.stock_quantity += quantity
                else:  # OUT
                    if item.stock_quantity >= quantity:
                        item.stock_quantity -= quantity
                    else:
                        flash('Stock insuffisant pour cette sortie.', 'error')
                        if request.form.get('item_type') == 'ingredient':
                            return redirect(url_for('inventory.ingredients'))
                        else:
                            return redirect(url_for('reports.inventory'))
            else:  # product
                # Update product stock
                if movement_type == 'IN':
                    item.stock_quantity += quantity
                else:  # OUT
                    if item.stock_quantity >= quantity:
                        item.stock_quantity -= quantity
                    else:
                        flash('Stock insuffisant pour cette sortie.', 'error')
                        if request.form.get('item_type') == 'ingredient':
                            return redirect(url_for('inventory.ingredients'))
                        else:
                            return redirect(url_for('reports.inventory'))

            # Create stock movement record with all required fields
            movement = StockMovement(
                owner_id=current_user.id,
                user_id=current_user.id,
                type=StockMovementType.IN if movement_type == 'IN' else StockMovementType.OUT,
                reason=StockMovementReason.ADJUSTMENT,
                quantity=quantity,
                previous_quantity=previous_quantity,
                new_quantity=item.stock_quantity,
                notes=note
            )

            # Set the appropriate item reference
            if item_type == 'ingredient':
                movement.ingredient_id = item.id
            else:  # product
                movement.product_id = item.id

            # Save to database
            db.session.add(movement)
            db.session.commit()

            action_text = "ajouté au" if movement_type == 'IN' else "retiré du"
            flash(f'{quantity} unité(s) {action_text} stock de {item.name}.', 'success')

        except ValueError as e:
            flash(str(e), 'error')
        except Exception as e:
            db.session.rollback()
            flash('Une erreur est survenue lors de la mise à jour du stock.', 'error')
            print(f"Debug - Error updating stock: {str(e)}")  # Debug log
    else:
        flash('Données du formulaire invalides.', 'error')

    # Rediriger vers la page appropriée selon le type d'article
    if request.form.get('item_type') == 'ingredient':
        return redirect(url_for('inventory.ingredients'))
    else:
        return redirect(url_for('reports.inventory'))

@bp.route('/inventory/export')
@login_required
def export_inventory():
    """Export the inventory report as CSV or PDF"""
    format_type = request.args.get('format', 'csv')

    # Get all products and ingredients
    products = Product.query.filter_by(owner_id=current_user.id).all()
    ingredients = Ingredient.query.filter_by(owner_id=current_user.id).all()

    if format_type == 'pdf':
        # Import PDF libraries
        from reportlab.lib.pagesizes import letter, A4
        from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
        from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
        from reportlab.lib import colors
        from reportlab.lib.units import inch

        # Create PDF
        buffer = BytesIO()
        doc = SimpleDocTemplate(buffer, pagesize=A4)
        styles = getSampleStyleSheet()
        story = []

        # Title
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=18,
            spaceAfter=30,
            alignment=1  # Center alignment
        )
        story.append(Paragraph("Rapport d'Inventaire", title_style))
        story.append(Spacer(1, 20))

        # Prepare data for table
        data = [['Type', 'Nom', 'Catégorie', 'Stock actuel', 'Stock minimum', 'Prix unitaire', 'Valeur totale']]

        # Add product data
        for product in products:
            data.append([
                'Produit',
                product.name,
                product.category.name if product.category else "Sans catégorie",
                str(product.stock_quantity),
                str(product.minimum_stock),
                f"{float(product.price):.2f}€",
                f"{float(product.stock_quantity * product.price):.2f}€"
            ])

        # Add ingredient data
        for ingredient in ingredients:
            data.append([
                'Ingrédient',
                ingredient.name,
                ingredient.category.name if ingredient.category else "Sans catégorie",
                str(ingredient.stock_quantity),
                str(ingredient.minimum_stock),
                f"{float(ingredient.price_per_unit):.2f}€",
                f"{float(ingredient.stock_quantity * ingredient.price_per_unit):.2f}€"
            ])

        # Create table
        table = Table(data)
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 10),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))

        story.append(table)
        doc.build(story)

        buffer.seek(0)
        return send_file(
            buffer,
            mimetype='application/pdf',
            as_attachment=True,
            download_name=f'inventory_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.pdf'
        )

    else:
        # CSV Export
        output = StringIO()
        headers = ['Type', 'Nom', 'Catégorie', 'Stock actuel', 'Stock minimum', 'Prix unitaire', 'Valeur totale']

        import csv
        writer = csv.writer(output)
        writer.writerow(headers)

        # Add products
        for product in products:
            writer.writerow([
                'Produit',
                product.name,
                product.category.name if product.category else "Sans catégorie",
                product.stock_quantity,
                product.minimum_stock,
                float(product.price),
                float(product.stock_quantity * product.price)
            ])

        # Add ingredients
        for ingredient in ingredients:
            writer.writerow([
                'Ingrédient',
                ingredient.name,
                ingredient.category.name if ingredient.category else "Sans catégorie",
                ingredient.stock_quantity,
                ingredient.minimum_stock,
                float(ingredient.price_per_unit),
                float(ingredient.stock_quantity * ingredient.price_per_unit)
            ])

        # Prepare the response
        output.seek(0)
        return send_file(
            BytesIO(output.getvalue().encode('utf-8')),
            mimetype='text/csv',
            as_attachment=True,
            download_name=f'inventory_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv'
        )

@bp.route('/inventory/history/<int:id>')
@login_required
def get_history(id):
    """Get movement history for an item with pagination"""
    # Get pagination parameters
    page = request.args.get('page', 1, type=int)
    per_page = 10

    # Get the item (try product first, then ingredient)
    item = Product.query.filter_by(id=id, owner_id=current_user.id).first()
    item_type = 'product'

    if not item:
        item = Ingredient.query.filter_by(id=id, owner_id=current_user.id).first()
        item_type = 'ingredient'
        if not item:
            return jsonify({'error': 'Item not found'}), 404

    # Get movements for the last 30 days
    end_date = datetime.now()
    start_date = end_date - timedelta(days=30)

    movements_query = StockMovement.query.filter(
        StockMovement.owner_id == current_user.id,
        StockMovement.created_at.between(start_date, end_date)
    ).order_by(StockMovement.created_at.desc())

    if item_type == 'product':
        movements_query = movements_query.filter_by(product_id=id)
    else:
        movements_query = movements_query.filter_by(ingredient_id=id)

    # Paginate the movements
    movements_pagination = movements_query.paginate(
        page=page, per_page=per_page, error_out=False
    )

    # Render the history template
    return render_template('reports/_history.html',
                         item=item,
                         movements=movements_pagination.items,
                         pagination=movements_pagination,
                         item_type=item_type,
                         item_id=id)


@bp.route('/expenses')
@login_required
def expenses():
    """Rapport des dépenses"""
    # Get period from query parameters
    period = request.args.get('period', 'month')
    
    # Set date range based on period
    end_date = datetime.now()
    if period == 'today':
        start_date = end_date.replace(hour=0, minute=0, second=0, microsecond=0)
    elif period == 'week':
        start_date = end_date - timedelta(days=7)
    elif period == 'month':
        start_date = end_date - timedelta(days=30)
    elif period == 'year':
        start_date = end_date - timedelta(days=365)
    elif period == 'custom':
        try:
            start_date = datetime.strptime(request.args.get('start_date'), '%Y-%m-%d')
            end_date = datetime.strptime(request.args.get('end_date'), '%Y-%m-%d')
            end_date = end_date.replace(hour=23, minute=59, second=59, microsecond=999999)
        except (TypeError, ValueError):
            start_date = end_date - timedelta(days=30)  # Default to month if invalid dates
    else:
        start_date = end_date - timedelta(days=30)  # Default to month
    
    # Get category filter
    category_id = request.args.get('category', type=int)
    
    # Query expenses
    query = Expense.query.filter(
        Expense.owner_id == current_user.id,
        Expense.date.between(start_date, end_date)
    )
    
    if category_id:
        query = query.filter_by(category_id=category_id)
    
    expenses = query.order_by(Expense.date.desc()).all()
    
    # Calculate total amount
    total_amount = sum(expense.amount for expense in expenses)
    
    # Get all categories for filter
    categories = ExpenseCategory.query.filter_by(owner_id=current_user.id).all()
    
    # Prepare daily expense data
    daily_expenses = {}
    current_date = start_date
    
    while current_date <= end_date:
        date_str = current_date.strftime('%Y-%m-%d')
        day_expenses = [e for e in expenses if e.date == current_date.date()]
        daily_expenses[date_str] = round(sum(e.amount for e in day_expenses), 2)
        current_date += timedelta(days=1)
    
    # Ne plus ajouter de données fictives
    # if all(value == 0 for value in daily_expenses.values()):
    #     current_app.logger.info("Toutes les valeurs de dépenses sont à zéro, ajout de données de test pour le graphique")
    #     # Ajouter des données fictives pour tester le graphique
    #     for i, date in enumerate(daily_expenses.keys()):
    #         # Créer une courbe cosinus pour visualiser clairement le graphique
    #         daily_expenses[date] = 75 + 50 * math.cos(i * 0.3)
    
    dates = list(daily_expenses.keys())
    expense_values = list(daily_expenses.values())
    
    # Prepare category data for pie chart
    category_data = {}
    for expense in expenses:
        cat_name = expense.category.name if expense.category else "Sans catégorie"
        if cat_name not in category_data:
            category_data[cat_name] = 0
        category_data[cat_name] += float(expense.amount)
    
    category_labels = list(category_data.keys())
    category_values = list(category_data.values())
    
    # Generate colors for categories
    category_colors = [
        'rgba(78, 115, 223, 0.8)',
        'rgba(54, 185, 204, 0.8)',
        'rgba(246, 194, 62, 0.8)',
        'rgba(231, 74, 59, 0.8)',
        'rgba(28, 200, 138, 0.8)',
        'rgba(133, 135, 150, 0.8)',
        'rgba(105, 0, 132, 0.8)',
        'rgba(255, 159, 64, 0.8)',
    ]
    
    # Extend colors if needed
    while len(category_colors) < len(category_labels):
        category_colors.extend(category_colors)
    category_colors = category_colors[:len(category_labels)]
    
    return render_template('reports/expenses.html',
                         title='Rapport des dépenses',
                         expenses=expenses,
                         total_amount=total_amount,
                         categories=categories,
                         period=period,
                         start_date=start_date,
                         end_date=end_date,
                         dates=dates,
                         expense_values=expense_values,
                         category_labels=category_labels,
                         category_data=category_values,
                         category_colors=category_colors) 

@bp.route('/expenses/export')
@login_required
def export_expenses():
    """Export des dépenses"""
    # Get period from query parameters
    period = request.args.get('period', 'month')
    
    # Set date range based on period
    end_date = datetime.now()
    if period == 'today':
        start_date = end_date.replace(hour=0, minute=0, second=0, microsecond=0)
    elif period == 'week':
        start_date = end_date - timedelta(days=7)
    elif period == 'month':
        start_date = end_date - timedelta(days=30)
    elif period == 'year':
        start_date = end_date - timedelta(days=365)
    elif period == 'custom':
        try:
            start_date = datetime.strptime(request.args.get('start_date'), '%Y-%m-%d')
            end_date = datetime.strptime(request.args.get('end_date'), '%Y-%m-%d')
            end_date = end_date.replace(hour=23, minute=59, second=59, microsecond=999999)
        except (TypeError, ValueError):
            start_date = end_date - timedelta(days=30)  # Default to month if invalid dates
    else:
        start_date = end_date - timedelta(days=30)  # Default to month
    
    # Get category filter
    category_id = request.args.get('category', type=int)
    
    # Query expenses
    query = Expense.query.filter(
        Expense.owner_id == current_user.id,
        Expense.date.between(start_date, end_date)
    )
    
    if category_id:
        query = query.filter_by(category_id=category_id)
    
    expenses = query.order_by(Expense.date.desc()).all()
    
    # Create CSV content
    output = StringIO()
    writer = csv.writer(output)
    
    # Write headers
    writer.writerow(['Date', 'Description', 'Catégorie', 'Montant', 'Mode de paiement', 'Référence', 'Notes'])
    
    # Write data
    for expense in expenses:
        writer.writerow([
            expense.date.strftime('%d/%m/%Y'),
            expense.description,
            expense.category.name if expense.category else "Sans catégorie",
            f"{expense.amount:.2f}",
            expense.payment_method,
            expense.reference or "",
            expense.notes or ""
        ])
    
    # Prepare response
    output.seek(0)
    return send_file(
        BytesIO(output.getvalue().encode('utf-8-sig')),
        mimetype='text/csv',
        as_attachment=True,
        download_name=f'depenses_{start_date.strftime("%Y%m%d")}-{end_date.strftime("%Y%m%d")}.csv'
    ) 

@bp.route('/products')
@login_required
def products():
    """Rapport des produits"""
    # Get period from query parameters
    period = request.args.get('period', 'month')
    
    # Set date range based on period
    end_date = datetime.now()
    if period == 'today':
        start_date = end_date.replace(hour=0, minute=0, second=0, microsecond=0)
    elif period == 'week':
        start_date = end_date - timedelta(days=7)
    elif period == 'month':
        start_date = end_date - timedelta(days=30)
    elif period == 'year':
        start_date = end_date - timedelta(days=365)
    elif period == 'custom':
        try:
            start_date = datetime.strptime(request.args.get('start_date'), '%Y-%m-%d')
            end_date = datetime.strptime(request.args.get('end_date'), '%Y-%m-%d')
            end_date = end_date.replace(hour=23, minute=59, second=59, microsecond=999999)
        except (TypeError, ValueError):
            start_date = end_date - timedelta(days=30)  # Default to month if invalid dates
    else:
        start_date = end_date - timedelta(days=30)  # Default to month
    
    # Get category filter
    category_id = request.args.get('category', type=int)
    
    # Query products
    query = Product.query.filter_by(owner_id=current_user.id)
    
    if category_id:
        query = query.filter_by(category_id=category_id)
    
    products = query.all()
    
    # Get all categories for filter
    categories = ProductCategory.query.filter_by(owner_id=current_user.id).all()
    
    # Calculate statistics
    total_products = len(products)
    active_products = sum(1 for p in products if p.get_available_quantity() > 0)  # Utiliser get_available_quantity
    out_of_stock = sum(1 for p in products if p.get_available_quantity() == 0)
    low_stock = sum(1 for p in products if 0 < p.get_available_quantity() <= p.minimum_stock)
    
    # Get sales data for the period (POS + Online) - Séparées pour le graphique
    pos_sales_data = {}
    online_sales_data = {}
    total_sales_data = {}
    current_date = start_date

    while current_date <= end_date:
        date_str = current_date.strftime('%Y-%m-%d')

        # POS sales for the day
        day_pos_sales = SaleItem.query.join(Sale).filter(
            Sale.owner_id == current_user.id,
            Sale.status.in_([SaleStatus.PAID, SaleStatus.COMPLETED]),
            Sale.created_at.between(
                current_date.replace(hour=0, minute=0, second=0),
                current_date.replace(hour=23, minute=59, second=59)
            )
        ).all()

        # Online sales for the day
        day_online_sales = OnlineOrderItem.query.join(OnlineOrder).filter(
            OnlineOrder.site.has(owner_id=current_user.id),
            OnlineOrder.payment_status == OnlinePaymentStatus.PAID,
            OnlineOrder.ordered_at.between(
                current_date.replace(hour=0, minute=0, second=0),
                current_date.replace(hour=23, minute=59, second=59)
            )
        ).all()

        pos_revenue = sum(item.quantity * item.price for item in day_pos_sales)
        online_revenue = sum(item.quantity * item.unit_price for item in day_online_sales)

        pos_sales_data[date_str] = pos_revenue
        online_sales_data[date_str] = online_revenue
        total_sales_data[date_str] = pos_revenue + online_revenue
        current_date += timedelta(days=1)

    dates = list(total_sales_data.keys())
    pos_sales_values = list(pos_sales_data.values())
    online_sales_values = list(online_sales_data.values())
    total_sales_values = list(total_sales_data.values())
    
    # Get top selling products (POS + Online)
    top_products = []
    for product in products:
        # POS sales for this product in the period
        pos_sales_items = SaleItem.query.join(Sale).filter(
            SaleItem.product_id == product.id,
            Sale.owner_id == current_user.id,
            Sale.status.in_([SaleStatus.COMPLETED, SaleStatus.PAID]),
            Sale.created_at.between(start_date, end_date)
        ).all()

        # Online sales for this product in the period
        online_sales_items = OnlineOrderItem.query.join(OnlineOrder).filter(
            OnlineOrderItem.product_id == product.id,
            OnlineOrder.site.has(owner_id=current_user.id),
            OnlineOrder.payment_status == OnlinePaymentStatus.PAID,
            OnlineOrder.ordered_at.between(start_date, end_date)
        ).all()

        pos_quantity = sum(item.quantity for item in pos_sales_items)
        pos_revenue = sum(item.quantity * item.price for item in pos_sales_items)

        online_quantity = sum(item.quantity for item in online_sales_items)
        online_revenue = sum(item.quantity * item.unit_price for item in online_sales_items)

        total_quantity = pos_quantity + online_quantity
        total_revenue = pos_revenue + online_revenue

        if total_quantity > 0:
            top_products.append({
                'name': product.name,
                'quantity': total_quantity,
                'revenue': total_revenue,
                'pos_quantity': pos_quantity,
                'online_quantity': online_quantity
            })

    top_products.sort(key=lambda x: x['revenue'], reverse=True)
    top_products = top_products[:5]  # Keep only top 5
    
    return render_template('reports/products.html',
                         title='Rapport des produits',
                         products=products,
                         categories=categories,
                         total_products=total_products,
                         active_products=active_products,
                         out_of_stock=out_of_stock,
                         low_stock=low_stock,
                         period=period,
                         start_date=start_date,
                         end_date=end_date,
                         dates=dates,
                         pos_sales_values=pos_sales_values,
                         online_sales_values=online_sales_values,
                         total_sales_values=total_sales_values,
                         top_products=top_products)

@bp.route('/products/export')
@login_required
def export_products():
    """Export the products report as CSV"""
    # Get period from query parameters
    period = request.args.get('period', 'month')
    
    # Set date range based on period
    end_date = datetime.now()
    if period == 'today':
        start_date = end_date.replace(hour=0, minute=0, second=0, microsecond=0)
    elif period == 'week':
        start_date = end_date - timedelta(days=7)
    elif period == 'month':
        start_date = end_date - timedelta(days=30)
    elif period == 'year':
        start_date = end_date - timedelta(days=365)
    elif period == 'custom':
        try:
            start_date = datetime.strptime(request.args.get('start_date'), '%Y-%m-%d')
            end_date = datetime.strptime(request.args.get('end_date'), '%Y-%m-%d')
            end_date = end_date.replace(hour=23, minute=59, second=59, microsecond=999999)
        except (TypeError, ValueError):
            start_date = end_date - timedelta(days=30)  # Default to month if invalid dates
    else:
        start_date = end_date - timedelta(days=30)  # Default to month
    
    # Get category filter
    category_id = request.args.get('category', type=int)
    
    # Query products
    query = Product.query.filter_by(owner_id=current_user.id)
    
    if category_id:
        query = query.filter_by(category_id=category_id)
    
    products = query.all()
    
    # Create CSV content
    output = StringIO()
    writer = csv.writer(output)
    
    # Write headers
    writer.writerow([
        'Nom', 'Catégorie', 'Prix', 'Stock actuel', 'Stock minimum', 'Statut',
        'Ventes (période)', 'Revenu (période)', 'Date de création', 'Dernière modification'
    ])
    
    # Write data
    for product in products:
        # Calculate POS sales for the period
        pos_sales = SaleItem.query.join(Sale).filter(
            Sale.owner_id == current_user.id,
            Sale.status.in_([SaleStatus.PAID, SaleStatus.COMPLETED]),
            Sale.created_at.between(start_date, end_date),
            SaleItem.product_id == product.id
        ).all()

        # Calculate online sales for the period
        online_sales = OnlineOrderItem.query.join(OnlineOrder).filter(
            OnlineOrder.site.has(owner_id=current_user.id),
            OnlineOrder.payment_status == OnlinePaymentStatus.PAID,
            OnlineOrder.ordered_at.between(start_date, end_date),
            OnlineOrderItem.product_id == product.id
        ).all()

        pos_quantity = sum(item.quantity for item in pos_sales)
        pos_revenue = sum(item.quantity * item.price for item in pos_sales)

        online_quantity = sum(item.quantity for item in online_sales)
        online_revenue = sum(item.quantity * item.unit_price for item in online_sales)

        period_quantity = pos_quantity + online_quantity
        period_revenue = pos_revenue + online_revenue
        
        # Determine stock status
        if product.stock_quantity == 0:
            status = 'Rupture'
        elif product.stock_quantity <= product.minimum_stock:
            status = 'Stock faible'
        else:
            status = 'En stock'
        
        writer.writerow([
            product.name,
            product.category.name if product.category else "Sans catégorie",
            f"{float(product.price):.2f}",
            product.stock_quantity,
            product.minimum_stock,
            status,
            period_quantity,
            f"{float(period_revenue):.2f}",
            product.created_at.strftime('%d/%m/%Y'),
            product.updated_at.strftime('%d/%m/%Y') if product.updated_at else ''
        ])
    
    # Prepare response
    output.seek(0)
    return send_file(
        BytesIO(output.getvalue().encode('utf-8-sig')),
        mimetype='text/csv',
        as_attachment=True,
        download_name=f'produits_{start_date.strftime("%Y%m%d")}-{end_date.strftime("%Y%m%d")}.csv'
    ) 

@bp.route('/profit-loss')
@login_required
def profit_loss():
    """Rapport de profits et pertes page Bilan"""
    period = request.args.get('period', 'today')
    
    # Gestion de la période personnalisée
    if period == 'custom':
        try:
            start_date = datetime.strptime(request.args.get('start_date'), '%Y-%m-%d')
            end_date = datetime.strptime(request.args.get('end_date'), '%Y-%m-%d')
            end_date = end_date.replace(hour=23, minute=59, second=59)
        except (TypeError, ValueError):
            flash('Dates invalides', 'error')
            return redirect(url_for('reports.profit_loss'))
    else:
        start_date, end_date = get_date_range(period)
    
    # Ventes (Chiffre d'affaires) - POS + Online
    pos_sales = Sale.query.filter(
        Sale.owner_id == current_user.id,
        Sale.created_at.between(start_date, end_date),
        Sale.status == SaleStatus.PAID
    ).all()

    online_orders = OnlineOrder.query.filter(
        OnlineOrder.site.has(owner_id=current_user.id),
        OnlineOrder.payment_status == OnlinePaymentStatus.PAID,
        OnlineOrder.ordered_at.between(start_date, end_date)
    ).all()

    pos_total_sales = sum(sale.total for sale in pos_sales)
    online_total_sales = sum(order.total_amount for order in online_orders)
    total_sales = pos_total_sales + online_total_sales
    
    # Coût des produits vendus (POS + Online)
    total_cost = 0

    # Coût des ventes POS
    for sale in pos_sales:
        for item in sale.items:
            product = item.product
            if product.has_recipe:
                total_cost += product.calculate_recipe_cost() * item.quantity
            else:
                total_cost += product.get_current_cost() * item.quantity

    # Coût des commandes en ligne
    for order in online_orders:
        for item in order.items:
            product = item.product
            if product.has_recipe:
                total_cost += product.calculate_recipe_cost() * item.quantity
            else:
                total_cost += product.get_current_cost() * item.quantity
    
    # Calcul du REX (Résultat d'Exploitation)
    rex = total_sales - total_cost
    rex_percentage = (rex / total_sales * 100) if total_sales > 0 else 0
    
    # Dépenses
    expenses = Expense.query.filter(
        Expense.owner_id == current_user.id,
        Expense.date.between(start_date.date(), end_date.date())
    ).all()
    total_expenses = sum(expense.amount for expense in expenses)
    
    # Calcul du RNET (Résultat Net)
    rnet = rex - total_expenses
    rnet_percentage = (rnet / total_sales * 100) if total_sales > 0 else 0
    
    # Get expense categories for the period
    expense_by_category = {}
    for expense in expenses:
        category = expense.category.name if expense.category else "Sans catégorie"
        if category not in expense_by_category:
            expense_by_category[category] = 0
        expense_by_category[category] += float(expense.amount)
    
    category_labels = list(expense_by_category.keys())
    category_values = list(expense_by_category.values())
    
    # Données pour les graphiques
    trend_dates = []
    trend_data = {
        'sales': [],
        'costs': [],
        'charges': [],
        'rex': [],
        'rnet': []
    }
    
    # Création d'un dictionnaire pour stocker les dépenses par date
    expenses_by_date = {}
    for expense in expenses:
        date_str = expense.date.strftime('%Y-%m-%d')
        if date_str not in expenses_by_date:
            expenses_by_date[date_str] = 0
        expenses_by_date[date_str] += expense.amount
    
    # Remplissage des données jour par jour
    current_date = start_date.date()
    end_date_only = end_date.date()
    
    while current_date <= end_date_only:
        date_str = current_date.strftime('%Y-%m-%d')
        
        # Ventes du jour (POS + Online)
        daily_pos_sales = sum(sale.total for sale in pos_sales
                             if sale.created_at.date() == current_date)
        daily_online_sales = sum(order.total_amount for order in online_orders
                                if order.ordered_at.date() == current_date)
        daily_sales = daily_pos_sales + daily_online_sales

        # Coût des produits du jour (POS + Online)
        daily_pos_cost = sum(
            (item.product.calculate_recipe_cost() if item.product.has_recipe
             else item.product.get_current_cost()) * item.quantity
            for sale in pos_sales if sale.created_at.date() == current_date
            for item in sale.items
        )
        daily_online_cost = sum(
            (item.product.calculate_recipe_cost() if item.product.has_recipe
             else item.product.get_current_cost()) * item.quantity
            for order in online_orders if order.ordered_at.date() == current_date
            for item in order.items
        )
        daily_cost = daily_pos_cost + daily_online_cost
        
        # Dépenses du jour
        daily_expenses = expenses_by_date.get(date_str, 0)
        
        # Calculs des résultats
        daily_rex = daily_sales - daily_cost
        daily_rnet = daily_rex - daily_expenses
        
        # Ajout des données
        trend_dates.append(date_str)
        trend_data['sales'].append(round(daily_sales, 2))
        trend_data['costs'].append(round(daily_cost, 2))
        trend_data['charges'].append(round(daily_expenses, 2))
        trend_data['rex'].append(round(daily_rex, 2))
        trend_data['rnet'].append(round(daily_rnet, 2))
        
        current_date += timedelta(days=1)
    
    # Couleurs pour les catégories
    category_colors = [
        'rgba(78, 115, 223, 0.8)',
        'rgba(54, 185, 204, 0.8)',
        'rgba(246, 194, 62, 0.8)',
        'rgba(231, 74, 59, 0.8)',
        'rgba(28, 200, 138, 0.8)',
        'rgba(133, 135, 150, 0.8)',
        'rgba(105, 0, 132, 0.8)',
        'rgba(255, 159, 64, 0.8)',
    ]
    
    while len(category_colors) < len(category_labels):
        category_colors.extend(category_colors)
    category_colors = category_colors[:len(category_labels)]

    return render_template('reports/profit_loss.html',
                         title='Rapport de profits et pertes',
                         period=period,
                         start_date=start_date,
                         end_date=end_date,
                         total_sales=total_sales,
                         total_cost=total_cost,
                         total_expenses=total_expenses,
                         rex=rex,
                         rex_percentage=rex_percentage,
                         rnet=rnet,
                         rnet_percentage=rnet_percentage,
                         trend_dates=trend_dates,
                         trend_data=trend_data,
                         category_labels=category_labels,
                         category_values=category_values,
                         category_colors=category_colors)

@bp.route('/profit-loss/export')
@login_required
def export_profit_loss():
    """Export the profit and loss report as CSV"""
    # Get period from query parameters
    period = request.args.get('period', 'month')
    
    # Set date range based on period
    end_date = datetime.now()
    if period == 'today':
        start_date = end_date.replace(hour=0, minute=0, second=0, microsecond=0)
    elif period == 'week':
        start_date = end_date - timedelta(days=7)
    elif period == 'month':
        start_date = end_date - timedelta(days=30)
    elif period == 'year':
        start_date = end_date - timedelta(days=365)
    elif period == 'custom':
        try:
            start_date = datetime.strptime(request.args.get('start_date'), '%Y-%m-%d')
            end_date = datetime.strptime(request.args.get('end_date'), '%Y-%m-%d')
            end_date = end_date.replace(hour=23, minute=59, second=59, microsecond=999999)
        except (TypeError, ValueError):
            start_date = end_date - timedelta(days=30)  # Default to month if invalid dates
    else:
        start_date = end_date - timedelta(days=30)  # Default to month
    
    # Get sales and expenses data
    sales = Sale.query.filter(
        Sale.owner_id == current_user.id,
        Sale.status.in_([SaleStatus.PAID, SaleStatus.COMPLETED]),
        Sale.created_at.between(start_date, end_date)
    ).all()
    
    expenses = Expense.query.filter(
        Expense.owner_id == current_user.id,
        Expense.date.between(start_date, end_date)
    ).all()
    
    # Create CSV content
    output = StringIO()
    writer = csv.writer(output)
    
    # Write headers
    writer.writerow(['Date', 'Type', 'Description', 'Montant'])
    
    # Write sales data
    for sale in sales:
        writer.writerow([
            sale.created_at.strftime('%d/%m/%Y'),
            'Vente',
            f'Vente #{sale.id}',
            float(sale.total)
        ])
    
    # Write expenses data
    for expense in expenses:
        writer.writerow([
            expense.date.strftime('%d/%m/%Y'),
            'Dépense',
            f'{expense.description} ({expense.category.name if expense.category else "Sans catégorie"})',
            float(expense.amount)
        ])
    
    # Calculate and write totals
    total_revenue = sum(sale.total for sale in sales)
    total_expenses = sum(expense.amount for expense in expenses)
    gross_profit = total_revenue - total_expenses
    profit_margin = (gross_profit / total_revenue * 100) if total_revenue > 0 else 0
    
    writer.writerow([])  # Empty row for spacing
    writer.writerow(['Totaux', '', '', ''])
    writer.writerow(['Chiffre d\'affaires', '', '', float(total_revenue)])
    writer.writerow(['Total des dépenses', '', '', float(total_expenses)])
    writer.writerow(['Bénéfice brut', '', '', float(gross_profit)])
    writer.writerow(['Marge bénéficiaire', '', '', f'{profit_margin:.2f}%'])
    
    # Prepare response
    output.seek(0)
    return send_file(
        BytesIO(output.getvalue().encode('utf-8-sig')),
        mimetype='text/csv',
        as_attachment=True,
        download_name=f'profit_loss_{start_date.strftime("%Y%m%d")}-{end_date.strftime("%Y%m%d")}.csv'
    ) 

@bp.route('/sales')
@login_required
def sales():
    """Rapport des ventes optimisé"""
    from sqlalchemy import func, cast, Date
    # Get period from query parameters
    period = request.args.get('period', 'month')
    end_date = datetime.now()
    if period == 'today':
        start_date = end_date.replace(hour=0, minute=0, second=0, microsecond=0)
    elif period == 'week':
        start_date = end_date - timedelta(days=7)
    elif period == 'month':
        start_date = end_date - timedelta(days=30)
    elif period == 'year':
        start_date = end_date - timedelta(days=365)
    elif period == 'custom':
        try:
            start_date = datetime.strptime(request.args.get('start_date'), '%Y-%m-%d')
            end_date = datetime.strptime(request.args.get('end_date'), '%Y-%m-%d')
            end_date = end_date.replace(hour=23, minute=59, second=59, microsecond=999999)
        except (TypeError, ValueError):
            start_date = end_date - timedelta(days=30)
    else:
        start_date = end_date - timedelta(days=30)

    # POS sales (optimisé)
    pos_sales = Sale.query.filter(
        Sale.owner_id == current_user.id,
        Sale.status.in_([SaleStatus.COMPLETED, SaleStatus.PAID]),
        Sale.created_at.between(start_date, end_date)
    ).order_by(Sale.created_at.desc()).all()

    # Online orders (optimisé)
    online_orders = OnlineOrder.query.filter(
        OnlineOrder.site.has(owner_id=current_user.id),
        OnlineOrder.payment_status == OnlinePaymentStatus.PAID,
        OnlineOrder.ordered_at.between(start_date, end_date)
    ).order_by(OnlineOrder.ordered_at.desc()).all()

    # Totaux globaux
    total_revenue = sum(sale.total for sale in pos_sales) + sum(order.total_amount for order in online_orders)
    all_sales = pos_sales + online_orders
    total_sales = len(all_sales)
    average_sale = total_revenue / total_sales if total_sales > 0 else 0
    days_count = (end_date - start_date).days + 1
    daily_average = total_revenue / days_count if days_count > 0 else 0

    # --- Optimisation: Agrégation SQL pour ventes par jour ---
    # POS
    pos_daily = db.session.query(
        func.date(Sale.created_at).label('date'),
        func.sum(Sale.total).label('total')
    ).filter(
        Sale.owner_id == current_user.id,
        Sale.status.in_([SaleStatus.COMPLETED, SaleStatus.PAID]),
        Sale.created_at.between(start_date, end_date)
    ).group_by(func.date(Sale.created_at)).all()
    # Online
    online_daily = db.session.query(
        func.date(OnlineOrder.ordered_at).label('date'),
        func.sum(OnlineOrder.total_amount).label('total')
    ).filter(
        OnlineOrder.site.has(owner_id=current_user.id),
        OnlineOrder.payment_status == OnlinePaymentStatus.PAID,
        OnlineOrder.ordered_at.between(start_date, end_date)
    ).group_by(func.date(OnlineOrder.ordered_at)).all()
    # Fusionner les deux sources
    daily_sales_map = {}
    for row in pos_daily:
        key = str(row.date)
        daily_sales_map[key] = row.total
    for row in online_daily:
        key = str(row.date)
        daily_sales_map[key] = daily_sales_map.get(key, 0) + row.total
    # Générer la liste ordonnée
    dates = [(start_date + timedelta(days=i)).strftime('%Y-%m-%d') for i in range(days_count)]
    sales_values = [round(daily_sales_map.get(date, 0), 2) for date in dates]

    # --- Optimisation: Top produits (agrégation SQL) ---
    # POS
    pos_products = db.session.query(
        SaleItem.product_id,
        func.sum(SaleItem.quantity).label('quantity'),
        func.sum(SaleItem.quantity * SaleItem.price).label('revenue')
    ).join(Sale).filter(
        Sale.owner_id == current_user.id,
        Sale.status.in_([SaleStatus.COMPLETED, SaleStatus.PAID]),
        Sale.created_at.between(start_date, end_date)
    ).group_by(SaleItem.product_id).all()
    # Online
    online_products = db.session.query(
        OnlineOrderItem.product_id,
        func.sum(OnlineOrderItem.quantity).label('quantity'),
        func.sum(OnlineOrderItem.quantity * OnlineOrderItem.unit_price).label('revenue')
    ).join(OnlineOrder).filter(
        OnlineOrder.site.has(owner_id=current_user.id),
        OnlineOrder.payment_status == OnlinePaymentStatus.PAID,
        OnlineOrder.ordered_at.between(start_date, end_date)
    ).group_by(OnlineOrderItem.product_id).all()
    # Fusionner les deux sources
    product_map = {}
    from app.modules.inventory.models_product import Product
    for row in pos_products:
        if row.product_id not in product_map:
            product_map[row.product_id] = {'quantity': 0, 'revenue': 0}
        product_map[row.product_id]['quantity'] += row.quantity or 0
        product_map[row.product_id]['revenue'] += row.revenue or 0
    for row in online_products:
        if row.product_id not in product_map:
            product_map[row.product_id] = {'quantity': 0, 'revenue': 0}
        product_map[row.product_id]['quantity'] += row.quantity or 0
        product_map[row.product_id]['revenue'] += row.revenue or 0
    # Récupérer les noms produits
    top_products = []
    if product_map:
        products = Product.query.filter(Product.id.in_(product_map.keys())).all()
        product_names = {p.id: p.name for p in products}
        for pid, stats in product_map.items():
            top_products.append({
                'name': product_names.get(pid, f'Produit {pid}'),
                'quantity': stats['quantity'],
                'revenue': stats['revenue']
            })
        top_products.sort(key=lambda x: x['revenue'], reverse=True)
        top_products = top_products[:5]
    product_labels = [p['name'] for p in top_products]
    product_data = [p['revenue'] for p in top_products]
    product_colors = [
        'rgba(78, 115, 223, 0.8)',
        'rgba(54, 185, 204, 0.8)',
        'rgba(246, 194, 62, 0.8)',
        'rgba(231, 74, 59, 0.8)',
        'rgba(28, 200, 138, 0.8)'
    ]
    pos_revenue = sum(sale.total for sale in pos_sales)
    online_revenue = sum(order.total_amount for order in online_orders)

    # Couleurs pour les badges de mode de paiement
    payment_method_colors = {
        'cash': 'success',
        'CASH': 'success',
        'card': 'primary', 
        'CARD': 'primary',
        'check': 'info',
        'CHECK': 'info',
        'transfer': 'warning',
        'TRANSFER': 'warning',
        'other': 'secondary',
        'OTHER': 'secondary',
        'online_payment': 'purple',
        'ONLINE_PAYMENT': 'purple',
        'cash_on_delivery': 'success',
        'CASH_ON_DELIVERY': 'success',
        'card_on_delivery': 'primary',
        'CARD_ON_DELIVERY': 'primary'
    }

    return render_template('reports/sales.html',
                         title='Rapport des ventes',
                         pos_sales=pos_sales,
                         online_orders=online_orders,
                         all_sales=all_sales,
                         total_revenue=total_revenue,
                         total_sales=total_sales,
                         average_sale=average_sale,
                         daily_average=daily_average,
                         period=period,
                         start_date=start_date,
                         end_date=end_date,
                         dates=dates,
                         sales_values=sales_values,
                         top_products=top_products,
                         product_labels=product_labels,
                         product_data=product_data,
                         product_colors=product_colors,
                         pos_revenue=pos_revenue,
                         online_revenue=online_revenue,
                         payment_method_colors=payment_method_colors)

@bp.route('/sales/export')
@login_required
def export_sales():
    """Export the sales report"""
    format = request.args.get('format', 'pdf')  # Par défaut PDF si non spécifié
    
    # Get period from query parameters
    period = request.args.get('period', 'month')
    
    # Set date range based on period
    end_date = datetime.now()
    if period == 'today':
        start_date = end_date.replace(hour=0, minute=0, second=0, microsecond=0)
    elif period == 'week':
        start_date = end_date - timedelta(days=7)
    elif period == 'month':
        start_date = end_date - timedelta(days=30)
    elif period == 'year':
        start_date = end_date - timedelta(days=365)
    elif period == 'custom':
        try:
            start_date = datetime.strptime(request.args.get('start_date'), '%Y-%m-%d')
            end_date = datetime.strptime(request.args.get('end_date'), '%Y-%m-%d')
            end_date = end_date.replace(hour=23, minute=59, second=59, microsecond=999999)
        except (TypeError, ValueError):
            start_date = end_date - timedelta(days=30)  # Default to month if invalid dates
    else:
        start_date = end_date - timedelta(days=30)  # Default to month
    
    # Query sales
    sales = Sale.query.filter(
        Sale.owner_id == current_user.id,
        Sale.status.in_([SaleStatus.PAID, SaleStatus.COMPLETED]),
        Sale.created_at.between(start_date, end_date)
    ).order_by(Sale.created_at.desc()).all()
    
    # Create CSV content
    output = StringIO()
    writer = csv.writer(output)
    
    # Write headers
    writer.writerow(['Date', 'Numéro', 'Produits', 'Quantité totale', 'Montant total', 'Mode de paiement'])
    
    # Write data
    for sale in sales:
        products = '; '.join([f"{item.product.name} (x{item.quantity})" for item in sale.items])
        total_quantity = sum(item.quantity for item in sale.items)
        
        writer.writerow([
            sale.created_at.strftime('%d/%m/%Y %H:%M'),
            f'#{sale.id}',
            products,
            total_quantity,
            float(sale.total),
            sale.payment_method
        ])
    
    # Calculate and write totals
    total_revenue = sum(sale.total for sale in sales)
    total_quantity = sum(sum(item.quantity for item in sale.items) for sale in sales)
    
    writer.writerow([])  # Empty row for spacing
    writer.writerow(['Totaux', '', '', total_quantity, float(total_revenue), ''])
    
    # Prepare response
    output.seek(0)
    return send_file(
        BytesIO(output.getvalue().encode('utf-8-sig')),
        mimetype='text/csv',
        as_attachment=True,
        download_name=f'ventes_{start_date.strftime("%Y%m%d")}-{end_date.strftime("%Y%m%d")}.csv'
    ) 


@bp.route('/products/details/<int:id>')
@login_required
def get_product_details(id):
    """Get product details"""
    product = Product.query.filter_by(id=id, owner_id=current_user.id).first_or_404()

    # Calculate cost and margin
    cost = product.calculate_recipe_cost() if product.has_recipe else product.get_current_cost()
    margin = product.price - cost if product.price > 0 else 0
    margin_rate = (margin / product.price * 100) if product.price > 0 else 0

    # Add calculated values to product object
    product.cost = cost
    product.margin = margin
    product.margin_rate = margin_rate

    # Get sales trend data for the last 30 days
    end_date = datetime.now()
    start_date = end_date - timedelta(days=30)

    # Get daily POS sales data
    daily_pos_sales = db.session.query(
        func.date(Sale.created_at).label('date'),
        func.sum(SaleItem.quantity).label('quantity')
    ).join(SaleItem).filter(
        Sale.owner_id == current_user.id,
        Sale.status.in_([SaleStatus.PAID, SaleStatus.COMPLETED]),
        SaleItem.product_id == product.id,
        Sale.created_at.between(start_date, end_date)
    ).group_by(func.date(Sale.created_at)).all()

    # Get daily online sales data
    daily_online_sales = db.session.query(
        func.date(OnlineOrder.ordered_at).label('date'),
        func.sum(OnlineOrderItem.quantity).label('quantity')
    ).join(OnlineOrderItem).filter(
        OnlineOrder.site.has(owner_id=current_user.id),
        OnlineOrder.payment_status == OnlinePaymentStatus.PAID,
        OnlineOrderItem.product_id == product.id,
        OnlineOrder.ordered_at.between(start_date, end_date)
    ).group_by(func.date(OnlineOrder.ordered_at)).all()

    # Prepare trend data with separate channels
    dates = []
    pos_quantities = []
    online_quantities = []
    total_quantities = []
    current_date = start_date

    pos_sales_dict = {str(sale.date): sale.quantity for sale in daily_pos_sales}
    online_sales_dict = {str(sale.date): sale.quantity for sale in daily_online_sales}

    while current_date <= end_date:
        date_str = current_date.strftime('%Y-%m-%d')
        dates.append(current_date.strftime('%d/%m'))

        pos_qty = pos_sales_dict.get(date_str, 0) or 0
        online_qty = online_sales_dict.get(date_str, 0) or 0
        total_qty = pos_qty + online_qty

        pos_quantities.append(pos_qty)
        online_quantities.append(online_qty)
        total_quantities.append(total_qty)
        current_date += timedelta(days=1)

    trend = {
        'dates': dates,
        'pos_quantities': pos_quantities,
        'online_quantities': online_quantities,
        'total_quantities': total_quantities
    }

    return render_template('reports/_product_details.html', product=product, trend=trend)

@bp.route('/products/<int:id>')
@login_required
def get_product(id):
    """Get product data"""
    product = Product.query.filter_by(id=id, owner_id=current_user.id).first_or_404()
    return jsonify({
        'name': product.name,
        'description': product.description,
        'category_id': product.category_id,
        'price': float(product.price),
        'minimum_stock': product.minimum_stock
    })

@bp.route('/products/view/<int:id>')
@login_required
def view_product(id):
    """View product details"""
    product = Product.query.filter_by(id=id, owner_id=current_user.id).first_or_404()
    return render_template('reports/view_product.html', product=product)

@bp.route('/products/edit/<int:id>', methods=['GET', 'POST'])
@login_required
def edit_product(id):
    """Edit product"""
    product = Product.query.filter_by(id=id, owner_id=current_user.id).first_or_404()
    
    if request.method == 'POST':
        try:
            product.name = request.form['name']
            product.description = request.form['description']
            product.category_id = request.form['category_id'] or None
            product.price = request.form['price']
            product.minimum_stock = request.form['minimum_stock']
            
            db.session.commit()
            flash('Produit mis à jour avec succès.', 'success')
            return redirect(url_for('reports.products'))
        except Exception as e:
            db.session.rollback()
            flash(str(e), 'error')
    
    categories = ProductCategory.query.filter_by(owner_id=current_user.id).all()
    return render_template('reports/edit_product.html', product=product, categories=categories)

@bp.route('/products/delete/<int:id>', methods=['GET', 'POST'])
@login_required
def delete_product(id):
    """Delete product"""
    product = Product.query.filter_by(id=id, owner_id=current_user.id).first_or_404()
    
    if request.method == 'POST':
        try:
            db.session.delete(product)
            db.session.commit()
            flash('Produit supprimé avec succès.', 'success')
            return redirect(url_for('reports.products'))
        except Exception as e:
            db.session.rollback()
            flash(str(e), 'error')
    
    return render_template('reports/delete_product.html', product=product)


@bp.route('/dashboard')
@login_required
def dashboard():
    """Tableau de bord des rapports (page Rapports de ventes 1)"""
    # Get period from query parameters
    period = request.args.get('period', 'month')
    
    # Set date range based on period
    end_date = datetime.now()
    if period == 'today':
        start_date = end_date.replace(hour=0, minute=0, second=0, microsecond=0)
    elif period == 'week':
        start_date = end_date - timedelta(days=7)
    elif period == 'month':
        start_date = end_date - timedelta(days=30)
    elif period == 'year':
        start_date = end_date - timedelta(days=365)
    elif period == 'custom':
        try:
            start_date = datetime.strptime(request.args.get('start_date'), '%Y-%m-%d')
            end_date = datetime.strptime(request.args.get('end_date'), '%Y-%m-%d')
        except (TypeError, ValueError):
            start_date = end_date - timedelta(days=30)
    else:
        start_date = end_date - timedelta(days=30)

    # Get POS sales data
    pos_sales = Sale.query.filter(
        Sale.owner_id == current_user.id,
        Sale.status.in_([SaleStatus.COMPLETED, SaleStatus.PAID]),
        Sale.created_at.between(start_date, end_date)
    ).order_by(Sale.created_at.desc()).all()

    # Get online orders data
    online_orders = OnlineOrder.query.filter(
        OnlineOrder.site.has(owner_id=current_user.id),
        OnlineOrder.payment_status == OnlinePaymentStatus.PAID,
        OnlineOrder.ordered_at.between(start_date, end_date)
    ).order_by(OnlineOrder.ordered_at.desc()).all()

    # Calculate revenue metrics (combining both)
    pos_revenue = sum(sale.total for sale in pos_sales)
    online_revenue = sum(order.total_amount for order in online_orders)
    total_revenue = pos_revenue + online_revenue
    total_orders = len(pos_sales) + len(online_orders)
    average_order = total_revenue / total_orders if total_orders > 0 else 0

    # Calculate revenue change vs previous period
    previous_start = start_date - (end_date - start_date)
    previous_pos_sales = Sale.query.filter(
        Sale.owner_id == current_user.id,
        Sale.status.in_([SaleStatus.COMPLETED, SaleStatus.PAID]),
        Sale.created_at.between(previous_start, start_date)
    ).all()
    previous_online_orders = OnlineOrder.query.filter(
        OnlineOrder.site.has(owner_id=current_user.id),
        OnlineOrder.payment_status == OnlinePaymentStatus.PAID,
        OnlineOrder.ordered_at.between(previous_start, start_date)
    ).all()
    previous_revenue = sum(sale.total for sale in previous_pos_sales) + sum(order.total_amount for order in previous_online_orders)
    revenue_change = ((total_revenue - previous_revenue) / previous_revenue * 100) if previous_revenue > 0 else 0

    # Get expenses data
    expenses = Expense.query.filter(
        Expense.owner_id == current_user.id,
        Expense.date.between(start_date.date(), end_date.date())
    ).all()
    total_expenses = sum(expense.amount for expense in expenses)
    expenses_count = len(expenses)

    # Calculate profit metrics
    gross_profit = total_revenue - total_expenses
    profit_margin = (gross_profit / total_revenue * 100) if total_revenue > 0 else 0

    # Get daily revenue data for chart (combining POS and online)
    daily_revenue = {}
    current_date = start_date
    while current_date <= end_date:
        date_str = current_date.strftime('%Y-%m-%d')
        day_pos_sales = [s for s in pos_sales if s.created_at.date() == current_date.date()]
        day_online_orders = [o for o in online_orders if o.ordered_at.date() == current_date.date()]
        daily_revenue[date_str] = sum(s.total for s in day_pos_sales) + sum(o.total_amount for o in day_online_orders)
        current_date += timedelta(days=1)

    revenue_dates = list(daily_revenue.keys())
    revenue_data = list(daily_revenue.values())

    # Get top products (combining POS and online)
    product_sales = {}

    # Process POS sales
    for sale in pos_sales:
        for item in sale.items:
            if item.product_id not in product_sales:
                product_sales[item.product_id] = {
                    'name': item.product.name,
                    'quantity': 0,
                    'revenue': 0
                }
            product_sales[item.product_id]['quantity'] += item.quantity
            product_sales[item.product_id]['revenue'] += item.quantity * item.price

    # Process online orders
    for order in online_orders:
        for item in order.items:
            if item.product_id not in product_sales:
                product_sales[item.product_id] = {
                    'name': item.product.name,
                    'quantity': 0,
                    'revenue': 0
                }
            product_sales[item.product_id]['quantity'] += item.quantity
            product_sales[item.product_id]['revenue'] += item.quantity * item.unit_price

    top_products = sorted(product_sales.values(), key=lambda x: x['revenue'], reverse=True)[:5]

    # Prepare product chart data
    product_labels = [p['name'] for p in top_products]
    product_data = [p['revenue'] for p in top_products]
    product_colors = [
        'rgba(78, 115, 223, 0.8)',
        'rgba(54, 185, 204, 0.8)',
        'rgba(246, 194, 62, 0.8)',
        'rgba(231, 74, 59, 0.8)',
        'rgba(28, 200, 138, 0.8)'
    ]

    # Get low stock items
    products = Product.query.filter_by(owner_id=current_user.id).all()
    low_stock_items = [p for p in products if p.get_available_quantity() <= p.minimum_stock]

    return render_template('reports/dashboard.html',
                         period=period,
                         start_date=start_date,
                         end_date=end_date,
                         total_revenue=total_revenue,
                         pos_revenue=pos_revenue,
                         online_revenue=online_revenue,
                         revenue_change=revenue_change,
                         total_orders=total_orders,
                         pos_orders_count=len(pos_sales),
                         online_orders_count=len(online_orders),
                         average_order=average_order,
                         total_expenses=total_expenses,
                         expenses_count=expenses_count,
                         gross_profit=gross_profit,
                         profit_margin=profit_margin,
                         revenue_dates=revenue_dates,
                         revenue_data=revenue_data,
                         product_labels=product_labels,
                         product_data=product_data,
                         product_colors=product_colors,
                         top_products=top_products,
                         recent_pos_orders=pos_sales[:5],
                         recent_online_orders=online_orders[:5],
                         low_stock_items=low_stock_items)

@bp.route('/tables')
@login_required
def tables_report():
    """Rapport des ventes par salle et table"""
    # Get period from query parameters
    period = request.args.get('period', 'month')
    start_date, end_date = get_date_range(period)

    print(f"Debug - Date range: {start_date} to {end_date}")
    print(f"Debug - Current user ID: {current_user.id}")

    # Récupérer toutes les salles et tables de l'utilisateur
    rooms = Room.query.filter_by(owner_id=current_user.id).all()
    tables = Table.query.filter_by(owner_id=current_user.id).all()

    # Statistiques par salle
    room_stats = {}
    for room in rooms:
        # Ventes POS pour cette salle
        pos_sales = Sale.query.join(Table, Sale.table_id == Table.id).filter(
            Table.room_id == room.id,
            Sale.owner_id == current_user.id,
            Sale.status.in_([SaleStatus.COMPLETED, SaleStatus.PAID]),
            Sale.created_at.between(start_date, end_date)
        ).all()

        # Commandes en ligne pour cette salle (dine_in avec table)
        online_orders = OnlineOrder.query.join(Table, OnlineOrder.table_id == Table.id).filter(
            Table.room_id == room.id,
            OnlineOrder.site.has(owner_id=current_user.id),
            OnlineOrder.payment_status == OnlinePaymentStatus.PAID,
            OnlineOrder.ordered_at.between(start_date, end_date)
        ).all()

        room_revenue = sum(sale.total for sale in pos_sales) + sum(order.total_amount for order in online_orders)
        room_orders_count = len(pos_sales) + len(online_orders)

        room_stats[room.id] = {
            'room': room,
            'revenue': room_revenue,
            'orders_count': room_orders_count,
            'pos_sales': pos_sales,
            'online_orders': online_orders,
            'tables_count': len(room.tables)
        }

    # Statistiques par table
    table_stats = {}
    for table in tables:
        # Ventes POS pour cette table
        pos_sales = Sale.query.filter(
            Sale.table_id == table.id,
            Sale.owner_id == current_user.id,
            Sale.status.in_([SaleStatus.COMPLETED, SaleStatus.PAID]),
            Sale.created_at.between(start_date, end_date)
        ).all()

        # Commandes en ligne pour cette table
        online_orders = OnlineOrder.query.filter(
            OnlineOrder.table_id == table.id,
            OnlineOrder.site.has(owner_id=current_user.id),
            OnlineOrder.payment_status == OnlinePaymentStatus.PAID,
            OnlineOrder.ordered_at.between(start_date, end_date)
        ).all()

        table_revenue = sum(sale.total for sale in pos_sales) + sum(order.total_amount for order in online_orders)
        table_orders_count = len(pos_sales) + len(online_orders)

        table_stats[table.id] = {
            'table': table,
            'revenue': table_revenue,
            'orders_count': table_orders_count,
            'pos_sales': pos_sales,
            'online_orders': online_orders,
            'average_order': table_revenue / table_orders_count if table_orders_count > 0 else 0
        }

    # Calculs globaux
    total_revenue = sum(stats['revenue'] for stats in room_stats.values())
    total_orders = sum(stats['orders_count'] for stats in room_stats.values())

    # Top 5 des salles par chiffre d'affaires
    top_rooms = sorted(room_stats.values(), key=lambda x: x['revenue'], reverse=True)[:5]

    # Top 5 des tables par chiffre d'affaires
    top_tables = sorted(table_stats.values(), key=lambda x: x['revenue'], reverse=True)[:5]

    return render_template('reports/tables.html',
                         title='Rapport des ventes par salle et table',
                         period=period,
                         start_date=start_date,
                         end_date=end_date,
                         rooms=rooms,
                         tables=tables,
                         room_stats=room_stats,
                         table_stats=table_stats,
                         total_revenue=total_revenue,
                         total_orders=total_orders,
                         top_rooms=top_rooms,
                         top_tables=top_tables)

@bp.route('/categories')
@login_required
def categories_report():
    """Rapport des ventes par catégorie et produit"""
    # Get period from query parameters
    period = request.args.get('period', 'month')
    start_date, end_date = get_date_range(period)

    print(f"Debug - Date range: {start_date} to {end_date}")
    print(f"Debug - Current user ID: {current_user.id}")

    # Debug: Vérifier les commandes en ligne
    debug_online_orders = OnlineOrder.query.filter(
        OnlineOrder.site.has(owner_id=current_user.id),
        OnlineOrder.ordered_at.between(start_date, end_date)
    ).all()
    print(f"Debug - Total online orders in period: {len(debug_online_orders)}")
    paid_orders = [o for o in debug_online_orders if o.payment_status == OnlinePaymentStatus.PAID]
    print(f"Debug - Paid online orders: {len(paid_orders)}")

    # Récupérer toutes les catégories de l'utilisateur
    categories = ProductCategory.query.filter_by(owner_id=current_user.id).all()

    # Statistiques par catégorie
    category_stats = {}
    for category in categories:
        # Ventes POS pour cette catégorie
        pos_sales_items = SaleItem.query.join(Sale).join(Product).filter(
            Product.category_id == category.id,
            Sale.owner_id == current_user.id,
            Sale.status.in_([SaleStatus.PAID, SaleStatus.COMPLETED]),
            Sale.created_at.between(start_date, end_date)
        ).all()

        # Commandes en ligne pour cette catégorie
        online_order_items = OnlineOrderItem.query.join(OnlineOrder).join(Product).filter(
            Product.category_id == category.id,
            OnlineOrder.site.has(owner_id=current_user.id),
            OnlineOrder.payment_status == OnlinePaymentStatus.PAID,
            OnlineOrder.ordered_at.between(start_date, end_date)
        ).all()

        # Calculs pour la catégorie
        pos_revenue = sum(item.price * item.quantity for item in pos_sales_items)
        online_revenue = sum(item.unit_price * item.quantity for item in online_order_items)
        total_revenue = pos_revenue + online_revenue

        pos_quantity = sum(item.quantity for item in pos_sales_items)
        online_quantity = sum(item.quantity for item in online_order_items)
        total_quantity = pos_quantity + online_quantity

        # Coût des produits vendus (COGS)
        total_cost = 0
        for item in pos_sales_items:
            total_cost += item.product.get_current_cost() * item.quantity
        for item in online_order_items:
            total_cost += item.product.get_current_cost() * item.quantity

        profit = total_revenue - total_cost
        profit_margin = (profit / total_revenue * 100) if total_revenue > 0 else 0

        category_stats[category.id] = {
            'category': category,
            'revenue': total_revenue,
            'pos_revenue': pos_revenue,
            'online_revenue': online_revenue,
            'quantity_sold': total_quantity,
            'pos_quantity': pos_quantity,
            'online_quantity': online_quantity,
            'cost': total_cost,
            'profit': profit,
            'profit_margin': profit_margin,
            'products_count': category.products.count()
        }

    # Statistiques par produit (top produits)
    products = Product.query.filter_by(owner_id=current_user.id, is_active=True).all()
    product_stats = {}

    for product in products:
        # Ventes POS pour ce produit
        pos_sales_items = SaleItem.query.join(Sale).filter(
            SaleItem.product_id == product.id,
            Sale.owner_id == current_user.id,
            Sale.status.in_([SaleStatus.PAID, SaleStatus.COMPLETED]),
            Sale.created_at.between(start_date, end_date)
        ).all()

        # Commandes en ligne pour ce produit
        online_order_items = OnlineOrderItem.query.join(OnlineOrder).filter(
            OnlineOrderItem.product_id == product.id,
            OnlineOrder.site.has(owner_id=current_user.id),
            OnlineOrder.payment_status == OnlinePaymentStatus.PAID,
            OnlineOrder.ordered_at.between(start_date, end_date)
        ).all()

        # Calculs pour le produit
        pos_revenue = sum(item.price * item.quantity for item in pos_sales_items)
        online_revenue = sum(item.unit_price * item.quantity for item in online_order_items)
        total_revenue = pos_revenue + online_revenue

        pos_quantity = sum(item.quantity for item in pos_sales_items)
        online_quantity = sum(item.quantity for item in online_order_items)
        total_quantity = pos_quantity + online_quantity

        total_cost = product.get_current_cost() * total_quantity if total_quantity > 0 else 0
        profit = total_revenue - total_cost
        profit_margin = (profit / total_revenue * 100) if total_revenue > 0 else 0
        average_price = total_revenue / total_quantity if total_quantity > 0 else 0

        product_stats[product.id] = {
            'product': product,
            'revenue': total_revenue,
            'pos_revenue': pos_revenue,
            'online_revenue': online_revenue,
            'quantity_sold': total_quantity,
            'pos_quantity': pos_quantity,
            'online_quantity': online_quantity,
            'cost': total_cost,
            'profit': profit,
            'profit_margin': profit_margin,
            'average_price': average_price
        }

    # Calculs globaux
    total_revenue = sum(stats['revenue'] for stats in category_stats.values())
    total_quantity = sum(stats['quantity_sold'] for stats in category_stats.values())
    total_cost = sum(stats['cost'] for stats in category_stats.values())
    total_profit = total_revenue - total_cost
    overall_margin = (total_profit / total_revenue * 100) if total_revenue > 0 else 0

    # Top 10 des catégories par chiffre d'affaires
    top_categories = sorted(category_stats.values(), key=lambda x: x['revenue'], reverse=True)[:10]

    # Top 20 des produits par chiffre d'affaires
    top_products = sorted(product_stats.values(), key=lambda x: x['revenue'], reverse=True)[:20]

    # Top 10 des produits par quantité
    top_products_quantity = sorted(product_stats.values(), key=lambda x: x['quantity_sold'], reverse=True)[:10]

    # Préparer les données pour les graphiques
    # "Répartition par Catégorie" (par chiffre d'affaires)
    category_chart_labels = [c['category'].name for c in top_categories]
    category_chart_data = [c['revenue'] for c in top_categories]

    # "Top 10 Produits" (par quantité vendue)
    product_chart_labels = [p['product'].name for p in top_products_quantity]
    product_chart_data = [p['quantity_sold'] for p in top_products_quantity]

    # Couleurs pour le graphique des catégories
    category_colors = [
        'rgba(78, 115, 223, 0.8)', 'rgba(28, 200, 138, 0.8)', 'rgba(54, 185, 204, 0.8)',
        'rgba(246, 194, 62, 0.8)', 'rgba(231, 74, 59, 0.8)', 'rgba(105, 0, 132, 0.8)',
        'rgba(255, 159, 64, 0.8)', 'rgba(133, 135, 150, 0.8)', 'rgba(255, 99, 132, 0.8)',
        'rgba(75, 192, 192, 0.8)'
    ]

    # Couleurs pour les badges de catégorie
    badge_colors = [
        'primary', 'success', 'info', 'warning', 'danger', 
        'secondary', 'dark', 'light', 'primary', 'success'
    ]
    
    # Créer un mapping de couleurs pour chaque catégorie
    category_badge_colors = {}
    for i, category in enumerate(categories):
        category_badge_colors[category.id] = badge_colors[i % len(badge_colors)]

    return render_template('reports/categories.html',
                         title='Rapport des ventes par catégorie et produit',
                         period=period,
                         start_date=start_date,
                         end_date=end_date,
                         categories=categories,
                         products=products,
                         category_stats=category_stats,
                         product_stats=product_stats,
                         total_revenue=total_revenue,
                         total_quantity=total_quantity,
                         total_cost=total_cost,
                         total_profit=total_profit,
                         overall_margin=overall_margin,
                         top_categories=top_categories,
                         top_products=top_products,
                         top_products_quantity=top_products_quantity,
                         category_chart_labels=category_chart_labels,
                         category_chart_data=category_chart_data,
                         category_colors=category_colors,
                         category_badge_colors=category_badge_colors,
                         product_chart_labels=product_chart_labels,
                         product_chart_data=product_chart_data)