from typing import Dict

from .. import Provider as LoremProvider


class Provider(LoremProvider):
    """Implement lorem provider for ``de_DE`` locale.

    Word list is based on the source below, and some words have been removed
    because of some duplications.

    Sources:

    - https://www.gut1.de/grundwortschatz/grundwortschatz-500/
    """

    word_list = (
        "ab",
        "Abend",
        "aber",
        "acht",
        "Affe",
        "alle",
        "allein",
        "als",
        "also",
        "alt",
        "am",
        "an",
        "andere",
        "anfangen",
        "Angst",
        "antworten",
        "Apfel",
        "Arbeit",
        "arbeiten",
        "Arzt",
        "auch",
        "auf",
        "Auge",
        "aus",
        "Auto",
        "baden",
        "bald",
        "Ball",
        "bauen",
        "Bauer",
        "Baum",
        "bei",
        "beide",
        "beim",
        "Bein",
        "Beispiel",
        "beißen",
        "bekommen",
        "<PERSON>",
        "besser",
        "Bett",
        "Bild",
        "bin",
        "bis",
        "blau",
        "bleiben",
        "Blume",
        "Boden",
        "brauchen",
        "braun",
        "Brief",
        "bringen",
        "Brot",
        "<PERSON>ruder",
        "<PERSON>uch",
        "böse",
        "da",
        "dabei",
        "dafür",
        "damit",
        "danach",
        "dann",
        "daran",
        "darauf",
        "darin",
        "das",
        "dauern",
        "davon",
        "dazu",
        "dein",
        "dem",
        "den",
        "denken",
        "denn",
        "der",
        "deshalb",
        "dich",
        "dick",
        "die",
        "Ding",
        "dir",
        "doch",
        "Dorf",
        "dort",
        "draußen",
        "drehen",
        "drei",
        "dumm",
        "dunkel",
        "durch",
        "dürfen",
        "eigentlich",
        "ein",
        "einfach",
        "einige",
        "einigen",
        "einmal",
        "Eis",
        "Eltern",
        "Ende",
        "endlich",
        "er",
        "Erde",
        "erklären",
        "erschrecken",
        "erst",
        "erzählen",
        "es",
        "essen",
        "Essen",
        "etwas",
        "fahren",
        "Fahrrad",
        "fallen",
        "Familie",
        "fangen",
        "fast",
        "fehlen",
        "Fenster",
        "Ferien",
        "fertig",
        "fest",
        "Feuer",
        "fiel",
        "finden",
        "Finger",
        "Fisch",
        "Flasche",
        "fliegen",
        "Frage",
        "fragen",
        "Frau",
        "frei",
        "fressen",
        "Freude",
        "freuen",
        "Freund",
        "fröhlich",
        "früh",
        "früher",
        "Fuß",
        "Fußball",
        "fährt",
        "führen",
        "fünf",
        "für",
        "gab",
        "ganz",
        "gar",
        "Garten",
        "geben",
        "Geburtstag",
        "gefährlich",
        "gegen",
        "gehen",
        "gehören",
        "gelb",
        "Geld",
        "genau",
        "gerade",
        "gern",
        "Geschenk",
        "Geschichte",
        "Gesicht",
        "gestern",
        "gesund",
        "gewinnen",
        "gibt",
        "ging",
        "Glas",
        "glauben",
        "gleich",
        "Glück",
        "glücklich",
        "Gott",
        "groß",
        "grün",
        "gut",
        "Haare",
        "haben",
        "halbe",
        "halten",
        "Hand",
        "hart",
        "Hase",
        "hat",
        "Haus",
        "heiß",
        "heißen",
        "helfen",
        "her",
        "heraus",
        "Herr",
        "Herz",
        "heute",
        "hier",
        "Hilfe",
        "Himmel",
        "hin",
        "hinein",
        "hinter",
        "hoch",
        "holen",
        "Hund",
        "Hunger",
        "hängen",
        "hören",
        "ich",
        "ihm",
        "ihn",
        "ihr",
        "im",
        "immer",
        "in",
        "ins",
        "ist",
        "ja",
        "Jahr",
        "jeder",
        "jetzt",
        "jung",
        "Junge",
        "kalt",
        "kam",
        "kann",
        "Katze",
        "kaufen",
        "kein",
        "kennen",
        "Kind",
        "Klasse",
        "klein",
        "klettern",
        "kochen",
        "kommen",
        "Kopf",
        "krank",
        "kurz",
        "können",
        "Küche",
        "lachen",
        "Land",
        "lange",
        "langsam",
        "las",
        "lassen",
        "laufen",
        "laut",
        "leben",
        "Leben",
        "legen",
        "Lehrer",
        "Lehrerin",
        "leicht",
        "leise",
        "lernen",
        "lesen",
        "letzte",
        "Leute",
        "Licht",
        "lieb",
        "liegen",
        "ließ",
        "Loch",
        "los",
        "Luft",
        "lustig",
        "machen",
        "mal",
        "Mama",
        "man",
        "Mann",
        "Maus",
        "Meer",
        "mehr",
        "mein",
        "Mensch",
        "merken",
        "mich",
        "Milch",
        "Minute",
        "Minutenmir",
        "mit",
        "Monat",
        "Monate",
        "Musik",
        "Mutter",
        "Mädchen",
        "mögen",
        "möglich",
        "müde",
        "müssen",
        "nach",
        "Nacht",
        "nah",
        "Name",
        "Nase",
        "nass",
        "natürlich",
        "neben",
        "nehmen",
        "nein",
        "nennen",
        "neu",
        "neun",
        "nicht",
        "nichts",
        "nie",
        "nimmt",
        "noch",
        "nun",
        "nur",
        "nächste",
        "nämlich",
        "ob",
        "oben",
        "oder",
        "offen",
        "oft",
        "ohne",
        "Oma",
        "Onkel",
        "Opa",
        "packen",
        "Papa",
        "Pferd",
        "Platz",
        "plötzlich",
        "Polizei",
        "Rad",
        "rechnen",
        "reich",
        "reiten",
        "rennen",
        "richtig",
        "rot",
        "rufen",
        "ruhig",
        "rund",
        "Sache",
        "sagen",
        "schaffen",
        "schauen",
        "scheinen",
        "schenken",
        "schicken",
        "Schiff",
        "schlafen",
        "schlagen",
        "schlecht",
        "schlimm",
        "Schluss",
        "Schnee",
        "schnell",
        "schon",
        "schreiben",
        "schreien",
        "Schuh",
        "Schule",
        "schwarz",
        "schwer",
        "Schwester",
        "schwimmen",
        "schön",
        "Schüler",
        "sechs",
        "See",
        "sehen",
        "sehr",
        "sein",
        "seit",
        "Seite",
        "selbst",
        "setzen",
        "sich",
        "sicher",
        "sie",
        "sieben",
        "sieht",
        "sind",
        "singen",
        "sitzen",
        "so",
        "sofort",
        "Sohn",
        "sollen",
        "Sommer",
        "Sonne",
        "Sonntag",
        "sonst",
        "Spaß",
        "Spiel",
        "spielen",
        "sprechen",
        "springen",
        "spät",
        "später",
        "Stadt",
        "stark",
        "stehen",
        "steigen",
        "Stein",
        "Stelle",
        "stellen",
        "Straße",
        "Stunde",
        "Stück",
        "suchen",
        "Tag",
        "Tante",
        "Teller",
        "tief",
        "Tier",
        "Tisch",
        "tot",
        "tragen",
        "traurig",
        "treffen",
        "trinken",
        "tun",
        "turnen",
        "Tür",
        "Uhr",
        "um",
        "und",
        "uns",
        "unser",
        "unten",
        "unter",
        "Vater",
        "vergessen",
        "verkaufen",
        "verlieren",
        "verstecken",
        "verstehen",
        "versuchen",
        "viel",
        "vielleicht",
        "vier",
        "Vogel",
        "voll",
        "vom",
        "von",
        "vor",
        "vorbei",
        "Wagen",
        "wahr",
        "Wald",
        "war",
        "warm",
        "warten",
        "warum",
        "was",
        "waschen",
        "Wasser",
        "weg",
        "Weg",
        "Weihnachten",
        "weil",
        "weinen",
        "weit",
        "weiter",
        "weiß",
        "Welt",
        "wenig",
        "wenn",
        "wer",
        "werden",
        "werfen",
        "Wetter",
        "wichtig",
        "wie",
        "wieder",
        "Wiese",
        "will",
        "Winter",
        "wir",
        "wird",
        "wirklich",
        "wissen",
        "Wissen",
        "wo",
        "Woche",
        "wohl",
        "wohnen",
        "Wohnung",
        "wollen",
        "Wort",
        "wünschen",
        "Zahl",
        "zehn",
        "zeigen",
        "Zeit",
        "Zeitung",
        "ziehen",
        "Zimmer",
        "zu",
        "Zug",
        "zum",
        "zur",
        "zurück",
        "zusammen",
        "zwei",
        "zwischen",
        "öffnen",
        "über",
        "überall",
    )

    parts_of_speech: Dict[str, tuple] = {}
