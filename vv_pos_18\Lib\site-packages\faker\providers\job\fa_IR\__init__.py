from .. import Provider as BaseProvider


class Provider(BaseProvider):
    jobs = [
        "هنر‌پیشه",
        "ناخدا",
        "بخشدار",
        "خیاط",
        "گله‌دار",
        "باغ‌دار",
        "مؤذن",
        "ساربان",
        "آش<PERSON>ز",
        "دندان‌پزشک",
        "نجار",
        "چوپان",
        "خانه‌دار",
        "شورا",
        "نویسنده",
        "گارسون",
        "استاد",
        "فروشنده",
        "شیشه‌ساز",
        "مدیر",
        "نقاش ساختمان",
        "قایقران",
        "رفتگر",
        "وزیر",
        "خلبان",
        "آرایشگر",
        "روحانی",
        "متخصص",
        "فوتبالیست",
        "قصاب",
        "ساعت‌ساز",
        "بقال",
        "تلفن‌چی",
        "تاجر",
        "عینک‌ساز",
        "خوشنویس",
        "جنگلبان",
        "معلم",
        "مهندس",
        "راننده",
        "آذین گر",
        "نظامی",
        "نانوا",
        "فرماندار",
        "دانش‌آموز",
        "دانشجو",
        "تعمیرکار",
        "کشاورز",
        "هنرمند",
        "معاون",
        "بانکدار",
        "آهنگر",
        "رئیس",
        "سرتیپ",
        "سرایدار",
        "کارمند",
        "مربی",
        "سرهنگ",
        "غواص",
        "پزشک",
        "دربان",
        "آتش‌نشان",
        "ماهی‌گیر",
        "میوه‌فروش",
        "نگهبان",
        "پاسدار",
        "قاضی",
        "وکیل",
        "کارگر",
        "شهردار",
        "معدن‌چی",
        "پرستار",
        "افسر",
        "عکاس",
        "لوله‌کش",
        "بازیگر",
        "باربر",
        "رئیس‌جمهور",
        "نخست‌وزیر",
        "روانشناس",
        "خبر‌نگار",
        "بازنشسته",
        "مجسمه‌ساز",
        "گروهبان",
        "مغازه‌دار",
        "خواننده",
        "سرباز",
        "سخن‌ران",
        "جراح",
        "سفال‌گر",
        "جهانگرد",
        "جوشکار",
        "چشم‌پزشک",
        "گزارش‌گر",
        "خطاط",
    ]
