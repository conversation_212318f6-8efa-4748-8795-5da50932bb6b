// Système POS pour l'approvisionnement de stock
const StockReplenishmentPOS = {
    cart: [],
    currentQuantity: '0',
    selectedSupplier: null,
    currentCategory: 'all',
    currentMainFilter: 'all', // 'all', 'products', 'ingredients'
    currentSubCategory: null,
    currentPage: 1,
    allItems: [],
    discount: {
        amount: 0,
        type: 'amount', // 'amount' ou 'percentage'
        applyToItems: false
    },
    editingItem: null,

    init: function() {
        this.loadItems();
        this.initEventListeners();
        this.updateDisplay();
        this.initAnimations();

        // Initialiser l'état des boutons
        document.getElementById('discountButton').disabled = true;

        // Initialiser l'affichage par défaut (tous les articles)
        this.showMainFilter('all');
    },

    initEventListeners: function() {
        // Numpad
        document.querySelectorAll('.numpad-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const value = btn.dataset.value;
                this.handleNumpadInput(value);
            });
        });

        // Recherche
        document.getElementById('searchInput').addEventListener('input', (e) => {
            this.searchItems(e.target.value);
        });

        // Sélecteurs de fournisseurs dans la modale
        document.getElementById('modalSupplierCategorySelect').addEventListener('change', (e) => {
            this.loadSuppliersByCategory(e.target.value, true);
        });

        document.getElementById('modalSupplierSelect').addEventListener('change', (e) => {
            // Pas d'action immédiate, on attend l'application
        });

        // Méthodes de paiement
        document.querySelectorAll('.payment-method-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.selectPaymentMethod(btn.dataset.method);
            });
        });

        // Confirmation de paiement
        document.getElementById('confirmPaymentBtn').addEventListener('click', () => {
            this.processPayment();
        });

        // Gestion des remises
        document.querySelectorAll('input[name="discountType"]').forEach(radio => {
            radio.addEventListener('change', () => {
                this.updateDiscountUnit();
                this.calculateDiscount();
            });
        });

        document.getElementById('discountValue').addEventListener('input', () => {
            this.calculateDiscount();
        });

        document.getElementById('confirmDiscount').addEventListener('click', () => {
            this.applyDiscount();
        });

        // Gestion de la modification de prix
        document.getElementById('confirmPriceChange').addEventListener('click', () => {
            this.confirmPriceChange();
        });
    },

    loadItems: function() {
        if (window.stockReplenishmentData) {
            const products = window.stockReplenishmentData.products.map(p => ({...p, type: 'product'}));
            const ingredients = window.stockReplenishmentData.ingredients.map(i => ({...i, type: 'ingredient'}));
            this.allItems = [...products, ...ingredients];
        }
        this.displayItems(this.allItems);
    },

    displayItems: function(items) {
        const grid = document.getElementById('itemsGrid');

        if (items.length === 0) {
            grid.innerHTML = `
                <div class="col-12 text-center text-muted py-4">
                    <i class="fas fa-search fa-2x mb-2"></i>
                    <p>Aucun article trouvé</p>
                </div>
            `;
            this.updatePagination(0, 0);
            return;
        }

        // Pagination - 25 éléments par page (5x5)
        const itemsPerPage = 25;
        const totalPages = Math.ceil(items.length / itemsPerPage);
        const startIndex = (this.currentPage - 1) * itemsPerPage;
        const endIndex = startIndex + itemsPerPage;
        const paginatedItems = items.slice(startIndex, endIndex);

        grid.innerHTML = paginatedItems.map(item => `
            <div class="item-card" onclick="StockReplenishmentPOS.selectItem(${item.id}, '${item.type}')">
                <img src="${item.image_path}"
                     alt="${item.name}"
                     onerror="this.src='/static/images/default-${item.type}.svg'"
                     loading="lazy">
                <div class="item-name" title="${item.name}">${item.name}</div>
                <div class="item-price">${item.price.toFixed(2)} €</div>
                <div class="item-stock">${item.stock_quantity} ${item.unit}</div>
            </div>
        `).join('');

        this.updatePagination(this.currentPage, totalPages);
    },

    updatePagination: function(currentPage, totalPages) {
        const pagination = document.getElementById('itemsPagination');
        if (!pagination) return;

        if (totalPages <= 1) {
            pagination.innerHTML = '';
            return;
        }

        let paginationHTML = '';

        // Bouton précédent
        if (currentPage > 1) {
            paginationHTML += `
                <li class="page-item">
                    <a class="page-link" href="#" onclick="StockReplenishmentPOS.goToPage(${currentPage - 1})">
                        <i class="fas fa-chevron-left"></i>
                    </a>
                </li>
            `;
        }

        // Pages
        const startPage = Math.max(1, currentPage - 2);
        const endPage = Math.min(totalPages, currentPage + 2);

        for (let i = startPage; i <= endPage; i++) {
            paginationHTML += `
                <li class="page-item ${i === currentPage ? 'active' : ''}">
                    <a class="page-link" href="#" onclick="StockReplenishmentPOS.goToPage(${i})">${i}</a>
                </li>
            `;
        }

        // Bouton suivant
        if (currentPage < totalPages) {
            paginationHTML += `
                <li class="page-item">
                    <a class="page-link" href="#" onclick="StockReplenishmentPOS.goToPage(${currentPage + 1})">
                        <i class="fas fa-chevron-right"></i>
                    </a>
                </li>
            `;
        }

        pagination.innerHTML = paginationHTML;
    },

    goToPage: function(page) {
        this.currentPage = page;
        this.filterItems();
    },

    handleNumpadInput: function(value) {
        if (value === 'clear') {
            this.currentQuantity = '0';
        } else if (value === 'backspace') {
            this.currentQuantity = this.currentQuantity.length > 1 ? 
                this.currentQuantity.slice(0, -1) : '0';
        } else if (value === '.' && !this.currentQuantity.includes('.')) {
            this.currentQuantity += '.';
        } else if (value !== '.') {
            if (this.currentQuantity === '0') {
                this.currentQuantity = value;
            } else {
                this.currentQuantity += value;
            }
        }
        
        document.getElementById('numpadDisplay').textContent = this.currentQuantity;
    },

    selectItem: function(itemId, itemType) {
        const quantity = parseFloat(this.currentQuantity) || 0;
        
        if (quantity <= 0) {
            alert('Veuillez saisir une quantité valide');
            return;
        }

        const item = this.allItems.find(i => i.id === itemId && i.type === itemType);
        if (!item) return;

        // Vérifier si l'article est déjà dans le panier
        const existingIndex = this.cart.findIndex(cartItem => 
            cartItem.id === itemId && cartItem.type === itemType
        );

        if (existingIndex >= 0) {
            // Mettre à jour la quantité
            this.cart[existingIndex].quantity += quantity;
            this.cart[existingIndex].total = this.cart[existingIndex].quantity * this.cart[existingIndex].price;
        } else {
            // Ajouter nouvel article
            this.cart.push({
                id: itemId,
                type: itemType,
                name: item.name,
                price: item.price,
                quantity: quantity,
                unit: item.unit,
                total: quantity * item.price
            });
        }

        // Réinitialiser la quantité
        this.currentQuantity = '0';
        document.getElementById('numpadDisplay').textContent = '0';
        
        this.updateTicket();
    },

    updateTicket: function() {
        const ticketItems = document.getElementById('ticketItems');
        const totalAmount = document.getElementById('totalAmount');
        const payButton = document.getElementById('payButton');

        if (this.cart.length === 0) {
            ticketItems.innerHTML = `
                <div class="text-center text-muted py-4">
                    <i class="fas fa-shopping-cart fa-2x mb-2"></i>
                    <p>Aucun article sélectionné</p>
                </div>
            `;
            totalAmount.textContent = '0.00 €';
            payButton.disabled = true;
            document.getElementById('discountButton').disabled = true;
            return;
        }

        const itemsHtml = this.cart.map((item, index) => `
            <div class="ticket-item">
                <div class="flex-grow-1">
                    <div class="fw-bold">${item.name}</div>
                    <small class="text-muted">${item.quantity} ${item.unit} × ${item.price.toFixed(2)} €</small>
                    ${item.originalPrice && item.originalPrice !== item.price ?
                        `<small class="text-warning"><i class="fas fa-edit"></i> Prix modifié (${item.originalPrice.toFixed(2)} €)</small>` : ''}
                </div>
                <div class="text-end">
                    <div class="fw-bold">${item.total.toFixed(2)} €</div>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary"
                                onclick="StockReplenishmentPOS.editItemPrice(${index})"
                                title="Modifier le prix">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-outline-danger"
                                onclick="StockReplenishmentPOS.removeItem(${index})"
                                title="Supprimer">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        `).join('');

        ticketItems.innerHTML = itemsHtml;

        // Calcul des totaux avec remise
        const subtotal = this.cart.reduce((sum, item) => sum + item.total, 0);
        let discountAmount = 0;

        if (this.discount.amount > 0) {
            if (this.discount.type === 'percentage') {
                discountAmount = subtotal * (this.discount.amount / 100);
            } else {
                discountAmount = Math.min(this.discount.amount, subtotal);
            }
        }

        const total = subtotal - discountAmount;

        // Mise à jour de l'affichage
        document.getElementById('subtotalAmount').textContent = subtotal.toFixed(2) + ' €';

        if (discountAmount > 0) {
            document.getElementById('discountRow').style.display = 'flex';
            document.getElementById('discountAmount').textContent = '-' + discountAmount.toFixed(2) + ' €';
        } else {
            document.getElementById('discountRow').style.display = 'none';
        }

        totalAmount.textContent = total.toFixed(2) + ' €';
        payButton.disabled = false;
        document.getElementById('discountButton').disabled = false;
    },

    removeItem: function(index) {
        this.cart.splice(index, 1);
        this.updateTicket();
    },

    clearCart: function() {
        this.cart = [];
        this.discount = { amount: 0, type: 'amount', applyToItems: false };
        this.updateTicket();
    },

    clearDiscount: function() {
        this.resetIndividualDiscounts();
        this.discount = { amount: 0, type: 'amount', applyToItems: false };
        this.updateTicket();
    },

    showMainFilter: function(filter) {
        this.currentMainFilter = filter;
        this.currentSubCategory = null;

        // Mettre à jour les boutons actifs du filtre principal
        document.querySelectorAll('.main-filter-buttons .btn').forEach(btn => {
            btn.classList.remove('active');
        });

        let buttonId = 'filterAll';
        if (filter === 'products') buttonId = 'filterProducts';
        else if (filter === 'ingredients') buttonId = 'filterIngredients';

        document.getElementById(buttonId).classList.add('active');

        // Afficher/masquer les boutons de catégories
        this.updateCategoryButtons(filter);

        // Filtrer les articles
        this.filterItems();
    },

    updateCategoryButtons: function(filter) {
        const categoryButtonsContainer = document.getElementById('categoryButtons');
        const categoryButtonsContent = categoryButtonsContainer.querySelector('.category-buttons-container');

        if (filter === 'all') {
            categoryButtonsContainer.style.display = 'none';
        } else {
            categoryButtonsContainer.style.display = 'block';

            let categories = [];
            if (filter === 'products') {
                categories = window.stockReplenishmentData.productCategories || [];
            } else if (filter === 'ingredients') {
                categories = window.stockReplenishmentData.ingredientCategories || [];
            }

            // Créer les boutons de catégories
            let buttonsHTML = '<button class="category-btn active" onclick="StockReplenishmentPOS.selectSubCategory(null)">Tous</button>';
            categories.forEach(category => {
                buttonsHTML += `<button class="category-btn" onclick="StockReplenishmentPOS.selectSubCategory(${category.id})">${category.name}</button>`;
            });

            categoryButtonsContent.innerHTML = buttonsHTML;
        }
    },

    selectSubCategory: function(categoryId) {
        this.currentSubCategory = categoryId;

        // Mettre à jour les boutons actifs des sous-catégories
        document.querySelectorAll('.category-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        event.target.classList.add('active');

        // Filtrer les articles
        this.filterItems();
    },

    filterItems: function() {
        let filteredItems = this.allItems;

        // Filtrer par type principal
        if (this.currentMainFilter === 'products') {
            filteredItems = filteredItems.filter(item => item.type === 'product');
        } else if (this.currentMainFilter === 'ingredients') {
            filteredItems = filteredItems.filter(item => item.type === 'ingredient');
        }

        // Filtrer par sous-catégorie
        if (this.currentSubCategory !== null) {
            filteredItems = filteredItems.filter(item => item.category_id === this.currentSubCategory);
        }

        this.displayItems(filteredItems);
    },

    // Ancienne fonction pour compatibilité
    showCategory: function(category) {
        this.showMainFilter(category);
    },

    searchItems: function(query) {
        if (!query.trim()) {
            this.filterItems();
            return;
        }

        let filtered = this.allItems;

        // Appliquer d'abord les filtres de type
        if (this.currentMainFilter === 'products') {
            filtered = filtered.filter(item => item.type === 'product');
        } else if (this.currentMainFilter === 'ingredients') {
            filtered = filtered.filter(item => item.type === 'ingredient');
        }

        // Appliquer le filtre de sous-catégorie
        if (this.currentSubCategory !== null) {
            filtered = filtered.filter(item => item.category_id === this.currentSubCategory);
        }

        // Appliquer la recherche textuelle
        filtered = filtered.filter(item =>
            item.name.toLowerCase().includes(query.toLowerCase())
        );

        this.displayItems(filtered);
    },

    loadSuppliersByCategory: function(categoryId, isModal = false) {
        const supplierSelect = document.getElementById(isModal ? 'modalSupplierSelect' : 'supplierSelect');

        if (categoryId == 0) {
            // Afficher tous les fournisseurs
            supplierSelect.innerHTML = '<option value="0">Aucun fournisseur</option>' +
                window.stockReplenishmentData.suppliers.map(s =>
                    `<option value="${s.id}">${s.name}</option>`
                ).join('');
        } else {
            // Filtrer par catégorie
            const filtered = window.stockReplenishmentData.suppliers.filter(s =>
                s.category_id == categoryId
            );
            supplierSelect.innerHTML = '<option value="0">Aucun fournisseur</option>' +
                filtered.map(s =>
                    `<option value="${s.id}">${s.name}</option>`
                ).join('');
        }
    },

    openSupplierModal: function() {
        const modal = new bootstrap.Modal(document.getElementById('supplierModal'));
        modal.show();
    },

    applySupplierSelection: function() {
        const categoryId = document.getElementById('modalSupplierCategorySelect').value;
        const supplierId = document.getElementById('modalSupplierSelect').value;

        // Appliquer la sélection
        this.selectSupplier(supplierId);

        // Mettre à jour le texte du bouton
        const supplierButton = document.getElementById('supplierButtonText');
        if (supplierId == 0) {
            supplierButton.textContent = 'Choisir fournisseur';
        } else {
            const supplier = window.stockReplenishmentData.suppliers.find(s => s.id == supplierId);
            supplierButton.textContent = supplier ? supplier.name : 'Choisir fournisseur';
        }

        // Fermer la modale
        const modal = bootstrap.Modal.getInstance(document.getElementById('supplierModal'));
        modal.hide();
    },

    selectSupplier: function(supplierId) {
        this.selectedSupplier = supplierId == 0 ? null : supplierId;
        
        const supplierInfo = document.getElementById('supplierInfo');
        if (this.selectedSupplier) {
            const supplier = window.stockReplenishmentData.suppliers.find(s => 
                s.id == this.selectedSupplier
            );
            supplierInfo.textContent = `Fournisseur: ${supplier ? supplier.name : 'Autres'}`;
        } else {
            supplierInfo.textContent = 'Fournisseur: Autres';
        }
    },

    openPaymentModal: function() {
        if (this.cart.length === 0) {
            alert('Veuillez ajouter des articles au panier avant de procéder au paiement');
            return;
        }

        // Calculer les totaux
        const subtotal = this.cart.reduce((sum, item) => sum + item.total, 0);
        let discountAmount = 0;

        if (this.discount.amount > 0) {
            if (this.discount.type === 'percentage') {
                discountAmount = subtotal * (this.discount.amount / 100);
            } else {
                discountAmount = Math.min(this.discount.amount, subtotal);
            }
        }

        const total = subtotal - discountAmount;

        // Remplir le résumé des articles
        const paymentOrderItems = document.getElementById('paymentOrderItems');
        if (paymentOrderItems) {
            const itemsHtml = this.cart.map(item => `
                <div class="d-flex justify-content-between align-items-center py-1">
                    <div class="flex-grow-1">
                        <span class="fw-medium">${item.name}</span>
                        <br><small class="text-muted">${item.quantity} × ${item.price.toFixed(2)} €</small>
                    </div>
                    <span class="fw-medium">${item.total.toFixed(2)} €</span>
                </div>
            `).join('');
            paymentOrderItems.innerHTML = itemsHtml;
        }

        // Mettre à jour les totaux
        const paymentSubtotal = document.getElementById('paymentSubtotal');
        const paymentDiscountRow = document.getElementById('paymentDiscountRow');
        const paymentDiscount = document.getElementById('paymentDiscount');
        const paymentTotal = document.getElementById('paymentTotal');

        if (paymentSubtotal) paymentSubtotal.textContent = subtotal.toFixed(2) + ' €';
        if (paymentTotal) paymentTotal.textContent = total.toFixed(2) + ' €';

        // Afficher/masquer la ligne de remise
        if (discountAmount > 0 && paymentDiscountRow && paymentDiscount) {
            paymentDiscountRow.style.display = 'flex';
            paymentDiscount.textContent = '-' + discountAmount.toFixed(2) + ' €';
        } else if (paymentDiscountRow) {
            paymentDiscountRow.style.display = 'none';
        }

        const modal = new bootstrap.Modal(document.getElementById('paymentModal'));
        modal.show();
    },

    selectPaymentMethod: function(method) {
        // Réinitialiser les boutons
        document.querySelectorAll('.payment-method-btn').forEach(btn => {
            btn.classList.remove('btn-primary');
            btn.classList.add('btn-outline-primary');
        });

        // Activer le bouton sélectionné
        event.target.classList.remove('btn-outline-primary');
        event.target.classList.add('btn-primary');

        this.selectedPaymentMethod = method;
        document.getElementById('confirmPaymentBtn').disabled = false;
    },

    processPayment: function() {
        if (!this.selectedSupplier) {
            alert('Veuillez sélectionner un fournisseur');
            return;
        }

        if (!this.selectedPaymentMethod) {
            alert('Veuillez sélectionner une méthode de paiement');
            return;
        }

        const processingType = document.querySelector('input[name="processingType"]:checked').value;
        const subtotal = this.cart.reduce((sum, item) => sum + item.total, 0);

        // Calculer la remise
        let discountAmount = 0;
        if (this.discount.amount > 0) {
            if (this.discount.type === 'percentage') {
                discountAmount = subtotal * (this.discount.amount / 100);
            } else {
                discountAmount = Math.min(this.discount.amount, subtotal);
            }
        }

        const total = subtotal - discountAmount;

        const orderData = {
            supplier_id: this.selectedSupplier,
            cart_items: this.cart.map(item => ({
                type: item.type,
                product_id: item.type === 'product' ? item.id : null,
                ingredient_id: item.type === 'ingredient' ? item.id : null,
                quantity: item.quantity,
                unit_price: item.price,
                original_unit_price: item.originalPrice || item.price,
                total_price: item.total,
                price_change_reason: item.priceChangeReason || null
            })),
            subtotal: subtotal,
            discount: {
                amount: this.discount.amount,
                type: this.discount.type,
                apply_to_items: this.discount.applyToItems
            },
            total_amount: total,
            processing_type: processingType,
            payment_method: this.selectedPaymentMethod,
            notes: ''
        };

        // Envoyer la commande
        fetch('/inventory/stock-replenishment/process-order', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').content
            },
            body: JSON.stringify(orderData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Commande traitée avec succès!');
                this.clearCart();
                bootstrap.Modal.getInstance(document.getElementById('paymentModal')).hide();
                
                // Rediriger vers les détails de la commande
                if (data.purchase_order_id) {
                    window.location.href = `/inventory/stock-replenishment/orders/${data.purchase_order_id}`;
                }
            } else {
                alert('Erreur: ' + (data.error || 'Erreur inconnue'));
            }
        })
        .catch(error => {
            console.error('Erreur:', error);
            alert('Erreur de connexion');
        });
    },

    updateDisplay: function() {
        this.updateTicket();
    },

    initAnimations: function() {
        // Ajouter des animations aux éléments au chargement
        setTimeout(() => {
            const itemCards = document.querySelectorAll('.item-card');
            itemCards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(20px)';
                    card.style.transition = 'all 0.3s ease';

                    setTimeout(() => {
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }, 50);
                }, index * 50);
            });
        }, 100);

        // Animation pour le numpad
        const numpadBtns = document.querySelectorAll('.numpad-btn');
        numpadBtns.forEach((btn, index) => {
            setTimeout(() => {
                btn.style.opacity = '0';
                btn.style.transform = 'scale(0.8)';
                btn.style.transition = 'all 0.3s ease';

                setTimeout(() => {
                    btn.style.opacity = '1';
                    btn.style.transform = 'scale(1)';
                }, 50);
            }, index * 100);
        });

        // Animation pour le ticket
        setTimeout(() => {
            const ticket = document.querySelector('.purchase-ticket');
            if (ticket) {
                ticket.style.opacity = '0';
                ticket.style.transform = 'translateX(20px)';
                ticket.style.transition = 'all 0.5s ease';

                setTimeout(() => {
                    ticket.style.opacity = '1';
                    ticket.style.transform = 'translateX(0)';
                }, 100);
            }
        }, 300);
    },

    // Nouvelles méthodes pour la gestion des prix et remises
    editItemPrice: function(index) {
        const item = this.cart[index];
        if (!item) return;

        this.editingItem = { index, item };

        // Remplir la modal
        document.getElementById('editItemName').textContent = item.name;
        document.getElementById('editReferencePrice').textContent = (item.originalPrice || item.price).toFixed(2) + ' €';
        document.getElementById('editCurrentPrice').textContent = item.price.toFixed(2) + ' €';
        document.getElementById('newPrice').value = item.price.toFixed(2);

        // Ouvrir la modal
        const modal = new bootstrap.Modal(document.getElementById('priceEditModal'));
        modal.show();
    },

    confirmPriceChange: function() {
        if (!this.editingItem) return;

        const newPrice = parseFloat(document.getElementById('newPrice').value);
        const reason = document.getElementById('priceChangeReason').value;

        if (isNaN(newPrice) || newPrice < 0) {
            alert('Veuillez entrer un prix valide');
            return;
        }

        const { index, item } = this.editingItem;

        // Sauvegarder le prix de référence initial si ce n'est pas déjà fait
        if (!item.referencePrice) {
            item.referencePrice = item.originalPrice || item.price;
        }

        // Sauvegarder le prix original (avant modification manuelle) si ce n'est pas déjà fait
        if (!item.originalPrice) {
            item.originalPrice = item.price;
        }

        // Mettre à jour le prix
        this.cart[index].price = newPrice;
        this.cart[index].total = this.cart[index].quantity * newPrice;
        this.cart[index].priceChangeReason = reason;
        this.cart[index].hasManualPriceChange = true;

        // Mettre à jour l'affichage
        this.updateTicket();

        // Fermer la modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('priceEditModal'));
        modal.hide();

        this.editingItem = null;
    },

    openDiscountModal: function() {
        // Vérifier qu'il y a des articles dans le panier
        if (this.cart.length === 0) {
            alert('Veuillez ajouter des articles au panier avant d\'appliquer une remise');
            return;
        }

        // Calculer le sous-total en utilisant les prix actuels (possiblement modifiés)
        // Si des remises individuelles sont appliquées, utiliser les prix avant remise
        let subtotal = 0;
        this.cart.forEach(item => {
            if (item.hasIndividualDiscount && item.priceBeforeDiscount) {
                subtotal += item.quantity * item.priceBeforeDiscount;
            } else {
                subtotal += item.total;
            }
        });

        // Attendre que la modal soit dans le DOM
        setTimeout(() => {
            const currentSubtotalElement = document.getElementById('currentSubtotal');
            if (!currentSubtotalElement) {
                console.error('Element currentSubtotal not found');
                return;
            }
            currentSubtotalElement.textContent = subtotal.toFixed(2) + ' €';

            // Réinitialiser les valeurs
            const discountValueElement = document.getElementById('discountValue');
            const calculatedDiscountElement = document.getElementById('calculatedDiscount');
            const newTotalElement = document.getElementById('newTotal');

            if (discountValueElement) discountValueElement.value = '';
            if (calculatedDiscountElement) calculatedDiscountElement.textContent = '0.00 €';
            if (newTotalElement) newTotalElement.textContent = subtotal.toFixed(2) + ' €';
        }, 100);

        const modal = new bootstrap.Modal(document.getElementById('discountModal'));
        modal.show();
    },

    updateDiscountUnit: function() {
        const discountType = document.querySelector('input[name="discountType"]:checked').value;
        const unit = document.getElementById('discountUnit');
        unit.textContent = discountType === 'percentage' ? '%' : '€';
    },

    updateDiscountUnit: function() {
        const discountType = document.querySelector('input[name="discountType"]:checked').value;
        const unitElement = document.getElementById('discountUnit');
        if (unitElement) {
            unitElement.textContent = discountType === 'percentage' ? '%' : '€';
        }
    },

    calculateDiscount: function() {
        // Calculer le sous-total en utilisant les prix actuels (possiblement modifiés)
        // Si des remises individuelles sont appliquées, utiliser les prix avant remise
        let subtotal = 0;
        this.cart.forEach(item => {
            if (item.hasIndividualDiscount && item.priceBeforeDiscount) {
                subtotal += item.quantity * item.priceBeforeDiscount;
            } else {
                subtotal += item.total;
            }
        });

        const discountValue = parseFloat(document.getElementById('discountValue').value) || 0;
        const discountType = document.querySelector('input[name="discountType"]:checked').value;

        let discountAmount = 0;
        if (discountValue > 0) {
            if (discountType === 'percentage') {
                discountAmount = subtotal * (discountValue / 100);
            } else {
                discountAmount = Math.min(discountValue, subtotal);
            }
        }

        const newTotal = subtotal - discountAmount;

        const calculatedDiscountElement = document.getElementById('calculatedDiscount');
        const newTotalElement = document.getElementById('newTotal');

        if (calculatedDiscountElement) calculatedDiscountElement.textContent = discountAmount.toFixed(2) + ' €';
        if (newTotalElement) newTotalElement.textContent = newTotal.toFixed(2) + ' €';
    },

    applyDiscount: function() {
        const discountValue = parseFloat(document.getElementById('discountValue').value) || 0;
        const discountType = document.querySelector('input[name="discountType"]:checked').value;
        const applyToItems = document.getElementById('applyToItems').checked;

        if (discountValue <= 0) {
            alert('Veuillez entrer une valeur de remise valide');
            return;
        }

        // Réinitialiser les remises individuelles précédentes
        this.resetIndividualDiscounts();

        this.discount = {
            amount: discountValue,
            type: discountType,
            applyToItems: applyToItems
        };

        // Si on applique aux articles individuels
        if (applyToItems) {
            this.applyDiscountToItems();
        }

        // Mettre à jour l'affichage
        this.updateTicket();

        // Fermer la modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('discountModal'));
        modal.hide();
    },

    resetIndividualDiscounts: function() {
        this.cart.forEach(item => {
            if (item.hasIndividualDiscount) {
                // Restaurer le prix avant remise (qui peut être un prix modifié manuellement)
                if (item.priceBeforeDiscount) {
                    item.price = item.priceBeforeDiscount;
                    item.total = item.quantity * item.price;
                }

                // Nettoyer les marqueurs de remise individuelle
                delete item.hasIndividualDiscount;
                delete item.discountAmount;
                delete item.priceBeforeDiscount;
            }
        });
    },

    applyDiscountToItems: function() {
        const subtotal = this.cart.reduce((sum, item) => sum + item.total, 0);
        let totalDiscountAmount = 0;

        if (this.discount.type === 'percentage') {
            totalDiscountAmount = subtotal * (this.discount.amount / 100);
        } else {
            totalDiscountAmount = Math.min(this.discount.amount, subtotal);
        }

        // Répartir la remise proportionnellement
        this.cart.forEach(item => {
            // Sauvegarder le prix de référence initial si ce n'est pas déjà fait
            if (!item.referencePrice) {
                item.referencePrice = item.originalPrice || item.price;
            }

            // Sauvegarder le prix avant application de remise (prix actuel, possiblement modifié)
            if (!item.priceBeforeDiscount) {
                item.priceBeforeDiscount = item.price;
            }

            const itemProportion = item.total / subtotal;
            const itemDiscount = totalDiscountAmount * itemProportion;
            const itemDiscountPerUnit = itemDiscount / item.quantity;

            // Appliquer la remise sur le prix actuel (pas le prix de référence)
            item.price = Math.max(0, item.priceBeforeDiscount - itemDiscountPerUnit);
            item.total = item.quantity * item.price;

            // Marquer que cet article a une remise appliquée
            item.hasIndividualDiscount = true;
            item.discountAmount = itemDiscount;
        });

        // Réinitialiser la remise globale puisqu'elle est maintenant appliquée aux articles
        this.discount = { amount: 0, type: 'amount', applyToItems: false };
    }
};

// Fonctions globales pour les événements onclick
function clearCart() {
    StockReplenishmentPOS.clearCart();
}

function showCategory(category) {
    StockReplenishmentPOS.showCategory(category);
}

function showMainFilter(filter) {
    StockReplenishmentPOS.showMainFilter(filter);
}

function openDiscountModal() {
    StockReplenishmentPOS.openDiscountModal();
}

function openSupplierModal() {
    StockReplenishmentPOS.openSupplierModal();
}

function applySupplierSelection() {
    StockReplenishmentPOS.applySupplierSelection();
}

function openPaymentModal() {
    StockReplenishmentPOS.openPaymentModal();
}

// Initialisation au chargement de la page
document.addEventListener('DOMContentLoaded', function() {
    StockReplenishmentPOS.init();

    // Event listeners pour les modales de remise
    document.querySelectorAll('input[name="discountType"]').forEach(radio => {
        radio.addEventListener('change', () => {
            StockReplenishmentPOS.updateDiscountUnit();
            StockReplenishmentPOS.calculateDiscount();
        });
    });

    const discountValueInput = document.getElementById('discountValue');
    if (discountValueInput) {
        discountValueInput.addEventListener('input', () => {
            StockReplenishmentPOS.calculateDiscount();
        });
    }

    // Event listener pour le bouton de confirmation de remise
    const confirmDiscountButton = document.getElementById('confirmDiscount');
    if (confirmDiscountButton) {
        confirmDiscountButton.addEventListener('click', () => {
            StockReplenishmentPOS.applyDiscount();
        });
    }

    const confirmDiscountBtn = document.getElementById('confirmDiscount');
    if (confirmDiscountBtn) {
        confirmDiscountBtn.addEventListener('click', () => {
            StockReplenishmentPOS.applyDiscount();
        });
    }

    const confirmPaymentBtn = document.getElementById('confirmPaymentBtn');
    if (confirmPaymentBtn) {
        confirmPaymentBtn.addEventListener('click', () => {
            StockReplenishmentPOS.processPayment();
        });
    }
});
