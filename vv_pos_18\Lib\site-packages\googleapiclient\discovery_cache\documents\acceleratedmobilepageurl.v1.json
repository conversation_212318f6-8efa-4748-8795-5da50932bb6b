{"basePath": "", "baseUrl": "https://acceleratedmobilepageurl.googleapis.com/", "batchPath": "batch", "canonicalName": "Acceleratedmobilepageurl", "description": "Retrieves the list of AMP URLs (and equivalent AMP Cache URLs) for a given list of public URL(s). ", "discoveryVersion": "v1", "documentationLink": "https://developers.google.com/amp/cache/", "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "acceleratedmobilepageurl:v1", "kind": "discovery#restDescription", "mtlsRootUrl": "https://acceleratedmobilepageurl.mtls.googleapis.com/", "name": "acceleratedmobilepageurl", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"ampUrls": {"methods": {"batchGet": {"description": "Returns AMP URL(s) and equivalent [AMP Cache URL(s)](/amp/cache/overview#amp-cache-url-format).", "flatPath": "v1/ampUrls:batchGet", "httpMethod": "POST", "id": "acceleratedmobilepageurl.ampUrls.batchGet", "parameterOrder": [], "parameters": {}, "path": "v1/ampUrls:batchGet", "request": {"$ref": "BatchGetAmpUrlsRequest"}, "response": {"$ref": "BatchGetAmpUrlsResponse"}}}}}, "revision": "20240707", "rootUrl": "https://acceleratedmobilepageurl.googleapis.com/", "schemas": {"AmpUrl": {"description": "AMP URL response for a requested URL.", "id": "AmpUrl", "properties": {"ampUrl": {"description": "The AMP URL pointing to the publisher's web server.", "type": "string"}, "cdnAmpUrl": {"description": "The [AMP Cache URL](/amp/cache/overview#amp-cache-url-format) pointing to the cached document in the Google AMP Cache.", "type": "string"}, "originalUrl": {"description": "The original non-AMP URL.", "type": "string"}}, "type": "object"}, "AmpUrlError": {"description": "AMP URL Error resource for a requested URL that couldn't be found.", "id": "AmpUrlError", "properties": {"errorCode": {"description": "The error code of an API call.", "enum": ["ERROR_CODE_UNSPECIFIED", "INPUT_URL_NOT_FOUND", "NO_AMP_URL", "APPLICATION_ERROR", "URL_IS_VALID_AMP", "URL_IS_INVALID_AMP"], "enumDeprecated": [false, false, false, false, true, false], "enumDescriptions": ["Not specified error.", "Indicates the requested URL is not found in the index, possibly because it's unable to be found, not able to be accessed by Googlebot, or some other error.", "Indicates no AMP URL has been found that corresponds to the requested URL.", "Indicates some kind of application error occurred at the server. Client advised to retry.", "DEPRECATED: Indicates the requested URL is a valid AMP URL. This is a non-error state, should not be relied upon as a sign of success or failure. It will be removed in future versions of the API.", "Indicates that an AMP URL has been found that corresponds to the request URL, but it is not valid AMP HTML."], "type": "string"}, "errorMessage": {"description": "An optional descriptive error message.", "type": "string"}, "originalUrl": {"description": "The original non-AMP URL.", "type": "string"}}, "type": "object"}, "BatchGetAmpUrlsRequest": {"description": "AMP URL request for a batch of URLs.", "id": "BatchGetAmpUrlsRequest", "properties": {"lookupStrategy": {"description": "The lookup_strategy being requested.", "enum": ["FETCH_LIVE_DOC", "IN_INDEX_DOC"], "enumDescriptions": ["FETCH_LIVE_DOC strategy involves live document fetch of URLs not found in the index. Any request URL not found in the index is crawled in realtime to validate if there is a corresponding AMP URL. This strategy has higher coverage but with extra latency introduced by realtime crawling. This is the default strategy. Applications using this strategy should set higher HTTP timeouts of the API calls.", "IN_INDEX_DOC strategy skips fetching live documents of URL(s) not found in index. For applications which need low latency use of IN_INDEX_DOC strategy is recommended."], "type": "string"}, "urls": {"description": "List of URLs to look up for the paired AMP URLs. The URLs are case-sensitive. Up to 50 URLs per lookup (see [Usage Limits](/amp/cache/reference/limits)).", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "BatchGetAmpUrlsResponse": {"description": "Batch AMP URL response.", "id": "BatchGetAmpUrlsResponse", "properties": {"ampUrls": {"description": "For each URL in BatchAmpUrlsRequest, the URL response. The response might not be in the same order as URLs in the batch request. If BatchAmpUrlsRequest contains duplicate URLs, AmpUrl is generated only once.", "items": {"$ref": "AmpUrl"}, "type": "array"}, "urlErrors": {"description": "The errors for requested URLs that have no AMP URL.", "items": {"$ref": "AmpUrlError"}, "type": "array"}}, "type": "object"}}, "servicePath": "", "title": "Accelerated Mobile Pages (AMP) URL API", "version": "v1", "version_module": true}